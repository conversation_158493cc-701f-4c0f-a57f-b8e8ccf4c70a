// Practice Questions by Category
const practiceData = {
  1: [ // 役的判断
    {
      id: 'p1_1',
      text: "手牌：[2][3][4]万 [5][6][7]筒 [1][2][3]索 [7][8][9]万 [4][4]筒，门清听[3][6]筒。有什么役？",
      options: ["立直", "平和", "断幺九", "以上都可能"],
      answer: 3,
      explain: "可立直获得1番；四面子皆顺子+雀头非役牌+两面和=平和1番；无1/9/字牌=断幺九1番。"
    },
    {
      id: 'p1_2',
      text: "副露后手牌全是2-8数牌，无字牌。有什么役？",
      options: ["断幺九", "平和", "无役", "立直"],
      answer: 0,
      explain: "断幺九不需门清，副露也成立。平和和立直都需要门清。"
    },
    {
      id: 'p1_3',
      text: "门清手牌有两组[2][3][4]万，这是什么役？",
      options: ["二盃口", "一盃口", "平和", "断幺九"],
      answer: 1,
      explain: "两组完全相同的顺子=一盃口1番（门清限定）。"
    }
  ],
  2: [ // 振听识别
    {
      id: 'p2_1',
      text: "你听[4][7]万，舍牌河有[4]万。上家打出[7]万，能荣和吗？",
      options: ["能荣和", "不能荣和", "看情况"],
      answer: 1,
      explain: "舍张振听：你的舍牌包含和张4万，所以对所有和张（4万和7万）都不能荣和。"
    },
    {
      id: 'p2_2',
      text: "立直听牌，本巡内你选择不和一张牌，同巡内其他人再打相同牌？",
      options: ["可以荣和", "不能荣和", "只能自摸"],
      answer: 1,
      explain: "同巡振听：本巡内放过和张后，本巡内不能再荣和任何牌，下一巡解除。"
    },
    {
      id: 'p2_3',
      text: "立直后摸牌改变了待牌，新的舍牌河包含新和张，怎么办？",
      options: ["可以荣和", "只能自摸", "可以取消立直"],
      answer: 1,
      explain: "立直振听：立直后出现的舍张振听持续到和牌或流局，不能荣和任何牌。"
    }
  ],
  3: [ // 平和判定
    {
      id: 'p3_1',
      text: "[1][2][3]万 [4][5][6]筒 [7][8][9]索 [2][3][4]万 [5][5]筒，听[1][4][7]筒，是平和吗？",
      options: ["是", "否"],
      answer: 1,
      explain: "听多面不是两面和牌，不符合平和条件。平和要求严格的两面和牌。"
    },
    {
      id: 'p3_2',
      text: "[2][3][4]万 [5][6][7]筒 [1][2][3]索 [7][8][9]万 [东][东]，听[东]，是平和吗？",
      options: ["是", "否"],
      answer: 1,
      explain: "单骑和牌不是两面，且雀头是字牌，不符合平和条件。"
    },
    {
      id: 'p3_3',
      text: "[2][3][4]万 [5][6][7]筒 [1][2][3]索 [6][7][8]万 [9][9]筒，门清听[5][8]筒，是平和吗？",
      options: ["是", "否"],
      answer: 0,
      explain: "四面子皆顺子，雀头9筒非役牌，两面和牌，门清，符合平和所有条件。"
    }
  ]
};

// Main Quiz Questions
const quizData = [
  {
    id: 1,
    text: "门清听两面（3万/6万），你的舍牌里有一张3万。此时他家打出3万。你能否荣和？",
    options: ["能荣和", "只能自摸", "无法和"],
    answer: 1,
    explain: "你处于舍张振听，不能对3万荣和，但仍可自摸3万或荣/自摸6万。"
  },
  {
    id: 2,
    text: "副露后的断幺九（不含1/9/字）是否依然是一番役？",
    options: ["是", "否", "只在门清时算"],
    answer: 0,
    explain: "断幺九无门清限制，副露也可成立。与立直不同。"
  },
  {
    id: 3,
    text: "立直后同巡你放过一张可荣和的牌，这一巡内是否还可以改判去荣和？",
    options: ["可以", "不可以，下一巡解除", "永久不能和"],
    answer: 1,
    explain: "这是同巡振听：当前巡内放过和张则本巡内不可再荣和，下一巡自动解除。"
  },
  {
    id: 4,
    text: "听单骑将牌，是否一定有平和？",
    options: ["是", "否"],
    answer: 1,
    explain: "平和要求四面子皆顺子、将为非役牌、且和牌为两面。单骑不满足两面和条件。"
  },
  {
    id: 5,
    text: "场风为东，你手里碰出东刻子。是否满足役牌？",
    options: ["是", "否", "要门清才算"],
    answer: 0,
    explain: "役牌（场风/门风/三元）无门清限制，碰出刻子即可成立一番。"
  },
  {
    id: 6,
    text: "手牌全是万子，包含字牌。这是什么役？",
    options: ["清一色", "混一色", "断幺九", "无役"],
    answer: 1,
    explain: "一种数牌+字牌=混一色。清一色要求仅一种数牌无字牌。"
  },
  {
    id: 7,
    text: "门清手牌：[1][1][1]万 [2][2][2]筒 [3][3][3]索 [4][4][4]万 [5][5]筒，有什么役？",
    options: ["对对和", "断幺九", "无役", "三暗刻"],
    answer: 2,
    explain: "虽然是对对和牌型，但对对和不是基础役。含1万不是断幺九。门清全刻子需要其他役。"
  },
  {
    id: 8,
    text: "立直宣言需要支付多少点棒？",
    options: ["500点", "1000点", "1500点", "不需要"],
    answer: 1,
    explain: "立直宣言需要支付1000点棒，放在桌面中央。"
  }
];

// Render Practice Questions
function renderPractice(practiceNum) {
  const root = document.getElementById(`practice${practiceNum}-root`);
  const questions = practiceData[practiceNum];

  root.innerHTML = '';
  questions.forEach((q, idx) => {
    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-item';
    questionDiv.style.marginBottom = '16px';
    questionDiv.style.padding = '12px';
    questionDiv.style.background = '#0f1419';
    questionDiv.style.borderRadius = '6px';

    const h = document.createElement('h4');
    h.textContent = `${idx + 1}. `;
    h.style.margin = '0 0 8px 0';

    const p = document.createElement('p');
    p.textContent = q.text;
    p.style.margin = '0 0 12px 0';

    const list = document.createElement('ul');
    list.style.listStyle = 'none';
    list.style.paddingLeft = '0';
    list.style.margin = '0';

    q.options.forEach((opt, i) => {
      const li = document.createElement('li');
      li.style.margin = '4px 0';
      const id = `${q.id}_opt${i}`;
      li.innerHTML = `<label style="cursor: pointer;"><input type="radio" name="${q.id}" value="${i}" id="${id}" style="margin-right: 8px;"> ${opt}</label>`;
      list.appendChild(li);
    });

    questionDiv.appendChild(h);
    questionDiv.appendChild(p);
    questionDiv.appendChild(list);
    root.appendChild(questionDiv);
  });
}

// Check Practice Answers
function checkPractice(practiceNum) {
  const questions = practiceData[practiceNum];
  let correct = 0;

  questions.forEach(q => {
    const sel = document.querySelector(`input[name="${q.id}"]:checked`);
    if (sel && Number(sel.value) === q.answer) correct++;
  });

  const result = document.getElementById(`practice${practiceNum}-result`);
  result.innerHTML = `<strong>结果：${correct}/${questions.length} 正确</strong>`;

  // Show explanations
  const root = document.getElementById(`practice${practiceNum}-root`);
  const questionItems = root.querySelectorAll('.question-item');

  questions.forEach((q, idx) => {
    let explainEl = questionItems[idx].querySelector('.explain');
    if (!explainEl) {
      explainEl = document.createElement('div');
      explainEl.className = 'tip explain';
      explainEl.style.marginTop = '12px';
      questionItems[idx].appendChild(explainEl);
    }

    const sel = document.querySelector(`input[name="${q.id}"]:checked`);
    const isCorrect = sel && Number(sel.value) === q.answer;
    const status = isCorrect ? '✅ 正确' : '❌ 错误';

    explainEl.innerHTML = `<strong>${status}</strong><br>解析：${q.explain}`;
  });
}

// Render Main Quiz
function renderQuiz() {
  const root = document.getElementById('quiz-root');
  root.innerHTML = '';

  quizData.forEach((q, idx) => {
    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-item';
    questionDiv.style.marginBottom = '20px';
    questionDiv.style.padding = '16px';
    questionDiv.style.background = '#1a1f26';
    questionDiv.style.borderRadius = '8px';
    questionDiv.style.border = '1px solid #2a2f3a';

    const h = document.createElement('h3');
    h.textContent = `题目 ${idx + 1}`;
    h.style.margin = '0 0 12px 0';
    h.style.color = '#ffffff';

    const p = document.createElement('p');
    p.textContent = q.text;
    p.style.margin = '0 0 16px 0';
    p.style.lineHeight = '1.6';

    const list = document.createElement('ul');
    list.style.listStyle = 'none';
    list.style.paddingLeft = '0';
    list.style.margin = '0';

    q.options.forEach((opt, i) => {
      const li = document.createElement('li');
      li.style.margin = '8px 0';
      const id = `q${q.id}_opt${i}`;
      li.innerHTML = `<label style="cursor: pointer; display: block; padding: 8px; background: #0f1419; border-radius: 4px; transition: background 0.2s;"><input type="radio" name="q${q.id}" value="${i}" id="${id}" style="margin-right: 12px;"> ${opt}</label>`;

      // Add hover effect
      li.addEventListener('mouseenter', () => {
        li.querySelector('label').style.background = '#1a1f26';
      });
      li.addEventListener('mouseleave', () => {
        li.querySelector('label').style.background = '#0f1419';
      });

      list.appendChild(li);
    });

    questionDiv.appendChild(h);
    questionDiv.appendChild(p);
    questionDiv.appendChild(list);
    root.appendChild(questionDiv);
  });
}

// Check Main Quiz Answers
function checkAnswers() {
  let correct = 0;

  quizData.forEach(q => {
    const sel = document.querySelector(`input[name="q${q.id}"]:checked`);
    if (sel && Number(sel.value) === q.answer) correct++;
  });

  const result = document.getElementById('quiz-result');
  const percentage = Math.round((correct / quizData.length) * 100);
  let grade = '';

  if (percentage >= 90) grade = '🏆 优秀';
  else if (percentage >= 80) grade = '🎉 良好';
  else if (percentage >= 70) grade = '👍 及格';
  else grade = '📚 需要复习';

  result.innerHTML = `<strong>成绩：${correct}/${quizData.length} 正确 (${percentage}%) - ${grade}</strong>`;

  // Show explanations
  const root = document.getElementById('quiz-root');
  const questionItems = root.querySelectorAll('.question-item');

  quizData.forEach((q, idx) => {
    let explainEl = questionItems[idx].querySelector('.explain');
    if (!explainEl) {
      explainEl = document.createElement('div');
      explainEl.className = 'tip explain';
      explainEl.style.marginTop = '16px';
      questionItems[idx].appendChild(explainEl);
    }

    const sel = document.querySelector(`input[name="q${q.id}"]:checked`);
    const isCorrect = sel && Number(sel.value) === q.answer;
    const status = isCorrect ? '✅ 正确' : '❌ 错误';

    explainEl.innerHTML = `<strong>${status}</strong><br>解析：${q.explain}`;
  });
}

// Initialize everything
window.addEventListener('DOMContentLoaded', () => {
  // Render practice sections
  for (let i = 1; i <= 3; i++) {
    renderPractice(i);
  }

  // Render main quiz
  renderQuiz();

  // Add event listeners
  document.getElementById('check-btn').addEventListener('click', checkAnswers);

  document.querySelectorAll('.practice-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const practiceNum = parseInt(e.target.dataset.practice);
      checkPractice(practiceNum);
    });
  });
});


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f0f0f0; 
        }
        .test-box { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .success { color: #28a745; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <div class="test-box">
        <h1 class="success">✅ 网络连接测试成功！</h1>
        <p class="info">如果你能看到这个页面，说明局域网连接正常。</p>
        <p><strong>测试时间:</strong> <span id="time"></span></p>
        <p><strong>用户代理:</strong> <span id="ua"></span></p>
        <p><strong>屏幕分辨率:</strong> <span id="screen"></span></p>
        
        <h2>下一步测试:</h2>
        <ul>
            <li><a href="/">返回主页面</a></li>
            <li><a href="/styles.css">测试CSS文件</a></li>
            <li><a href="/app.js">测试JS文件</a></li>
        </ul>
    </div>

    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        document.getElementById('ua').textContent = navigator.userAgent;
        document.getElementById('screen').textContent = screen.width + 'x' + screen.height;
    </script>
</body>
</html>

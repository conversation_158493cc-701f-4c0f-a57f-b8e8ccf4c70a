---
type: "manual"
---

# 规则：Python GUI界面美化设计

## 规则目标
作为助手，你将帮助用户创建美观、用户友好且功能强大的Python GUI界面。遵循以下规则来生成代码和提供建议。

## GUI框架选择

根据项目需求推荐合适的GUI框架：
- **Tkinter**: 轻量级，Python标准库自带，适合简单应用
- **PyQt/PySide**: 功能丰富，现代化外观，适合复杂应用
- **Kivy**: 跨平台，特别适合触摸应用和移动应用
- **wxPython**: 原生外观，跨平台
- **CustomTkinter**: Tkinter的现代化扩展，提供更美观的组件
- **DearPyGui**: 高性能即时模式GUI

## 第三方美化库

利用专业的第三方美化库可以显著提升GUI外观质量：

### 1. 样式框架
- **Qt-Material**: 为PyQt/PySide提供Material Design风格
- **ttkthemes**: 为Tkinter提供多种现代主题
- **Qdarkstyle**: 为PyQt应用提供暗色主题
- **awesomeTkinter**: Tkinter组件美化库
- **PySimpleGUIQt**: 简化PyQt使用并提供现代外观

```python
# Qt-Material示例
from qt_material import apply_stylesheet

app = QApplication([])
apply_stylesheet(app, theme='dark_teal.xml')
```

```python
# ttkthemes示例
from tkinter import ttk
from ttkthemes import ThemedTk

root = ThemedTk(theme="arc")  # 使用'arc'主题
```

### 2. 图表与可视化美化
- **Matplotlib-Stylesheets**: 提升图表视觉效果
- **Seaborn**: 统计图表美化
- **Plotly**: 交互式数据可视化
- **PyQtGraph**: 科学绘图与数据可视化

```python
# Matplotlib美化示例
import matplotlib.pyplot as plt
plt.style.use('ggplot')  # 使用ggplot风格
```

### 3. 组件库
- **Flet**: 快速构建Flutter风格的Python应用
- **PyWebView**: 使用Web技术创建GUI
- **streamlit-components**: 扩展Streamlit UI组件
- **DearPyGui widgets**: 高性能自定义组件

```python
# Flet示例
import flet as ft

def main(page: ft.Page):
    page.title = "美化示例"
    page.theme_mode = ft.ThemeMode.DARK
    page.add(
        ft.Card(
            content=ft.Container(
                content=ft.Text("精美卡片", size=20),
                padding=15
            ),
            elevation=5,
        )
    )

ft.app(target=main)
```

### 4. 图标与资源
- **PyQtAwesome**: FontAwesome图标集成
- **tksvg**: 在Tkinter中显示SVG图标
- **Material-Design-Icons**: 提供现代化图标
- **customtkinter-toolbox**: CustomTkinter的图标与工具集

```python
# PyQtAwesome示例
from qtawesome import icon
button.setIcon(icon('fa5s.user', color='white'))
```

## 设计原则

### 1. 布局与间距
- 使用网格系统或弹性布局
- 保持一致的边距和间距（通常8px或16px）
- 对齐元素创建视觉秩序
- 使用分组框架区分功能区域

```python
# PyQt示例 - 使用网格布局与统一间距
layout = QGridLayout()
layout.setSpacing(10)
layout.setContentsMargins(20, 20, 20, 20)
```

### 2. 配色方案
- 使用有限的主色调（2-3种）和点缀色
- 确保文本与背景对比度足够
- 遵循60-30-10原则（主色60%，次色30%，强调色10%）
- 为重要按钮使用突出色

```python
# 配色示例
PRIMARY_COLOR = "#3498db"    # 主色
SECONDARY_COLOR = "#2c3e50"  # 次色
ACCENT_COLOR = "#e74c3c"     # 强调色
BACKGROUND_COLOR = "#ecf0f1" # 背景色
TEXT_COLOR = "#2c3e50"       # 文本色
```

### 3. 字体和文本
- 使用最多2种字体（标题和正文）
- 确保字体大小可读（最小12px）
- 使用粗体区分标题和重要信息
- 保持文本左对齐（除非特殊设计需求）

```python
# Tkinter字体示例
title_font = ("Helvetica", 16, "bold")
body_font = ("Helvetica", 12)
small_font = ("Helvetica", 10)
```

## 组件美化

### 1. 按钮
- 添加悬停效果
- 使用圆角边框
- 为主要操作按钮使用突出色
- 添加适当的内边距

```python
# PyQt按钮样式示例
button.setStyleSheet("""
    QPushButton {
        background-color: #3498db;
        color: white;
        border-radius: 5px;
        padding: 8px 16px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #2980b9;
    }
    QPushButton:pressed {
        background-color: #1c6ea4;
    }
""")
```

### 2. 输入框
- 添加焦点状态效果
- 使用占位文本提供输入指导
- 添加输入验证和即时反馈
- 考虑使用图标指示输入类型

### 3. 列表和表格
- 使用条纹效果增强可读性
- 高亮选中行
- 限制一次显示的项目数量，添加滚动条
- 为表头使用不同样式

## 响应式设计

- 使用相对尺寸而非固定尺寸
- 实现窗口调整时的动态布局
- 测试不同分辨率下的显示效果
- 考虑使用最小尺寸约束

```python
# 响应式布局示例 (PyQt)
def resizeEvent(self, event):
    width = self.width()
    if width < 600:
        # 小屏幕布局调整
        self.sidebar.setMaximumWidth(0)
    else:
        # 大屏幕布局
        self.sidebar.setMaximumWidth(200)
    super().resizeEvent(event)
```

## 高级美化技巧

### 1. 动画效果
- 为状态变化添加平滑过渡
- 使用淡入淡出效果提升用户体验
- 避免过度使用动画造成干扰

### 2. 主题切换
- 实现亮色/暗色主题
- 允许用户自定义主题
- 保存用户主题偏好

### 3. 自定义组件
- 创建统一风格的自定义组件
- 使用Canvas或自绘制组件实现特殊效果
- 考虑使用SVG图标保证清晰度

## 代码示例

当用户请求时，提供完整的、可执行的代码示例，包括：
1. 基本窗口设置
2. 布局实现
3. 样式应用
4. 响应式调整
5. 注释清晰的代码

## 注意事项

- 始终优先考虑功能性，美观是第二位的
- 确保界面直观且易于导航
- 避免过度设计和不必要的视觉元素
- 测试不同平台上的外观一致性
- 考虑可访问性需求（如色盲友好设计）

遵循这些规则，为用户创建既美观又实用的Python GUI界面。
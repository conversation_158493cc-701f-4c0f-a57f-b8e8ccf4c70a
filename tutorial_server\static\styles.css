/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Helvetica, Arial, sans-serif;
  background: #0e1013;
  color: #e6e6e6;
  line-height: 1.6;
}

/* Header */
.site-header {
  padding: 32px 16px;
  text-align: center;
  background: linear-gradient(135deg, #243949, #517fa4);
}

.site-header h1 {
  margin: 0 0 8px;
  font-size: clamp(24px, 5vw, 32px);
  font-weight: 700;
}

.site-header p {
  margin: 0;
  color: #dfe9f3;
  font-size: clamp(14px, 3vw, 16px);
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 24px auto;
  padding: 0 16px;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 20px;
}

/* Table of Contents */
.toc {
  background: #161a20;
  border: 1px solid #2a2f3a;
  border-radius: 12px;
  padding: 20px;
  position: sticky;
  top: 20px;
  height: fit-content;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toc h2 {
  margin-top: 0;
  font-size: 18px;
  color: #ffffff;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 8px;
}

.toc ol {
  padding-left: 20px;
}

.toc li {
  margin: 8px 0;
}

.toc a {
  color: #b3c7d6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.toc a:hover {
  color: #3b82f6;
}

/* Cards */
.card {
  background: #161a20;
  border: 1px solid #2a2f3a;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.card h2 {
  margin-top: 0;
  color: #ffffff;
  font-size: 24px;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 8px;
}

.card h3 {
  color: #e6e6e6;
  margin-top: 24px;
  margin-bottom: 12px;
}

/* Yaku Sections */
.yaku-section {
  margin: 20px 0;
  padding: 16px;
  background: #1a1f26;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.yaku-section h3 {
  margin-top: 0;
  color: #ffffff;
  font-size: 18px;
}

/* Tile Examples */
.tile-example {
  background: #0f1419;
  border: 1px solid #2a2f3a;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  font-family: "Courier New", monospace;
}

.tile-example.error {
  border-left: 4px solid #ef4444;
  background: #1f1012;
}

.tile-example code {
  background: transparent;
  color: #fbbf24;
  font-weight: 600;
}

/* Furiten Visualization */
.furiten-example {
  margin: 20px 0;
  padding: 16px;
  background: #1a1f26;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.visual-example {
  margin: 12px 0;
}

.hand-display, .discard-display, .scenario, .choice {
  margin: 12px 0;
  padding: 12px;
  background: #0f1419;
  border-radius: 6px;
}

.discard-river {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.tile {
  background: #2a2f3a;
  color: #e6e6e6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #3a3f4a;
}

.tile.danger {
  background: #ef4444;
  color: white;
  font-weight: bold;
}

.waiting {
  color: #10b981;
  font-weight: 600;
}

.result {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  font-weight: 600;
}

.result.error {
  background: #1f1012;
  border-left: 4px solid #ef4444;
  color: #fca5a5;
}

.result.warning {
  background: #1f1a0f;
  border-left: 4px solid #f59e0b;
  color: #fcd34d;
}

/* Practice Sections */
.practice-section {
  margin: 24px 0;
  padding: 20px;
  background: #1a1f26;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.practice-quiz {
  margin: 16px 0;
}

.practice-result {
  margin-top: 12px;
  font-weight: 600;
}

/* Lists and Pills */
.pill-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-left: 0;
  list-style: none;
}

.pill-list li {
  background: #1f2630;
  border: 1px solid #2f3b4a;
  border-radius: 999px;
  padding: 8px 12px;
  font-size: 14px;
  transition: background 0.2s ease;
}

.pill-list li:hover {
  background: #2a3441;
}

/* Special Boxes */
.tip {
  background: #14221a;
  border-left: 4px solid #3cb371;
  color: #cde7d8;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px 0;
}

.example {
  background: #1a1822;
  border-left: 4px solid #8a7bd1;
  color: #dcd7ff;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px 0;
}

/* Buttons */
.btn {
  background: #3b82f6;
  border: none;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.practice-btn {
  background: #10b981;
}

.practice-btn:hover {
  background: #059669;
}

/* Footer */
.site-footer {
  padding: 32px 16px;
  text-align: center;
  color: #98a6b3;
  font-size: 14px;
  border-top: 1px solid #2a2f3a;
  margin-top: 40px;
}

/* Responsive Design */
@media (max-width: 900px) {
  .container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .toc {
    position: static;
    order: -1;
  }

  .card {
    padding: 20px;
  }

  .yaku-section {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .site-header {
    padding: 24px 12px;
  }

  .container {
    padding: 0 12px;
  }

  .card {
    padding: 16px;
  }

  .tile-example code {
    font-size: 12px;
    word-break: break-all;
  }

  .discard-river {
    gap: 2px;
  }

  .tile {
    padding: 2px 6px;
    font-size: 11px;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
    color: black;
    font-size: 12pt;
    line-height: 1.4;
  }

  .site-header {
    background: none;
    color: black;
    border-bottom: 2px solid black;
  }

  .container {
    display: block;
    max-width: none;
    margin: 0;
    padding: 0;
  }

  .toc {
    background: none;
    border: 1px solid black;
    page-break-after: always;
  }

  .card {
    background: none;
    border: 1px solid black;
    box-shadow: none;
    page-break-inside: avoid;
    margin-bottom: 20pt;
  }

  .card h2 {
    border-bottom: 2px solid black;
  }

  .yaku-section, .furiten-example, .practice-section {
    background: none;
    border: 1px solid black;
    page-break-inside: avoid;
  }

  .tile-example {
    background: #f5f5f5;
    border: 1px solid black;
  }

  .tip, .example {
    background: #f0f0f0;
    border-left: 4px solid black;
    color: black;
  }

  .btn {
    display: none;
  }

  .site-footer {
    border-top: 1px solid black;
    color: black;
  }

  /* Hide interactive elements in print */
  .practice-quiz, .quiz, #quiz-result, .practice-result {
    display: none;
  }
}

/* Dark Theme Enhancements */
@media (prefers-color-scheme: dark) {
  body {
    background: #0a0e13;
  }

  .card {
    background: #151a21;
    border-color: #252a35;
  }

  .toc {
    background: #151a21;
    border-color: #252a35;
  }
}


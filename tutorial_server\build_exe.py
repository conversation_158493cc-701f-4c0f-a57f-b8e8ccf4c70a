#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日本麻将教学网站打包脚本
自动化构建独立的exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def print_step(step, message):
    """打印构建步骤"""
    print(f"\n{'='*60}")
    print(f"步骤 {step}: {message}")
    print('='*60)

def check_requirements():
    """检查构建环境"""
    print_step(1, "检查构建环境")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装PyInstaller，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 检查必要文件
    current_dir = Path(__file__).parent
    required_files = [
        "mahjong_tutorial_launcher.py",
        "mahjong_tutorial.spec",
        "static/index.html",
        "static/styles.css",
        "static/app.js"
    ]
    
    missing_files = []
    for file in required_files:
        if not (current_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 构建环境检查通过")
    return True

def clean_build_dirs():
    """清理构建目录"""
    print_step(2, "清理构建目录")
    
    current_dir = Path(__file__).parent
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        dir_path = current_dir / dir_name
        if dir_path.exists():
            print(f"删除目录: {dir_path}")
            shutil.rmtree(dir_path)
    
    print("✅ 构建目录清理完成")

def build_executable():
    """构建可执行文件"""
    print_step(3, "构建可执行文件")
    
    current_dir = Path(__file__).parent
    spec_file = current_dir / "mahjong_tutorial.spec"
    
    try:
        # 运行PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", str(spec_file)]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, cwd=current_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 构建成功")
            return True
        else:
            print("❌ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print_step(4, "验证构建结果")
    
    current_dir = Path(__file__).parent
    exe_path = current_dir / "dist" / "日本麻将教学网站.exe"
    
    if not exe_path.exists():
        print(f"❌ 找不到生成的exe文件: {exe_path}")
        return False
    
    # 检查文件大小
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"✅ 生成的exe文件: {exe_path}")
    print(f"✅ 文件大小: {file_size:.1f} MB")
    
    # 检查静态文件是否包含
    print("✅ 构建验证通过")
    return True

def create_readme():
    """创建使用说明"""
    print_step(5, "创建使用说明")
    
    current_dir = Path(__file__).parent
    dist_dir = current_dir / "dist"
    readme_path = dist_dir / "使用说明.txt"
    
    readme_content = """🀄 日本麻将教学网站 - 使用说明

📖 简介：
这是一个专为新手设计的日本麻将教学网站，重点讲解"无役不胡"和"振听"等核心规则。

🚀 使用方法：
1. 双击"日本麻将教学网站.exe"启动程序
2. 点击"启动教学网站"按钮
3. 程序会自动打开浏览器显示教学内容
4. 学习完成后点击"停止服务"按钮

🌐 分享给朋友：
• 确保在同一WiFi网络下
• 朋友可以通过程序显示的局域网地址访问
• 如无法访问，请检查Windows防火墙设置

🛡️ 防火墙设置：
如果其他设备无法访问，请：
1. 临时关闭Windows防火墙测试
2. 或在防火墙中添加程序例外

📚 学习内容：
• 基本和牌条件与役的概念
• 详细的役种讲解（立直、断幺九、平和等）
• 振听规则的可视化说明
• 分步练习题和综合测验

⚠️ 注意事项：
• 程序运行时请勿关闭黑色命令行窗口
• 如遇问题，请重启程序
• 仅供学习交流使用

© 2025 日本麻将教学 · 版本 1.0.0
"""
    
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ 使用说明已创建: {readme_path}")
    except Exception as e:
        print(f"❌ 创建使用说明失败: {e}")

def main():
    """主函数"""
    print("🀄 日本麻将教学网站 - 自动化构建脚本")
    print("=" * 60)
    
    try:
        # 检查构建环境
        if not check_requirements():
            input("\n❌ 构建环境检查失败，按回车键退出...")
            return 1
        
        # 清理构建目录
        clean_build_dirs()
        
        # 构建可执行文件
        if not build_executable():
            input("\n❌ 构建失败，按回车键退出...")
            return 1
        
        # 验证构建结果
        if not verify_build():
            input("\n❌ 构建验证失败，按回车键退出...")
            return 1
        
        # 创建使用说明
        create_readme()
        
        print_step("完成", "构建成功！")
        print("🎉 exe文件已生成在 dist 目录中")
        print("📁 可以将整个 dist 目录打包分发给用户")
        
        # 询问是否立即测试
        try:
            test = input("\n是否立即测试exe文件？(y/n): ").lower().strip()
            if test == 'y':
                current_dir = Path(__file__).parent
                exe_path = current_dir / "dist" / "日本麻将教学网站.exe"
                os.startfile(str(exe_path))
        except KeyboardInterrupt:
            pass
        
        input("\n✅ 构建完成！按回车键退出...")
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 构建被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 构建过程中发生未知错误: {e}")
        input("按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())

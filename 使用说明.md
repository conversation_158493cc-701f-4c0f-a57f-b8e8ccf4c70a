# 雀魂MAX独立版 - 使用说明

## 快速开始

### 1. 启动程序
双击 `start_gui.bat` 启动GUI界面

### 2. 基本使用
1. **皮肤插件**（默认开启）
   - 在"皮肤插件"选项卡中配置角色、皮肤等
   - 可保存/读取配置文件
   - 点击"保存皮肤插件设置"应用更改

2. **助手插件**（可选）
   - 切换到"助手插件"选项卡
   - 点击"启动集成助手"（推荐）或"启动Go助手"
   - 在"程序控制"中启用助手插件

3. **助手分析输出**（高级功能）
   - 切换到"助手分析输出"选项卡
   - 启用"双语言并行模式"（Python + Go）
   - 自定义字体、颜色主题、动画效果
   - 查看实时分析输出

4. **启动代理**
   - 在"程序控制"选项卡点击"启动程序"
   - 设置浏览器代理到 127.0.0.1:23410
   - 启动雀魂游戏

## 网页版助手（推荐）

### 特色功能
- **美观的网页界面**：现代化设计，支持主题切换
- **实时分析输出**：Go助手的分析结果实时显示在网页中
- **响应式设计**：支持不同屏幕尺寸
- **功能丰富**：支持导出日志、暂停/继续、自动滚动等

### 使用方法
1. **编译Go助手**：运行 `build_go_gui_helper.bat`
2. **启动网页版**：双击 `start_web_helper.bat`
3. **自动打开浏览器**：访问 http://localhost:12121
4. **设置代理**：浏览器代理设置为 127.0.0.1:23410
5. **开始游戏**：启动雀魂，实时分析将显示在网页中

## 双语言并行模式

### 特色功能
- **Python插件先启动**：确保基础功能正常运行
- **Go助手后台运行**：提供高性能分析功能，持续输出中文信息
- **HTTP通信**：GUI通过HTTP接口获取Go助手的实时分析结果
- **统一界面显示**：所有输出集中在GUI中
- **自定义界面**：字体、颜色、动画效果可调

### 自定义选项
- **字体设置**：字体族、大小、粗体、斜体
- **颜色主题**：默认、深色、护眼绿、经典蓝、自定义
- **动画效果**：淡入淡出、滑动、缩放、闪烁
- **实时输出**：统一显示Python和Go助手的分析结果

## Go助手程序编译

如果要使用双语言并行模式：
1. 双击运行 `build_go_gui_helper.bat`（需要Go环境）
2. 或运行 `python build_go_gui_helper.py`
3. 编译成功后会生成 `mahjong-helper-gui.exe`

## 注意事项

- 首次运行需要网络连接下载协议文件
- 可能需要将程序添加到杀毒软件白名单
- 确保端口23410未被占用
- 助手功能仅供学习交流使用

## 故障排除

- 查看"运行日志"选项卡获取错误信息
- 检查config目录中的配置文件
- 重启程序或重置配置

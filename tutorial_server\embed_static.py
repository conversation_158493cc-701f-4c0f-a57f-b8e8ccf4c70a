#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将静态文件嵌入到Python代码中，避免打包问题
"""

import base64
from pathlib import Path

def embed_static_files():
    """将静态文件转换为Python代码"""
    current_dir = Path(__file__).parent
    static_dir = current_dir / "static"
    
    if not static_dir.exists():
        print("❌ static目录不存在")
        return
    
    embedded_code = '''# -*- coding: utf-8 -*-
"""
嵌入的静态文件内容
自动生成，请勿手动修改
"""

import base64
from pathlib import Path
import tempfile
import os

STATIC_FILES = {
'''
    
    # 收集所有静态文件
    for file_path in static_dir.rglob("*"):
        if file_path.is_file():
            rel_path = file_path.relative_to(static_dir)
            
            # 读取文件内容
            if file_path.suffix in ['.html', '.css', '.js', '.txt']:
                # 文本文件直接存储
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                embedded_code += f'    "{rel_path.as_posix()}": """{content}""",\n'
            else:
                # 二进制文件用base64编码
                with open(file_path, 'rb') as f:
                    content = base64.b64encode(f.read()).decode('ascii')
                embedded_code += f'    "{rel_path.as_posix()}": "data:base64,{content}",\n'
    
    embedded_code += '''}

def create_temp_static_dir():
    """创建临时静态文件目录"""
    temp_dir = Path(tempfile.mkdtemp(prefix="mahjong_tutorial_"))
    
    for file_path, content in STATIC_FILES.items():
        full_path = temp_dir / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        if content.startswith("data:base64,"):
            # 二进制文件
            data = base64.b64decode(content[12:])
            with open(full_path, 'wb') as f:
                f.write(data)
        else:
            # 文本文件
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
    
    return temp_dir

def cleanup_temp_dir(temp_dir):
    """清理临时目录"""
    import shutil
    try:
        shutil.rmtree(temp_dir)
    except:
        pass
'''
    
    # 写入嵌入文件
    output_path = current_dir / "embedded_static.py"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(embedded_code)
    
    print(f"✅ 静态文件已嵌入到: {output_path}")
    return output_path

if __name__ == "__main__":
    embed_static_files()

{"nested": {"lq": {"options": {"go_package": ".;majprotocol"}, "nested": {"NotifyRoomGameStart": {"fields": {"game_url": {"type": "string", "id": 1}, "connect_token": {"type": "string", "id": 2}, "game_uuid": {"type": "string", "id": 3}, "location": {"type": "string", "id": 4}}}, "NotifyMatchGameStart": {"fields": {"game_url": {"type": "string", "id": 1}, "connect_token": {"type": "string", "id": 2}, "game_uuid": {"type": "string", "id": 3}, "match_mode_id": {"type": "uint32", "id": 4}, "location": {"type": "string", "id": 5}}}, "NotifyRoomPlayerReady": {"fields": {"account_id": {"type": "uint32", "id": 1}, "ready": {"type": "bool", "id": 2}, "account_list": {"type": "AccountReadyState", "id": 3}, "seq": {"type": "uint32", "id": 4}}, "nested": {"AccountReadyState": {"fields": {"account_id": {"type": "uint32", "id": 1}, "ready": {"type": "bool", "id": 2}}}}}, "NotifyRoomPlayerDressing": {"fields": {"account_id": {"type": "uint32", "id": 1}, "dressing": {"type": "bool", "id": 2}, "account_list": {"type": "AccountDressingState", "id": 3}, "seq": {"type": "uint32", "id": 4}}, "nested": {"AccountDressingState": {"fields": {"account_id": {"type": "uint32", "id": 1}, "dressing": {"type": "bool", "id": 2}}}}}, "NotifyRoomPlayerUpdate": {"fields": {"owner_id": {"type": "uint32", "id": 3}, "robot_count": {"type": "uint32", "id": 4}, "player_list": {"rule": "repeated", "type": "PlayerGameView", "id": 5}, "seq": {"type": "uint32", "id": 6}, "robots": {"rule": "repeated", "type": "PlayerGameView", "id": 7}, "positions": {"rule": "repeated", "type": "uint32", "id": 8}}}, "NotifyRoomKickOut": {"fields": {}}, "NotifyFriendStateChange": {"fields": {"target_id": {"type": "uint32", "id": 1}, "active_state": {"type": "AccountActiveState", "id": 2}}}, "NotifyFriendViewChange": {"fields": {"target_id": {"type": "uint32", "id": 1}, "base": {"type": "PlayerBaseView", "id": 2}}}, "NotifyFriendChange": {"fields": {"account_id": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "friend": {"type": "Friend", "id": 3}}}, "NotifyNewFriendApply": {"fields": {"account_id": {"type": "uint32", "id": 1}, "apply_time": {"type": "uint32", "id": 2}, "removed_id": {"type": "uint32", "id": 3}}}, "NotifyClientMessage": {"fields": {"sender": {"type": "PlayerBaseView", "id": 1}, "type": {"type": "uint32", "id": 2}, "content": {"type": "string", "id": 3}}}, "NotifyAccountUpdate": {"fields": {"update": {"type": "AccountUpdate", "id": 1}}}, "NotifyAnotherLogin": {"fields": {}}, "NotifyAccountLogout": {"fields": {}}, "NotifyAnnouncementUpdate": {"fields": {"update_list": {"rule": "repeated", "type": "AnnouncementUpdate", "id": 1}}, "nested": {"AnnouncementUpdate": {"fields": {"lang": {"type": "string", "id": 1}, "platform": {"type": "string", "id": 2}}}}}, "NotifyNewMail": {"fields": {"mail": {"type": "Mail", "id": 1}}}, "NotifyDeleteMail": {"fields": {"mail_id_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "NotifyReviveCoinUpdate": {"fields": {"has_gained": {"type": "bool", "id": 1}}}, "NotifyDailyTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}, "max_daily_task_count": {"type": "uint32", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}}}, "NotifyActivityTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}}}, "NotifyActivityPeriodTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}}}, "NotifyAccountRandomTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}}}, "NotifyActivitySegmentTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "lq.SegmentTaskProgress", "id": 1}}}, "NotifyActivityUpdate": {"fields": {"list": {"rule": "repeated", "type": "FeedActivityData", "id": 1}}, "nested": {"FeedActivityData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "feed_count": {"type": "uint32", "id": 2}, "friend_receive_data": {"type": "CountWithTimeData", "id": 3}, "friend_send_data": {"type": "CountWithTimeData", "id": 4}, "gift_inbox": {"rule": "repeated", "type": "GiftBoxData", "id": 5}}, "nested": {"CountWithTimeData": {"fields": {"count": {"type": "uint32", "id": 1}, "last_update_time": {"type": "uint32", "id": 2}}}, "GiftBoxData": {"fields": {"id": {"type": "uint32", "id": 1}, "item_id": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}, "from_account_id": {"type": "uint32", "id": 4}, "time": {"type": "uint32", "id": 5}, "received": {"type": "uint32", "id": 6}}}}}}}, "NotifyAccountChallengeTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}, "level": {"type": "uint32", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}, "match_count": {"type": "uint32", "id": 4}, "ticket_id": {"type": "uint32", "id": 5}, "rewarded_season": {"rule": "repeated", "type": "uint32", "id": 6}}}, "NotifyNewComment": {"fields": {}}, "NotifyRollingNotice": {"fields": {}}, "NotifyMaintainNotice": {"fields": {}}, "NotifyGiftSendRefresh": {"fields": {}}, "NotifyShopUpdate": {"fields": {"shop_info": {"type": "ShopInfo", "id": 1}}}, "NotifyIntervalUpdate": {"fields": {}}, "NotifyVipLevelChange": {"fields": {"gift_limit": {"type": "uint32", "id": 1}, "friend_max_count": {"type": "uint32", "id": 2}, "zhp_free_refresh_limit": {"type": "uint32", "id": 3}, "zhp_cost_refresh_limit": {"type": "uint32", "id": 4}, "buddy_bonus": {"type": "float", "id": 5}, "record_collect_limit": {"type": "uint32", "id": 6}}}, "NotifyServerSetting": {"fields": {"settings": {"type": "ServerSettings", "id": 1}}}, "NotifyPayResult": {"fields": {"pay_result": {"type": "uint32", "id": 1}, "order_id": {"type": "string", "id": 2}, "goods_id": {"type": "uint32", "id": 3}, "new_month_ticket": {"type": "uint32", "id": 4}, "resource_modify": {"rule": "repeated", "type": "ResourceModify", "id": 5}}, "nested": {"ResourceModify": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "final": {"type": "uint32", "id": 3}}}}}, "NotifyCustomContestAccountMsg": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "sender": {"type": "string", "id": 3}, "content": {"type": "string", "id": 4}, "verified": {"type": "uint32", "id": 5}}}, "NotifyCustomContestSystemMsg": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "uuid": {"type": "string", "id": 3}, "game_start": {"type": "CustomizedContestGameStart", "id": 4}, "game_end": {"type": "CustomizedContestGameEnd", "id": 5}}}, "NotifyMatchTimeout": {"fields": {"sid": {"type": "string", "id": 1}}}, "NotifyMatchFailed": {"fields": {"sid": {"type": "string", "id": 1}}}, "NotifyCustomContestState": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "state": {"type": "uint32", "id": 2}}}, "NotifyActivityChange": {"fields": {"new_activities": {"rule": "repeated", "type": "Activity", "id": 1}, "end_activities": {"rule": "repeated", "type": "uint32", "id": 2}}}, "NotifyAFKResult": {"fields": {"type": {"type": "uint32", "id": 1}, "ban_end_time": {"type": "uint32", "id": 2}, "game_uuid": {"type": "string", "id": 3}}}, "NotifyLoginQueueFinished": {"fields": {}}, "NotifyGameFinishRewardV2": {"fields": {"mode_id": {"type": "uint32", "id": 1}, "level_change": {"type": "LevelChange", "id": 2}, "match_chest": {"type": "MatchChest", "id": 3}, "main_character": {"type": "MainCharacter", "id": 4}, "character_gift": {"type": "CharacterGift", "id": 5}, "badges": {"rule": "repeated", "type": "BadgeAchieveProgress", "id": 6}}, "nested": {"LevelChange": {"fields": {"origin": {"type": "AccountLevel", "id": 1}, "final": {"type": "AccountLevel", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "MatchChest": {"fields": {"chest_id": {"type": "uint32", "id": 1}, "origin": {"type": "uint32", "id": 2}, "final": {"type": "uint32", "id": 3}, "is_graded": {"type": "bool", "id": 4}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 5}}}, "MainCharacter": {"fields": {"level": {"type": "uint32", "id": 1}, "exp": {"type": "uint32", "id": 2}, "add": {"type": "uint32", "id": 3}}}, "CharacterGift": {"fields": {"origin": {"type": "uint32", "id": 1}, "final": {"type": "uint32", "id": 2}, "add": {"type": "uint32", "id": 3}, "is_graded": {"type": "bool", "id": 4}}}}}, "NotifyActivityRewardV2": {"fields": {"activity_reward": {"rule": "repeated", "type": "ActivityReward", "id": 1}}, "nested": {"ActivityReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 2}}}}}, "NotifyActivityPointV2": {"fields": {"activity_points": {"rule": "repeated", "type": "ActivityPoint", "id": 1}}, "nested": {"ActivityPoint": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "NotifyLeaderboardPointV2": {"fields": {"leaderboard_points": {"rule": "repeated", "type": "LeaderboardPoint", "id": 1}}, "nested": {"LeaderboardPoint": {"fields": {"leaderboard_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "NotifySeerReport": {"fields": {"report": {"type": "<PERSON><PERSON><PERSON><PERSON>", "id": 1}}}, "NotifyConnectionShutdown": {"fields": {"reason": {"type": "uint32", "id": 1}, "close_at": {"type": "uint32", "id": 2}}}, "Error": {"fields": {"code": {"type": "uint32", "id": 1}, "u32_params": {"rule": "repeated", "type": "uint32", "id": 2}, "str_params": {"rule": "repeated", "type": "string", "id": 3}, "json_param": {"type": "string", "id": 4}}}, "Wrapper": {"fields": {"name": {"type": "string", "id": 1}, "data": {"type": "bytes", "id": 2}}}, "NetworkEndpoint": {"fields": {"family": {"type": "string", "id": 1}, "address": {"type": "string", "id": 2}, "port": {"type": "uint32", "id": 3}}}, "ReqCommon": {"fields": {}}, "ResCommon": {"fields": {"error": {"type": "Error", "id": 1}}}, "ResAccountUpdate": {"fields": {"error": {"type": "Error", "id": 1}, "update": {"type": "AccountUpdate", "id": 2}}}, "AntiAddiction": {"fields": {"online_duration": {"type": "uint32", "id": 1}}}, "HighestHuRecord": {"fields": {"fanshu": {"type": "uint32", "id": 1}, "doranum": {"type": "uint32", "id": 2}, "title": {"type": "string", "id": 3}, "hands": {"rule": "repeated", "type": "string", "id": 4}, "ming": {"rule": "repeated", "type": "string", "id": 5}, "hupai": {"type": "string", "id": 6}, "title_id": {"type": "uint32", "id": 7}}}, "AccountMahjongStatistic": {"fields": {"final_position_counts": {"rule": "repeated", "type": "uint32", "id": 1}, "recent_round": {"type": "RoundSummary", "id": 2}, "recent_hu": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 3}, "highest_hu": {"type": "HighestHuRecord", "id": 4}, "recent_20_hu_summary": {"type": "Liqi20Summary", "id": 6}, "recent_10_hu_summary": {"type": "LiQi10Summary", "id": 7}, "recent_10_game_result": {"rule": "repeated", "type": "GameResult", "id": 8}}, "nested": {"RoundSummary": {"fields": {"total_count": {"type": "uint32", "id": 1}, "rong_count": {"type": "uint32", "id": 2}, "zimo_count": {"type": "uint32", "id": 3}, "fangchong_count": {"type": "uint32", "id": 4}}}, "HuSummary": {"fields": {"total_count": {"type": "uint32", "id": 1}, "dora_round_count": {"type": "uint32", "id": 2}, "total_fan": {"type": "uint32", "id": 3}}}, "Liqi20Summary": {"fields": {"total_count": {"type": "uint32", "id": 1}, "total_lidora_count": {"type": "uint32", "id": 2}, "average_hu_point": {"type": "uint32", "id": 3}}}, "LiQi10Summary": {"fields": {"total_xuanshang": {"type": "uint32", "id": 1}, "total_fanshu": {"type": "uint32", "id": 2}}}, "GameResult": {"fields": {"rank": {"type": "uint32", "id": 1}, "final_point": {"type": "int32", "id": 2}}}}}, "AccountStatisticData": {"fields": {"mahjong_category": {"type": "uint32", "id": 1}, "game_category": {"type": "uint32", "id": 2}, "statistic": {"type": "AccountMahjongStatistic", "id": 3}, "game_type": {"type": "uint32", "id": 4}}}, "AccountLevel": {"fields": {"id": {"type": "uint32", "id": 1}, "score": {"type": "uint32", "id": 2}}}, "ViewSlot": {"fields": {"slot": {"type": "uint32", "id": 1}, "item_id": {"type": "uint32", "id": 2}, "type": {"type": "uint32", "id": 3}, "item_id_list": {"rule": "repeated", "type": "uint32", "id": 4}}}, "FavoriteHu": {"fields": {"category": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "hu": {"type": "HighestHuRecord", "id": 3}, "mode": {"type": "uint32", "id": 4}}}, "Account": {"fields": {"account_id": {"type": "uint32", "id": 1}, "nickname": {"type": "string", "id": 2}, "login_time": {"type": "uint32", "id": 3}, "logout_time": {"type": "uint32", "id": 4}, "room_id": {"type": "uint32", "id": 5}, "anti_addiction": {"type": "AntiAddiction", "id": 6}, "title": {"type": "uint32", "id": 7}, "signature": {"type": "string", "id": 8}, "email": {"type": "string", "id": 9}, "email_verify": {"type": "uint32", "id": 10}, "gold": {"type": "uint32", "id": 11}, "diamond": {"type": "uint32", "id": 12}, "avatar_id": {"type": "uint32", "id": 13}, "vip": {"type": "uint32", "id": 14}, "birthday": {"type": "int32", "id": 15}, "phone": {"type": "string", "id": 16}, "phone_verify": {"type": "uint32", "id": 17}, "platform_diamond": {"rule": "repeated", "type": "PlatformDiamond", "id": 18}, "level": {"type": "AccountLevel", "id": 21}, "level3": {"type": "AccountLevel", "id": 22}, "avatar_frame": {"type": "uint32", "id": 23}, "skin_ticket": {"type": "uint32", "id": 24}, "platform_skin_ticket": {"rule": "repeated", "type": "PlatformSkinTicket", "id": 25}, "verified": {"type": "uint32", "id": 26}, "challenge_levels": {"rule": "repeated", "type": "ChallengeLevel", "id": 27}, "frozen_state": {"type": "uint32", "id": 29}, "achievement_count": {"rule": "repeated", "type": "AchievementCount", "id": 28}, "loading_image": {"rule": "repeated", "type": "uint32", "id": 30}, "favorite_hu": {"rule": "repeated", "type": "FavoriteHu", "id": 34}, "badges": {"rule": "repeated", "type": "Badge", "id": 35}}, "nested": {"PlatformDiamond": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "PlatformSkinTicket": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ChallengeLevel": {"fields": {"season": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}, "rank": {"type": "uint32", "id": 3}}}, "AchievementCount": {"fields": {"rare": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "Badge": {"fields": {"id": {"type": "uint32", "id": 1}, "achieved_time": {"type": "uint32", "id": 2}, "achieved_counter": {"type": "uint32", "id": 3}}}}}, "AccountOwnerData": {"fields": {"unlock_characters": {"rule": "repeated", "type": "uint32", "id": 1}}}, "AccountUpdate": {"fields": {"numerical": {"rule": "repeated", "type": "NumericalUpdate", "id": 1}, "character": {"type": "CharacterUpdate", "id": 2}, "bag": {"type": "BagUpdate", "id": 3}, "achievement": {"type": "AchievementUpdate", "id": 4}, "shilian": {"type": "Account<PERSON><PERSON><PERSON><PERSON>", "id": 5}, "daily_task": {"type": "DailyTaskUpdate", "id": 6}, "title": {"type": "TitleUpdate", "id": 7}, "new_recharged_list": {"rule": "repeated", "type": "uint32", "id": 8}, "activity_task": {"type": "TaskUpdate", "id": 9}, "activity_flip_task": {"type": "TaskUpdate", "id": 10}, "activity_period_task": {"type": "TaskUpdate", "id": 11}, "activity_random_task": {"type": "TaskUpdate", "id": 12}, "challenge": {"type": "AccountChallengeUpdate", "id": 13}, "ab_match": {"type": "AccountABMatchUpdate", "id": 14}, "activity": {"type": "lq.AccountActivityUpdate", "id": 15}, "activity_segment_task": {"type": "SegmentTaskUpdate", "id": 16}, "month_ticket": {"type": "MonthTicketUpdate", "id": 17}, "main_character": {"type": "MainCharacterUpdate", "id": 18}, "badge": {"type": "BadgeUpdate", "id": 19}}, "nested": {"NumericalUpdate": {"fields": {"id": {"type": "uint32", "id": 1}, "final": {"type": "uint32", "id": 3}}}, "CharacterUpdate": {"fields": {"characters": {"rule": "repeated", "type": "Character", "id": 2}, "skins": {"rule": "repeated", "type": "uint32", "id": 3}, "finished_endings": {"rule": "repeated", "type": "uint32", "id": 4}, "rewarded_endings": {"rule": "repeated", "type": "uint32", "id": 5}}}, "AchievementUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "AchievementProgress", "id": 1}, "rewarded_group": {"rule": "repeated", "type": "uint32", "id": 2}}}, "DailyTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}, "task_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "TitleUpdate": {"fields": {"new_titles": {"rule": "repeated", "type": "uint32", "id": 1}, "remove_titles": {"rule": "repeated", "type": "uint32", "id": 2}}}, "TaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}, "task_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "AccountChallengeUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "TaskProgress", "id": 1}, "level": {"type": "uint32", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}, "match_count": {"type": "uint32", "id": 4}, "ticket_id": {"type": "uint32", "id": 5}, "task_list": {"rule": "repeated", "type": "uint32", "id": 6}, "rewarded_season": {"rule": "repeated", "type": "uint32", "id": 7}}}, "AccountABMatchUpdate": {"fields": {"match_id": {"type": "uint32", "id": 1}, "match_count": {"type": "uint32", "id": 2}, "buy_in_count": {"type": "uint32", "id": 3}, "point": {"type": "uint32", "id": 4}, "rewarded": {"type": "bool", "id": 5}, "match_max_point": {"rule": "repeated", "type": "MatchPoint", "id": 6}, "quit": {"type": "bool", "id": 7}}, "nested": {"MatchPoint": {"fields": {"match_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "SegmentTaskUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "lq.SegmentTaskProgress", "id": 1}, "task_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "MonthTicketUpdate": {"fields": {"end_time": {"type": "uint32", "id": 1}, "last_pay_time": {"type": "uint32", "id": 2}}}, "MainCharacterUpdate": {"fields": {"character_id": {"type": "uint32", "id": 1}, "skin_id": {"type": "uint32", "id": 2}}}, "BadgeUpdate": {"fields": {"progresses": {"rule": "repeated", "type": "BadgeAchieveProgress", "id": 1}}}}}, "GameMetaData": {"fields": {"room_id": {"type": "uint32", "id": 1}, "mode_id": {"type": "uint32", "id": 2}, "contest_uid": {"type": "uint32", "id": 3}}}, "AccountPlayingGame": {"fields": {"game_uuid": {"type": "string", "id": 1}, "category": {"type": "uint32", "id": 2}, "meta": {"type": "GameMetaData", "id": 3}}}, "RandomCharacter": {"fields": {"character_id": {"type": "uint32", "id": 1}, "skin_id": {"type": "uint32", "id": 2}}}, "AccountCacheView": {"fields": {"cache_version": {"type": "uint32", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "nickname": {"type": "string", "id": 3}, "login_time": {"type": "uint32", "id": 4}, "logout_time": {"type": "uint32", "id": 5}, "is_online": {"type": "bool", "id": 6}, "room_id": {"type": "uint32", "id": 7}, "title": {"type": "uint32", "id": 8}, "avatar_id": {"type": "uint32", "id": 9}, "vip": {"type": "uint32", "id": 10}, "level": {"type": "AccountLevel", "id": 11}, "playing_game": {"type": "AccountPlayingGame", "id": 12}, "level3": {"type": "AccountLevel", "id": 13}, "avatar_frame": {"type": "uint32", "id": 14}, "verified": {"type": "uint32", "id": 15}, "ban_deadline": {"type": "uint32", "id": 16}, "comment_ban": {"type": "uint32", "id": 17}, "ban_state": {"type": "uint32", "id": 18}}}, "PlayerBaseView": {"fields": {"account_id": {"type": "uint32", "id": 1}, "avatar_id": {"type": "uint32", "id": 2}, "title": {"type": "uint32", "id": 3}, "nickname": {"type": "string", "id": 4}, "level": {"type": "AccountLevel", "id": 5}, "level3": {"type": "AccountLevel", "id": 6}, "avatar_frame": {"type": "uint32", "id": 7}, "verified": {"type": "uint32", "id": 8}, "is_banned": {"type": "uint32", "id": 9}}}, "PlayerGameView": {"fields": {"account_id": {"type": "uint32", "id": 1}, "avatar_id": {"type": "uint32", "id": 2}, "title": {"type": "uint32", "id": 3}, "nickname": {"type": "string", "id": 4}, "level": {"type": "AccountLevel", "id": 5}, "character": {"type": "Character", "id": 6}, "level3": {"type": "AccountLevel", "id": 7}, "avatar_frame": {"type": "uint32", "id": 8}, "verified": {"type": "uint32", "id": 9}, "views": {"rule": "repeated", "type": "ViewSlot", "id": 10}}}, "GameSetting": {"fields": {"emoji_switch": {"type": "uint32", "id": 1}}}, "GameMode": {"fields": {"mode": {"type": "uint32", "id": 1}, "ai": {"type": "bool", "id": 4}, "extendinfo": {"type": "string", "id": 5}, "detail_rule": {"type": "GameDetailRule", "id": 6}, "testing_environment": {"type": "GameTestingEnvironmentSet", "id": 7}, "game_setting": {"type": "GameSetting", "id": 8}}}, "GameTestingEnvironmentSet": {"fields": {"paixing": {"type": "uint32", "id": 1}, "left_count": {"type": "uint32", "id": 2}, "field_spell_var": {"type": "uint32", "id": 3}}}, "GameDetailRule": {"fields": {"time_fixed": {"type": "uint32", "id": 1}, "time_add": {"type": "uint32", "id": 2}, "dora_count": {"type": "uint32", "id": 3}, "shiduan": {"type": "uint32", "id": 4}, "init_point": {"type": "uint32", "id": 5}, "fandian": {"type": "uint32", "id": 6}, "can_jifei": {"type": "bool", "id": 7}, "tianbian_value": {"type": "uint32", "id": 8}, "liqibang_value": {"type": "uint32", "id": 9}, "changbang_value": {"type": "uint32", "id": 10}, "noting_fafu_1": {"type": "uint32", "id": 11}, "noting_fafu_2": {"type": "uint32", "id": 12}, "noting_fafu_3": {"type": "uint32", "id": 13}, "have_liujumanguan": {"type": "bool", "id": 14}, "have_qieshangmanguan": {"type": "bool", "id": 15}, "have_biao_dora": {"type": "bool", "id": 16}, "have_gang_biao_dora": {"type": "bool", "id": 17}, "ming_dora_immediately_open": {"type": "bool", "id": 18}, "have_li_dora": {"type": "bool", "id": 19}, "have_gang_li_dora": {"type": "bool", "id": 20}, "have_sifenglianda": {"type": "bool", "id": 21}, "have_sigangsanle": {"type": "bool", "id": 22}, "have_sijializhi": {"type": "bool", "id": 23}, "have_jiuzhongjiupai": {"type": "bool", "id": 24}, "have_sanjiahele": {"type": "bool", "id": 25}, "have_toutiao": {"type": "bool", "id": 26}, "have_helelianzhuang": {"type": "bool", "id": 27}, "have_helezhongju": {"type": "bool", "id": 28}, "have_tingpailianzhuang": {"type": "bool", "id": 29}, "have_tingpaizhongju": {"type": "bool", "id": 30}, "have_yifa": {"type": "bool", "id": 31}, "have_nanruxiru": {"type": "bool", "id": 32}, "jingsuanyuandian": {"type": "uint32", "id": 33}, "shunweima_2": {"type": "int32", "id": 34}, "shunweima_3": {"type": "int32", "id": 35}, "shunweima_4": {"type": "int32", "id": 36}, "bianjietishi": {"type": "bool", "id": 37}, "ai_level": {"type": "uint32", "id": 38}, "have_zimosun": {"type": "bool", "id": 39}, "disable_multi_yukaman": {"type": "bool", "id": 40}, "fanfu": {"type": "uint32", "id": 41}, "guyi_mode": {"type": "uint32", "id": 42}, "dora3_mode": {"type": "uint32", "id": 43}, "begin_open_mode": {"type": "uint32", "id": 44}, "jiuchao_mode": {"type": "uint32", "id": 45}, "muyu_mode": {"type": "uint32", "id": 46}, "open_hand": {"type": "uint32", "id": 47}, "xuezhandaodi": {"type": "uint32", "id": 48}, "huansanzhang": {"type": "uint32", "id": 49}, "chuanma": {"type": "uint32", "id": 50}, "reveal_discard": {"type": "uint32", "id": 51}, "field_spell_mode": {"type": "uint32", "id": 52}, "zhanxing": {"type": "uint32", "id": 53}, "tianming_mode": {"type": "uint32", "id": 54}, "disable_leijiyiman": {"type": "bool", "id": 60}, "disable_double_yakuman": {"type": "uint32", "id": 62}, "disable_composite_yakuman": {"type": "uint32", "id": 63}, "enable_shiti": {"type": "uint32", "id": 64}, "enable_nontsumo_liqi": {"type": "uint32", "id": 65}, "disable_double_wind_four_fu": {"type": "uint32", "id": 66}, "disable_angang_guoshi": {"type": "uint32", "id": 67}, "enable_renhe": {"type": "uint32", "id": 68}, "enable_baopai_extend_settings": {"type": "uint32", "id": 69}, "yongchang_mode": {"type": "uint32", "id": 70}, "hunzhiyiji_mode": {"type": "uint32", "id": 71}, "wanxiangxiuluo_mode": {"type": "uint32", "id": 72}, "beishuizhizhan_mode": {"type": "uint32", "id": 73}}}, "Room": {"fields": {"room_id": {"type": "uint32", "id": 1}, "owner_id": {"type": "uint32", "id": 2}, "mode": {"type": "GameMode", "id": 3}, "max_player_count": {"type": "uint32", "id": 4}, "persons": {"rule": "repeated", "type": "PlayerGameView", "id": 5}, "ready_list": {"rule": "repeated", "type": "uint32", "id": 6}, "is_playing": {"type": "bool", "id": 7}, "public_live": {"type": "bool", "id": 8}, "robot_count": {"type": "uint32", "id": 9}, "tournament_id": {"type": "uint32", "id": 10}, "seq": {"type": "uint32", "id": 11}, "pre_rule": {"type": "string", "id": 12}, "robots": {"rule": "repeated", "type": "PlayerGameView", "id": 13}, "positions": {"rule": "repeated", "type": "uint32", "id": 14}}}, "GameEndResult": {"fields": {"players": {"rule": "repeated", "type": "PlayerItem", "id": 1}}, "nested": {"PlayerItem": {"fields": {"seat": {"type": "uint32", "id": 1}, "total_point": {"type": "int32", "id": 2}, "part_point_1": {"type": "int32", "id": 3}, "part_point_2": {"type": "int32", "id": 4}, "grading_score": {"type": "int32", "id": 5}, "gold": {"type": "int32", "id": 6}}}}}, "GameConnectInfo": {"fields": {"connect_token": {"type": "string", "id": 2}, "game_uuid": {"type": "string", "id": 3}, "location": {"type": "string", "id": 4}}}, "ItemGainRecord": {"fields": {"item_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ItemGainRecords": {"fields": {"record_time": {"type": "uint32", "id": 1}, "limit_source_id": {"type": "uint32", "id": 2}, "records": {"rule": "repeated", "type": "ItemGainRecord", "id": 3}}}, "FakeRandomRecords": {"fields": {"item_id": {"type": "uint32", "id": 1}, "special_item_id": {"type": "uint32", "id": 2}, "gain_count": {"type": "uint32", "id": 3}, "gain_history": {"rule": "repeated", "type": "uint32", "id": 4}}}, "Item": {"fields": {"item_id": {"type": "uint32", "id": 1}, "stack": {"type": "uint32", "id": 2}}}, "Bag": {"fields": {"items": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 1}, "daily_gain_record": {"rule": "repeated", "type": "ItemGainRecords", "id": 2}}}, "BagUpdate": {"fields": {"update_items": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 1}, "update_daily_gain_record": {"rule": "repeated", "type": "ItemGainRecords", "id": 2}}}, "RewardSlot": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "OpenResult": {"fields": {"reward": {"type": "RewardSlot", "id": 1}, "replace": {"type": "RewardSlot", "id": 2}}}, "RewardPlusResult": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "exchange": {"type": "Exchange", "id": 3}}, "nested": {"Exchange": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "exchange": {"type": "uint32", "id": 3}}}}}, "ExecuteReward": {"fields": {"reward": {"type": "RewardSlot", "id": 1}, "replace": {"type": "RewardSlot", "id": 2}, "replace_count": {"type": "uint32", "id": 3}}}, "ExecuteResult": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "int32", "id": 2}}}, "I18nContext": {"fields": {"lang": {"type": "string", "id": 1}, "context": {"type": "string", "id": 2}}}, "Mail": {"fields": {"mail_id": {"type": "uint32", "id": 1}, "state": {"type": "uint32", "id": 2}, "take_attachment": {"type": "bool", "id": 3}, "title": {"type": "string", "id": 4}, "content": {"type": "string", "id": 5}, "attachments": {"rule": "repeated", "type": "RewardSlot", "id": 6}, "create_time": {"type": "uint32", "id": 7}, "expire_time": {"type": "uint32", "id": 8}, "reference_id": {"type": "uint32", "id": 9}, "title_i18n": {"rule": "repeated", "type": "I18nContext", "id": 10}, "content_i18n": {"rule": "repeated", "type": "I18nContext", "id": 11}, "template_id": {"type": "uint32", "id": 12}}}, "AchievementProgress": {"fields": {"id": {"type": "uint32", "id": 1}, "counter": {"type": "uint32", "id": 2}, "achieved": {"type": "bool", "id": 3}, "rewarded": {"type": "bool", "id": 4}, "achieved_time": {"type": "uint32", "id": 5}}}, "BadgeAchieveProgress": {"fields": {"id": {"type": "uint32", "id": 1}, "counter": {"type": "uint32", "id": 2}, "achieved_counter": {"type": "uint32", "id": 3}, "achieved_time": {"type": "uint32", "id": 4}}}, "AccountStatisticByGameMode": {"fields": {"mode": {"type": "uint32", "id": 1}, "game_count_sum": {"type": "uint32", "id": 2}, "game_final_position": {"rule": "repeated", "type": "uint32", "id": 3}, "fly_count": {"type": "uint32", "id": 4}, "gold_earn_sum": {"type": "float", "id": 5}, "round_count_sum": {"type": "uint32", "id": 6}, "dadian_sum": {"type": "float", "id": 7}, "round_end": {"rule": "repeated", "type": "RoundEndData", "id": 8}, "ming_count_sum": {"type": "uint32", "id": 9}, "liqi_count_sum": {"type": "uint32", "id": 10}, "xun_count_sum": {"type": "uint32", "id": 11}, "highest_lianzhuang": {"type": "uint32", "id": 12}, "score_earn_sum": {"type": "uint32", "id": 13}, "rank_score": {"rule": "repeated", "type": "RankScore", "id": 14}}, "nested": {"RoundEndData": {"fields": {"type": {"type": "uint32", "id": 1}, "sum": {"type": "uint32", "id": 2}}}, "RankScore": {"fields": {"rank": {"type": "uint32", "id": 1}, "score_sum": {"type": "int32", "id": 2}, "count": {"type": "uint32", "id": 3}}}}}, "AccountStatisticByFan": {"fields": {"fan_id": {"type": "uint32", "id": 1}, "sum": {"type": "uint32", "id": 2}}}, "AccountFanAchieved": {"fields": {"mahjong_category": {"type": "uint32", "id": 1}, "fan": {"rule": "repeated", "type": "AccountStatisticByFan", "id": 2}, "liujumanguan": {"type": "uint32", "id": 3}}}, "AccountDetailStatistic": {"fields": {"game_mode": {"rule": "repeated", "type": "AccountStatisticByGameMode", "id": 1}, "fan": {"rule": "repeated", "type": "AccountStatisticByFan", "id": 2}, "liujumanguan": {"type": "uint32", "id": 3}, "fan_achieved": {"rule": "repeated", "type": "AccountFanAchieved", "id": 4}}}, "AccountDetailStatisticByCategory": {"fields": {"category": {"type": "uint32", "id": 1}, "detail_statistic": {"type": "AccountDetailStatistic", "id": 2}}}, "AccountDetailStatisticV2": {"fields": {"friend_room_statistic": {"type": "AccountDetailStatistic", "id": 1}, "rank_statistic": {"type": "RankStatistic", "id": 2}, "customized_contest_statistic": {"type": "CustomizedContestStatistic", "id": 3}, "leisure_match_statistic": {"type": "AccountDetailStatistic", "id": 4}, "challenge_match_statistic": {"type": "ChallengeStatistic", "id": 5}, "activity_match_statistic": {"type": "AccountDetailStatistic", "id": 6}, "ab_match_statistic": {"type": "AccountDetailStatistic", "id": 7}}, "nested": {"RankStatistic": {"fields": {"total_statistic": {"type": "RankData", "id": 1}, "month_statistic": {"type": "RankData", "id": 2}, "month_refresh_time": {"type": "uint32", "id": 3}}, "nested": {"RankData": {"fields": {"all_level_statistic": {"type": "AccountDetailStatistic", "id": 1}, "level_data_list": {"rule": "repeated", "type": "RankLevelData", "id": 2}}, "nested": {"RankLevelData": {"fields": {"rank_level": {"type": "uint32", "id": 1}, "statistic": {"type": "AccountDetailStatistic", "id": 2}}}}}}}, "CustomizedContestStatistic": {"fields": {"total_statistic": {"type": "AccountDetailStatistic", "id": 1}, "month_statistic": {"type": "AccountDetailStatistic", "id": 2}, "month_refresh_time": {"type": "uint32", "id": 3}}}, "ChallengeStatistic": {"fields": {"all_season": {"type": "AccountDetailStatistic", "id": 1}, "season_data_list": {"rule": "repeated", "type": "SeasonData", "id": 2}}, "nested": {"SeasonData": {"fields": {"season_id": {"type": "uint32", "id": 1}, "statistic": {"type": "AccountDetailStatistic", "id": 2}}}}}}}, "AccountShiLian": {"fields": {"step": {"type": "uint32", "id": 1}, "state": {"type": "uint32", "id": 2}}}, "ClientDeviceInfo": {"fields": {"platform": {"type": "string", "id": 1}, "hardware": {"type": "string", "id": 2}, "os": {"type": "string", "id": 3}, "os_version": {"type": "string", "id": 4}, "is_browser": {"type": "bool", "id": 5}, "software": {"type": "string", "id": 6}, "sale_platform": {"type": "string", "id": 7}, "hardware_vendor": {"type": "string", "id": 8}, "model_number": {"type": "string", "id": 9}, "screen_width": {"type": "uint32", "id": 10}, "screen_height": {"type": "uint32", "id": 11}, "user_agent": {"type": "string", "id": 12}, "screen_type": {"type": "uint32", "id": 13}}}, "ClientVersionInfo": {"fields": {"resource": {"type": "string", "id": 1}, "package": {"type": "string", "id": 2}}}, "GamePlayerState": {"values": {"NULL": 0, "AUTH": 1, "SYNCING": 2, "READY": 3}}, "Announcement": {"fields": {"id": {"type": "uint32", "id": 1}, "title": {"type": "string", "id": 2}, "content": {"type": "string", "id": 3}, "header_image": {"type": "string", "id": 4}}}, "TaskProgress": {"fields": {"id": {"type": "uint32", "id": 1}, "counter": {"type": "uint32", "id": 2}, "achieved": {"type": "bool", "id": 3}, "rewarded": {"type": "bool", "id": 4}, "failed": {"type": "bool", "id": 5}, "rewarded_time": {"type": "uint32", "id": 6}}}, "GameConfig": {"fields": {"category": {"type": "uint32", "id": 1}, "mode": {"type": "GameMode", "id": 2}, "meta": {"type": "GameMetaData", "id": 3}}}, "RPGState": {"fields": {"player_damaged": {"type": "uint32", "id": 1}, "monster_damaged": {"type": "uint32", "id": 2}, "monster_seq": {"type": "uint32", "id": 3}}}, "RPGActivity": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "last_show_uuid": {"type": "string", "id": 5}, "last_played_uuid": {"type": "string", "id": 6}, "current_state": {"type": "RPGState", "id": 7}, "last_show_state": {"type": "RPGState", "id": 8}, "received_rewards": {"rule": "repeated", "type": "uint32", "id": 9}, "last_show_id": {"type": "uint32", "id": 10}}}, "ActivityArenaData": {"fields": {"win_count": {"type": "uint32", "id": 1}, "lose_count": {"type": "uint32", "id": 2}, "activity_id": {"type": "uint32", "id": 3}, "enter_time": {"type": "uint32", "id": 4}, "daily_enter_count": {"type": "uint32", "id": 5}, "daily_enter_time": {"type": "uint32", "id": 6}, "max_win_count": {"type": "uint32", "id": 7}, "total_win_count": {"type": "uint32", "id": 8}}}, "FeedActivityData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "feed_count": {"type": "uint32", "id": 2}, "friend_receive_data": {"type": "CountWithTimeData", "id": 3}, "friend_send_data": {"type": "CountWithTimeData", "id": 4}, "gift_inbox": {"rule": "repeated", "type": "GiftBoxData", "id": 5}}, "nested": {"CountWithTimeData": {"fields": {"count": {"type": "uint32", "id": 1}, "last_update_time": {"type": "uint32", "id": 2}}}, "GiftBoxData": {"fields": {"id": {"type": "uint32", "id": 1}, "item_id": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}, "from_account_id": {"type": "uint32", "id": 4}, "time": {"type": "uint32", "id": 5}, "received": {"type": "uint32", "id": 6}}}}}, "SegmentTaskProgress": {"fields": {"id": {"type": "uint32", "id": 1}, "counter": {"type": "uint32", "id": 2}, "achieved": {"type": "bool", "id": 3}, "rewarded": {"type": "bool", "id": 4}, "failed": {"type": "bool", "id": 5}, "reward_count": {"type": "uint32", "id": 6}, "achieved_count": {"type": "uint32", "id": 7}}}, "MineActivityData": {"fields": {"dig_point": {"rule": "repeated", "type": "Point", "id": 1}, "map": {"rule": "repeated", "type": "MineReward", "id": 2}, "id": {"type": "uint32", "id": 3}}}, "AccountActivityUpdate": {"fields": {"mine_data": {"rule": "repeated", "type": "lq.MineActivityData", "id": 1}, "rpg_data": {"rule": "repeated", "type": "lq.RPGActivity", "id": 2}, "feed_data": {"rule": "repeated", "type": "ActivityFeedData", "id": 3}, "spot_data": {"rule": "repeated", "type": "lq.ActivitySpotData", "id": 4}, "friend_gift_data": {"rule": "repeated", "type": "lq.ActivityFriendGiftData", "id": 5}, "upgrade_data": {"rule": "repeated", "type": "lq.ActivityUpgradeData", "id": 6}, "gacha_data": {"rule": "repeated", "type": "lq.ActivityGachaUpdateData", "id": 7}, "simulation_data": {"rule": "repeated", "type": "lq.ActivitySimulationData", "id": 8}, "combining_data": {"rule": "repeated", "type": "ActivityCombiningLQData", "id": 9}, "village_data": {"rule": "repeated", "type": "lq.ActivityVillageData", "id": 10}, "festival_data": {"rule": "repeated", "type": "lq.ActivityFestivalData", "id": 11}, "island_data": {"rule": "repeated", "type": "lq.ActivityIslandData", "id": 12}, "story_data": {"rule": "repeated", "type": "lq.ActivityStoryData", "id": 14}, "choose_up_data": {"rule": "repeated", "type": "lq.ActivityChooseUpData", "id": 15}, "simulation_v2_data": {"rule": "repeated", "type": "lq.SimulationV2Data", "id": 16}}}, "ActivityCombiningWorkbench": {"fields": {"craft_id": {"type": "uint32", "id": 1}, "pos": {"type": "uint32", "id": 2}}}, "ActivityCombiningMenuData": {"fields": {"menu_group": {"type": "uint32", "id": 1}, "generated": {"rule": "repeated", "type": "MenuRequire", "id": 2}, "multi_generated": {"rule": "repeated", "type": "MenuRequire", "id": 3}}, "nested": {"MenuRequire": {"fields": {"level": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ActivityCombiningOrderData": {"fields": {"id": {"type": "uint32", "id": 1}, "pos": {"type": "uint32", "id": 2}, "unlock_day": {"type": "uint32", "id": 4}, "char_id": {"type": "uint32", "id": 5}, "finished_craft_id": {"rule": "repeated", "type": "uint32", "id": 6}, "craft_id": {"rule": "repeated", "type": "uint32", "id": 7}}}, "ActivityCombiningLQData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "workbench": {"rule": "repeated", "type": "ActivityCombiningWorkbench", "id": 2}, "orders": {"rule": "repeated", "type": "ActivityCombiningOrderData", "id": 3}, "recycle_bin": {"type": "ActivityCombiningWorkbench", "id": 4}, "unlocked_craft": {"rule": "repeated", "type": "uint32", "id": 5}, "daily_bonus_count": {"type": "uint32", "id": 6}}}, "ActivityCombiningPoolData": {"fields": {"group": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ActivityCombiningData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "workbench": {"rule": "repeated", "type": "ActivityCombiningWorkbench", "id": 2}, "orders": {"rule": "repeated", "type": "ActivityCombiningOrderData", "id": 3}, "recycle_bin": {"type": "ActivityCombiningWorkbench", "id": 4}, "menu": {"type": "ActivityCombiningMenuData", "id": 5}, "current_order_id": {"type": "uint32", "id": 6}, "bonus": {"type": "BonusData", "id": 7}, "unlocked_craft": {"rule": "repeated", "type": "uint32", "id": 8}, "craft_pool": {"rule": "repeated", "type": "ActivityCombiningPoolData", "id": 9}, "order_pool": {"rule": "repeated", "type": "ActivityCombiningPoolData", "id": 10}}, "nested": {"BonusData": {"fields": {"count": {"type": "uint32", "id": 1}, "update_time": {"type": "uint32", "id": 2}}}}}, "VillageReward": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "VillageBuildingData": {"fields": {"id": {"type": "uint32", "id": 1}, "reward": {"rule": "repeated", "type": "VillageReward", "id": 3}, "workers": {"rule": "repeated", "type": "uint32", "id": 4}}}, "VillageTripData": {"fields": {"start_round": {"type": "uint32", "id": 1}, "dest_id": {"type": "uint32", "id": 2}, "reward": {"rule": "repeated", "type": "VillageReward", "id": 3}, "level": {"type": "uint32", "id": 4}, "info": {"type": "VillageTargetInfo", "id": 5}}}, "VillageTaskData": {"fields": {"id": {"type": "uint32", "id": 1}, "completed_count": {"type": "uint32", "id": 2}}}, "VillageTargetInfo": {"fields": {"nickname": {"type": "string", "id": 1}, "avatar": {"type": "uint32", "id": 2}, "avatar_frame": {"type": "uint32", "id": 3}, "title": {"type": "uint32", "id": 4}, "verified": {"type": "uint32", "id": 5}}}, "ActivityVillageData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "buildings": {"rule": "repeated", "type": "VillageBuildingData", "id": 2}, "trip": {"rule": "repeated", "type": "VillageTripData", "id": 3}, "tasks": {"rule": "repeated", "type": "VillageTaskData", "id": 6}, "round": {"type": "uint32", "id": 7}}}, "TimeCounterData": {"fields": {"count": {"type": "uint32", "id": 1}, "update_time": {"type": "uint32", "id": 2}}}, "SignedTimeCounterData": {"fields": {"count": {"type": "int32", "id": 1}, "update_time": {"type": "uint32", "id": 2}}}, "FestivalProposalData": {"fields": {"id": {"type": "uint32", "id": 1}, "proposal_id": {"type": "uint32", "id": 2}, "pos": {"type": "uint32", "id": 3}}}, "ActivityFestivalData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}, "proposal_list": {"rule": "repeated", "type": "FestivalProposalData", "id": 3}, "event_list": {"rule": "repeated", "type": "uint32", "id": 4}, "buy_record": {"type": "SignedTimeCounterData", "id": 5}}}, "SimulationV2Data": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "season": {"type": "lq.SimulationV2SeasonData", "id": 2}, "highest_score": {"type": "int32", "id": 3}, "upgrade": {"type": "lq.SimulationV2Ability", "id": 4}, "event_pool": {"rule": "repeated", "type": "uint32", "id": 5}, "season_count": {"type": "uint32", "id": 6}}}, "IslandBagItemData": {"fields": {"id": {"type": "uint32", "id": 1}, "pos": {"rule": "repeated", "type": "uint32", "id": 2}, "rotate": {"type": "uint32", "id": 3}, "goods_id": {"type": "uint32", "id": 4}, "price": {"type": "uint32", "id": 5}}}, "IslandBagData": {"fields": {"id": {"type": "uint32", "id": 1}, "matrix": {"type": "string", "id": 2}, "items": {"rule": "repeated", "type": "IslandBagItemData", "id": 3}}}, "IslandGoodsData": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "count": {"type": "int32", "id": 2}, "update_time": {"type": "uint32", "id": 3}}}, "IslandZoneData": {"fields": {"id": {"type": "uint32", "id": 1}, "currency_used": {"type": "SignedTimeCounterData", "id": 2}, "goods_records": {"rule": "repeated", "type": "IslandGoodsData", "id": 3}}}, "ActivityIslandData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "zone": {"type": "uint32", "id": 2}, "bags": {"rule": "repeated", "type": "IslandBagData", "id": 3}, "zones": {"rule": "repeated", "type": "IslandZoneData", "id": 4}}}, "AmuletEffectData": {"fields": {"id": {"type": "uint32", "id": 1}, "uid": {"type": "uint32", "id": 2}, "store": {"rule": "repeated", "type": "int64", "id": 3}}}, "AmuletBuffData": {"fields": {"id": {"type": "uint32", "id": 1}, "store": {"rule": "repeated", "type": "int64", "id": 3}}}, "AmuletGameShopGoods": {"fields": {"id": {"type": "uint32", "id": 1}, "sold": {"type": "bool", "id": 2}, "goods_id": {"type": "uint32", "id": 3}, "price": {"type": "uint32", "id": 4}}}, "AmuletActivityTingInfo": {"fields": {"tile": {"type": "string", "id": 1}, "fan": {"type": "uint64", "id": 2}, "ting_tile": {"type": "string", "id": 3}}}, "AmuletShowDesktopTileData": {"fields": {"id": {"type": "uint32", "id": 1}, "pos": {"type": "uint32", "id": 2}}}, "AmuletGameOperation": {"fields": {"type": {"type": "uint32", "id": 1}, "gang": {"rule": "repeated", "type": "GangTiles", "id": 2}, "effect_id": {"type": "uint32", "id": 3}}, "nested": {"GangTiles": {"fields": {"tiles": {"rule": "repeated", "type": "uint32", "id": 1}}}}}, "AmuletGameShopData": {"fields": {"goods": {"rule": "repeated", "type": "AmuletGameShopGoods", "id": 1}, "effect_list": {"rule": "repeated", "type": "uint32", "id": 2}, "shop_refresh_count": {"type": "uint32", "id": 3}, "refresh_price": {"type": "uint32", "id": 4}, "next_goods_id": {"type": "uint32", "id": 5}}}, "AmuletGameUpdateData": {"fields": {"tile_replace": {"rule": "repeated", "type": "AmuletTile", "id": 1}, "tian_dora": {"rule": "repeated", "type": "string", "id": 2}, "dora": {"rule": "repeated", "type": "uint32", "id": 4}, "hands": {"rule": "repeated", "type": "uint32", "id": 7}, "ming": {"rule": "repeated", "type": "AmuletMingInfo", "id": 8}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 9}, "buff_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 10}, "point": {"type": "string", "id": 13}, "coin": {"type": "uint32", "id": 14}, "stage": {"type": "uint32", "id": 22}, "desktop_remain": {"type": "uint32", "id": 26}, "show_desktop_tiles": {"rule": "repeated", "type": "AmuletShowDesktopTileData", "id": 28}, "ting_list": {"rule": "repeated", "type": "AmuletActivityTingInfo", "id": 30}, "next_operation": {"rule": "repeated", "type": "AmuletGameOperation", "id": 31}, "used_desktop": {"rule": "repeated", "type": "uint32", "id": 34}, "highest_hu": {"type": "ActivityAmuletHuRecord", "id": 35}, "records": {"type": "ActivityAmuletRecord", "id": 36}, "reward_pack": {"rule": "repeated", "type": "uint32", "id": 37}, "reward_effect": {"rule": "repeated", "type": "uint32", "id": 38}, "tile_score": {"rule": "repeated", "type": "AmuletGameTileScoreData", "id": 43}, "reward_pack_id": {"type": "uint32", "id": 47}}}, "AmuletGameRecordData": {"fields": {"key": {"type": "uint32", "id": 1}, "int_value": {"type": "int32", "id": 2}, "str_value": {"type": "string", "id": 3}, "int_arr_value": {"rule": "repeated", "type": "int32", "id": 4}}}, "AmuletGameTileScoreData": {"fields": {"tile": {"type": "string", "id": 1}, "score": {"type": "string", "id": 2}}}, "AmuletGameData": {"fields": {"pool": {"rule": "repeated", "type": "AmuletTile", "id": 1}, "tile_replace": {"rule": "repeated", "type": "AmuletTile", "id": 2}, "tian_dora": {"rule": "repeated", "type": "string", "id": 3}, "mountain": {"rule": "repeated", "type": "uint32", "id": 4}, "dora": {"rule": "repeated", "type": "uint32", "id": 5}, "hands": {"rule": "repeated", "type": "uint32", "id": 7}, "ming": {"rule": "repeated", "type": "AmuletMingInfo", "id": 8}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 9}, "buff_list": {"rule": "repeated", "type": "AmuletBuffData", "id": 10}, "level": {"type": "uint32", "id": 11}, "point": {"type": "string", "id": 13}, "coin": {"type": "uint32", "id": 14}, "shop": {"type": "AmuletGameShopData", "id": 16}, "used": {"rule": "repeated", "type": "uint32", "id": 20}, "boss_buff": {"rule": "repeated", "type": "uint32", "id": 21}, "stage": {"type": "uint32", "id": 22}, "desktop": {"rule": "repeated", "type": "uint32", "id": 24}, "show_desktop": {"rule": "repeated", "type": "uint32", "id": 25}, "desktop_remain": {"type": "uint32", "id": 26}, "free_effect_list": {"rule": "repeated", "type": "uint32", "id": 27}, "show_desktop_tiles": {"rule": "repeated", "type": "AmuletShowDesktopTileData", "id": 28}, "change_tile_count": {"type": "uint32", "id": 29}, "ting_list": {"rule": "repeated", "type": "AmuletActivityTingInfo", "id": 30}, "next_operation": {"rule": "repeated", "type": "AmuletGameOperation", "id": 31}, "shop_buff_list": {"rule": "repeated", "type": "AmuletBuffData", "id": 32}, "remain_change_tile_count": {"type": "int32", "id": 33}, "used_desktop": {"rule": "repeated", "type": "uint32", "id": 34}, "after_gang": {"type": "uint32", "id": 35}, "record_data": {"rule": "repeated", "type": "AmuletGameRecordData", "id": 36}, "skill_buff_list": {"rule": "repeated", "type": "AmuletBuffData", "id": 37}, "max_effect_count": {"type": "uint32", "id": 38}, "highest_hu": {"type": "ActivityAmuletHuRecord", "id": 39}, "total_consumed_coin": {"type": "uint32", "id": 40}, "boss_buff_id": {"rule": "repeated", "type": "uint32", "id": 41}, "locked_tile": {"rule": "repeated", "type": "uint32", "id": 42}, "tile_score": {"rule": "repeated", "type": "AmuletGameTileScoreData", "id": 43}, "locked_tile_count": {"type": "uint32", "id": 44}, "reward_pack": {"rule": "repeated", "type": "uint32", "id": 45}, "reward_effect": {"rule": "repeated", "type": "uint32", "id": 46}, "reward_pack_id": {"type": "uint32", "id": 47}, "total_change_tile_count": {"type": "uint32", "id": 48}}}, "ActivityAmuletUpdateData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "game_update": {"type": "AmuletGameUpdateData", "id": 2}, "game_empty": {"type": "bool", "id": 3}}}, "AmuletSkillData": {"fields": {"id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}}}, "ActivityAmuletUpgradeData": {"fields": {"skill": {"rule": "repeated", "type": "AmuletSkillData", "id": 2}}}, "ActivityAmuletRecord": {"fields": {"effect_gain_count": {"type": "uint32", "id": 1}, "hu_count": {"type": "uint32", "id": 2}}}, "ActivityAmuletHuRecord": {"fields": {"point": {"type": "string", "id": 1}, "pai": {"type": "string", "id": 2}, "fan": {"type": "string", "id": 3}, "base": {"type": "string", "id": 4}}}, "ActivityAmuletIllustratedBookData": {"fields": {"effect_collection": {"rule": "repeated", "type": "uint32", "id": 1}, "highest_hu": {"type": "ActivityAmuletHuRecord", "id": 2}, "highest_level": {"type": "uint32", "id": 3}}}, "ActivityAmuletTaskData": {"fields": {"progress": {"rule": "repeated", "type": "lq.TaskProgress", "id": 1}}}, "ActivityAmuletData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "game": {"type": "AmuletGameData", "id": 2}, "version": {"type": "uint32", "id": 3}, "upgrade": {"type": "ActivityAmuletUpgradeData", "id": 4}, "illustrated_book": {"type": "ActivityAmuletIllustratedBookData", "id": 5}, "task": {"type": "ActivityAmuletTaskData", "id": 6}}}, "ActivityFeedData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "feed_count": {"type": "uint32", "id": 2}, "friend_receive_data": {"type": "CountWithTimeData", "id": 3}, "friend_send_data": {"type": "CountWithTimeData", "id": 4}, "gift_inbox": {"rule": "repeated", "type": "GiftBoxData", "id": 5}, "max_inbox_id": {"type": "uint32", "id": 6}}, "nested": {"CountWithTimeData": {"fields": {"count": {"type": "uint32", "id": 1}, "last_update_time": {"type": "uint32", "id": 2}}}, "GiftBoxData": {"fields": {"id": {"type": "uint32", "id": 1}, "item_id": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}, "from_account_id": {"type": "uint32", "id": 4}, "time": {"type": "uint32", "id": 5}, "received": {"type": "uint32", "id": 6}}}}}, "UnlockedStoryData": {"fields": {"story_id": {"type": "uint32", "id": 1}, "finished_ending": {"rule": "repeated", "type": "uint32", "id": 2}, "rewarded_ending": {"rule": "repeated", "type": "uint32", "id": 3}, "finish_rewarded": {"type": "uint32", "id": 4}, "all_finish_rewarded": {"type": "uint32", "id": 5}}}, "ActivityStoryData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "unlocked_story": {"rule": "repeated", "type": "UnlockedStoryData", "id": 2}}}, "ActivityChooseUpData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "chest_id": {"type": "uint32", "id": 2}, "selection": {"type": "uint32", "id": 3}, "is_end": {"type": "uint32", "id": 4}}}, "ActivityFriendGiftData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "max_inbox_id": {"type": "uint32", "id": 2}, "receive_data": {"type": "CountWithTimeData", "id": 3}, "send_data": {"type": "CountWithTimeData", "id": 4}, "gift_inbox": {"rule": "repeated", "type": "GiftBoxData", "id": 5}}, "nested": {"CountWithTimeData": {"fields": {"count": {"type": "uint32", "id": 1}, "last_update_time": {"type": "uint32", "id": 2}, "send_friend_id": {"rule": "repeated", "type": "uint32", "id": 3}}}, "GiftBoxData": {"fields": {"id": {"type": "uint32", "id": 1}, "item_id": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}, "from_account_id": {"type": "uint32", "id": 4}, "time": {"type": "uint32", "id": 5}, "received": {"type": "uint32", "id": 6}}}}}, "ActivityUpgradeData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "groups": {"rule": "repeated", "type": "LevelGroup", "id": 2}, "received_level": {"type": "uint32", "id": 3}}, "nested": {"LevelGroup": {"fields": {"group_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}}}}}, "GachaRecord": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ActivityGachaData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "gained": {"rule": "repeated", "type": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 2}}}, "ActivityGachaUpdateData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "gained": {"rule": "repeated", "type": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 2}, "remain_count": {"type": "uint32", "id": 3}}}, "ActivitySimulationGameRecordMessage": {"fields": {"type": {"type": "uint32", "id": 1}, "args": {"rule": "repeated", "type": "uint32", "id": 2}, "xun": {"type": "uint32", "id": 3}}}, "ActivitySimulationGameRecord": {"fields": {"round": {"type": "uint32", "id": 1}, "seats": {"rule": "repeated", "type": "uint32", "id": 2}, "uuid": {"type": "string", "id": 3}, "start_time": {"type": "uint32", "id": 4}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "messages": {"rule": "repeated", "type": "ActivitySimulationGameRecordMessage", "id": 6}}}, "ActivitySimulationDailyContest": {"fields": {"day": {"type": "uint32", "id": 1}, "characters": {"rule": "repeated", "type": "uint32", "id": 2}, "records": {"rule": "repeated", "type": "ActivitySimulationGameRecord", "id": 3}, "round": {"type": "uint32", "id": 4}}}, "ActivitySimulationTrainRecord": {"fields": {"time": {"type": "uint32", "id": 1}, "modify_stats": {"rule": "repeated", "type": "int32", "id": 2}, "final_stats": {"rule": "repeated", "type": "uint32", "id": 3}, "type": {"type": "uint32", "id": 4}}}, "ActivitySimulationData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "stats": {"rule": "repeated", "type": "uint32", "id": 2}, "stamina_update_time": {"type": "uint32", "id": 3}, "daily_contest": {"rule": "repeated", "type": "ActivitySimulationDailyContest", "id": 4}, "train_records": {"rule": "repeated", "type": "ActivitySimulationTrainRecord", "id": 5}}}, "ActivitySpotData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "spots": {"rule": "repeated", "type": "SpotData", "id": 3}}, "nested": {"SpotData": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "rewarded": {"type": "uint32", "id": 2}, "unlocked_ending": {"rule": "repeated", "type": "uint32", "id": 3}, "unlocked": {"type": "uint32", "id": 4}}}}}, "AccountActiveState": {"fields": {"account_id": {"type": "uint32", "id": 1}, "login_time": {"type": "uint32", "id": 2}, "logout_time": {"type": "uint32", "id": 3}, "is_online": {"type": "bool", "id": 4}, "playing": {"type": "AccountPlayingGame", "id": 5}}}, "Friend": {"fields": {"base": {"type": "PlayerBaseView", "id": 1}, "state": {"type": "AccountActiveState", "id": 2}, "remark": {"type": "string", "id": 3}}}, "Point": {"fields": {"x": {"type": "uint32", "id": 1}, "y": {"type": "uint32", "id": 2}}}, "MineReward": {"fields": {"point": {"type": "Point", "id": 1}, "reward_id": {"type": "uint32", "id": 2}, "received": {"type": "bool", "id": 3}}}, "GameLiveUnit": {"fields": {"timestamp": {"type": "uint32", "id": 1}, "action_category": {"type": "uint32", "id": 2}, "action_data": {"type": "bytes", "id": 3}}}, "GameLiveSegment": {"fields": {"actions": {"rule": "repeated", "type": "GameLiveUnit", "id": 1}}}, "GameLiveSegmentUri": {"fields": {"segment_id": {"type": "uint32", "id": 1}, "segment_uri": {"type": "string", "id": 2}}}, "GameLiveHead": {"fields": {"uuid": {"type": "string", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "game_config": {"type": "GameConfig", "id": 3}, "players": {"rule": "repeated", "type": "PlayerGameView", "id": 4}, "seat_list": {"rule": "repeated", "type": "uint32", "id": 5}}}, "GameNewRoundState": {"fields": {"seat_states": {"rule": "repeated", "type": "uint32", "id": 1}}}, "GameEndAction": {"fields": {"state": {"type": "uint32", "id": 1}}}, "GameNoopAction": {"fields": {}}, "CommentItem": {"fields": {"comment_id": {"type": "uint32", "id": 1}, "timestamp": {"type": "uint32", "id": 2}, "commenter": {"type": "PlayerBaseView", "id": 3}, "content": {"type": "string", "id": 4}, "is_banned": {"type": "uint32", "id": 5}}}, "RollingNotice": {"fields": {"content": {"type": "string", "id": 2}, "start_time": {"type": "uint32", "id": 3}, "end_time": {"type": "uint32", "id": 4}, "repeat_interval": {"type": "uint32", "id": 5}, "repeat_time": {"rule": "repeated", "type": "uint32", "id": 7}, "repeat_type": {"type": "uint32", "id": 8}}}, "MaintainNotice": {"fields": {"maintain_time": {"type": "uint32", "id": 1}}}, "BillingGoods": {"fields": {"id": {"type": "string", "id": 1}, "name": {"type": "string", "id": 2}, "desc": {"type": "string", "id": 3}, "icon": {"type": "string", "id": 4}, "resource_id": {"type": "uint32", "id": 5}, "resource_count": {"type": "uint32", "id": 6}}}, "BillShortcut": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "dealPrice": {"type": "uint32", "id": 3}}}, "BillingProduct": {"fields": {"goods": {"type": "BillingGoods", "id": 1}, "currency_code": {"type": "string", "id": 2}, "currency_price": {"type": "uint32", "id": 3}, "sort_weight": {"type": "uint32", "id": 4}}}, "Character": {"fields": {"charid": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}, "exp": {"type": "uint32", "id": 3}, "views": {"rule": "repeated", "type": "ViewSlot", "id": 4}, "skin": {"type": "uint32", "id": 5}, "is_upgraded": {"type": "bool", "id": 6}, "extra_emoji": {"rule": "repeated", "type": "uint32", "id": 7}, "rewarded_level": {"rule": "repeated", "type": "uint32", "id": 8}}}, "BuyRecord": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ZHPShop": {"fields": {"goods": {"rule": "repeated", "type": "uint32", "id": 1}, "buy_records": {"rule": "repeated", "type": "BuyRecord", "id": 2}, "free_refresh": {"type": "RefreshCount", "id": 3}, "cost_refresh": {"type": "RefreshCount", "id": 4}}, "nested": {"RefreshCount": {"fields": {"count": {"type": "uint32", "id": 1}, "limit": {"type": "uint32", "id": 2}}}}}, "MonthTicketInfo": {"fields": {"id": {"type": "uint32", "id": 1}, "end_time": {"type": "uint32", "id": 2}, "last_pay_time": {"type": "uint32", "id": 3}}}, "ShopInfo": {"fields": {"zhp": {"type": "ZHPShop", "id": 1}, "buy_records": {"rule": "repeated", "type": "BuyRecord", "id": 2}, "last_refresh_time": {"type": "uint32", "id": 3}}}, "ChangeNicknameRecord": {"fields": {"from": {"type": "string", "id": 1}, "to": {"type": "string", "id": 2}, "time": {"type": "uint32", "id": 3}}}, "ServerSettings": {"fields": {"payment_setting": {"type": "PaymentSetting", "id": 3}, "payment_setting_v2": {"type": "PaymentSettingV2", "id": 4}, "nickname_setting": {"type": "NicknameSetting", "id": 5}}}, "NicknameSetting": {"fields": {"enable": {"type": "uint32", "id": 1}, "nicknames": {"rule": "repeated", "type": "string", "id": 2}}}, "PaymentSettingV2": {"fields": {"open_payment": {"type": "uint32", "id": 1}, "payment_platforms": {"rule": "repeated", "type": "PaymentSettingUnit", "id": 2}}, "nested": {"PaymentMaintain": {"fields": {"start_time": {"type": "uint32", "id": 1}, "end_time": {"type": "uint32", "id": 2}, "goods_click_action": {"type": "uint32", "id": 3}, "goods_click_text": {"type": "string", "id": 4}, "enabled_channel": {"rule": "repeated", "type": "string", "id": 5}}}, "PaymentSettingUnit": {"fields": {"platform": {"type": "string", "id": 1}, "is_show": {"type": "bool", "id": 2}, "goods_click_action": {"type": "uint32", "id": 3}, "goods_click_text": {"type": "string", "id": 4}, "maintain": {"type": "PaymentMaintain", "id": 5}, "enable_for_frozen_account": {"type": "bool", "id": 6}, "extra_data": {"type": "string", "id": 7}, "enabled_channel": {"rule": "repeated", "type": "string", "id": 8}}}}}, "PaymentSetting": {"fields": {"open_payment": {"type": "uint32", "id": 1}, "payment_info_show_type": {"type": "uint32", "id": 2}, "payment_info": {"type": "string", "id": 3}, "wechat": {"type": "WechatData", "id": 4}, "alipay": {"type": "AlipayData", "id": 5}}, "nested": {"WechatData": {"fields": {"disable_create": {"type": "bool", "id": 1}, "payment_source_platform": {"type": "uint32", "id": 2}, "enable_credit": {"type": "bool", "id": 3}}}, "AlipayData": {"fields": {"disable_create": {"type": "bool", "id": 1}, "payment_source_platform": {"type": "uint32", "id": 2}}}}}, "AccountSetting": {"fields": {"key": {"type": "uint32", "id": 1}, "value": {"type": "uint32", "id": 2}}}, "ChestData": {"fields": {"chest_id": {"type": "uint32", "id": 1}, "total_open_count": {"type": "uint32", "id": 2}, "consume_count": {"type": "uint32", "id": 3}, "face_black_count": {"type": "uint32", "id": 4}}}, "ChestDataV2": {"fields": {"chest_id": {"type": "uint32", "id": 1}, "total_open_count": {"type": "uint32", "id": 2}, "face_black_count": {"type": "uint32", "id": 3}, "ticket_face_black_count": {"type": "uint32", "id": 4}}}, "FaithData": {"fields": {"faith_id": {"type": "uint32", "id": 1}, "total_open_count": {"type": "uint32", "id": 2}, "consume_count": {"type": "uint32", "id": 3}, "modify_count": {"type": "int32", "id": 4}}}, "CustomizedContestBase": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "contest_id": {"type": "uint32", "id": 2}, "contest_name": {"type": "string", "id": 3}, "state": {"type": "uint32", "id": 4}, "creator_id": {"type": "uint32", "id": 5}, "create_time": {"type": "uint32", "id": 6}, "start_time": {"type": "uint32", "id": 7}, "finish_time": {"type": "uint32", "id": 8}, "open": {"type": "bool", "id": 9}, "contest_type": {"type": "uint32", "id": 10}, "public_notice": {"type": "string", "id": 11}, "check_state": {"type": "uint32", "id": 12}, "checking_name": {"type": "string", "id": 13}}}, "CustomizedContestExtend": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "public_notice": {"type": "string", "id": 2}}}, "CustomizedContestAbstract": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "contest_id": {"type": "uint32", "id": 2}, "contest_name": {"type": "string", "id": 3}, "state": {"type": "uint32", "id": 4}, "creator_id": {"type": "uint32", "id": 5}, "create_time": {"type": "uint32", "id": 6}, "start_time": {"type": "uint32", "id": 7}, "finish_time": {"type": "uint32", "id": 8}, "open": {"type": "bool", "id": 9}, "public_notice": {"type": "string", "id": 10}, "contest_type": {"type": "uint32", "id": 11}}}, "CustomizedContestDetail": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "contest_id": {"type": "uint32", "id": 2}, "contest_name": {"type": "string", "id": 3}, "state": {"type": "uint32", "id": 4}, "creator_id": {"type": "uint32", "id": 5}, "create_time": {"type": "uint32", "id": 6}, "start_time": {"type": "uint32", "id": 7}, "finish_time": {"type": "uint32", "id": 8}, "open": {"type": "bool", "id": 9}, "rank_rule": {"type": "uint32", "id": 10}, "game_mode": {"type": "GameMode", "id": 11}, "private_notice": {"type": "string", "id": 12}, "observer_switch": {"type": "uint32", "id": 13}, "emoji_switch": {"type": "uint32", "id": 14}, "contest_type": {"type": "uint32", "id": 15}, "disable_broadcast": {"type": "uint32", "id": 16}, "signup_start_time": {"type": "uint32", "id": 17}, "signup_end_time": {"type": "uint32", "id": 18}, "signup_type": {"type": "uint32", "id": 19}, "auto_match": {"type": "uint32", "id": 20}}}, "CustomizedContestPlayerReport": {"fields": {"rank_rule": {"type": "uint32", "id": 1}, "rank": {"type": "uint32", "id": 2}, "point": {"type": "int32", "id": 3}, "game_ranks": {"rule": "repeated", "type": "uint32", "id": 4}, "total_game_count": {"type": "uint32", "id": 5}}}, "RecordGame": {"fields": {"uuid": {"type": "string", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "end_time": {"type": "uint32", "id": 3}, "config": {"type": "GameConfig", "id": 5}, "accounts": {"rule": "repeated", "type": "AccountInfo", "id": 11}, "result": {"type": "GameEndResult", "id": 12}, "robots": {"rule": "repeated", "type": "AccountInfo", "id": 13}, "standard_rule": {"type": "uint32", "id": 14}}, "nested": {"AccountInfo": {"fields": {"account_id": {"type": "uint32", "id": 1}, "seat": {"type": "uint32", "id": 2}, "nickname": {"type": "string", "id": 3}, "avatar_id": {"type": "uint32", "id": 4}, "character": {"type": "Character", "id": 5}, "title": {"type": "uint32", "id": 6}, "level": {"type": "AccountLevel", "id": 7}, "level3": {"type": "AccountLevel", "id": 8}, "avatar_frame": {"type": "uint32", "id": 9}, "verified": {"type": "uint32", "id": 10}, "views": {"rule": "repeated", "type": "ViewSlot", "id": 11}}}}}, "RecordListEntry": {"fields": {"version": {"type": "uint32", "id": 1}, "uuid": {"type": "string", "id": 2}, "start_time": {"type": "uint32", "id": 3}, "end_time": {"type": "uint32", "id": 4}, "tag": {"type": "uint32", "id": 5}, "subtag": {"type": "uint32", "id": 6}, "players": {"rule": "repeated", "type": "RecordPlayerResult", "id": 7}, "standard_rule": {"type": "uint32", "id": 8}}}, "RecordPlayerResult": {"fields": {"rank": {"type": "uint32", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "nickname": {"type": "string", "id": 3}, "level": {"type": "AccountLevel", "id": 4}, "level3": {"type": "AccountLevel", "id": 5}, "seat": {"type": "uint32", "id": 6}, "pt": {"type": "int32", "id": 7}, "point": {"type": "int32", "id": 8}, "max_hu_type": {"type": "uint32", "id": 9}, "action_liqi": {"type": "uint32", "id": 10}, "action_rong": {"type": "uint32", "id": 11}, "action_zimo": {"type": "uint32", "id": 12}, "action_chong": {"type": "uint32", "id": 13}, "verified": {"type": "uint32", "id": 14}}}, "CustomizedContestGameStart": {"fields": {"players": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 1}}, "nested": {"Item": {"fields": {"account_id": {"type": "uint32", "id": 1}, "nickname": {"type": "string", "id": 2}}}}}, "CustomizedContestGameEnd": {"fields": {"players": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 1}}, "nested": {"Item": {"fields": {"account_id": {"type": "uint32", "id": 1}, "nickname": {"type": "string", "id": 2}, "total_point": {"type": "int32", "id": 3}}}}}, "Activity": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "end_time": {"type": "uint32", "id": 3}, "type": {"type": "string", "id": 4}}}, "ExchangeRecord": {"fields": {"exchange_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ActivityAccumulatedPointData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "point": {"type": "int32", "id": 2}, "gained_reward_list": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ActivityRankPointData": {"fields": {"leaderboard_id": {"type": "uint32", "id": 1}, "point": {"type": "int32", "id": 2}, "gained_reward": {"type": "bool", "id": 3}, "gainable_time": {"type": "uint32", "id": 4}}}, "GameRoundHuData": {"fields": {"hupai": {"type": "HuPai", "id": 1}, "fans": {"rule": "repeated", "type": "Fan", "id": 2}, "score": {"type": "uint32", "id": 3}, "xun": {"type": "uint32", "id": 4}, "title_id": {"type": "uint32", "id": 5}, "fan_sum": {"type": "uint32", "id": 6}, "fu_sum": {"type": "uint32", "id": 7}, "yakuman_count": {"type": "uint32", "id": 8}, "biao_dora_count": {"type": "uint32", "id": 9}, "red_dora_count": {"type": "uint32", "id": 10}, "li_dora_count": {"type": "uint32", "id": 11}, "babei_count": {"type": "uint32", "id": 12}, "xuan_shang_count": {"type": "uint32", "id": 13}, "pai_left_count": {"type": "uint32", "id": 14}}, "nested": {"HuPai": {"fields": {"tile": {"type": "string", "id": 1}, "seat": {"type": "uint32", "id": 2}, "liqi": {"type": "uint32", "id": 3}}}, "Fan": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "fan": {"type": "uint32", "id": 3}}}}}, "GameRoundPlayerFangChongInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}, "pai_left_count": {"type": "uint32", "id": 3}}}, "GameRoundPlayerResult": {"fields": {"type": {"type": "uint32", "id": 1}, "hands": {"rule": "repeated", "type": "string", "id": 2}, "ming": {"rule": "repeated", "type": "string", "id": 3}, "liqi_type": {"type": "uint32", "id": 4}, "is_fulu": {"type": "bool", "id": 5}, "is_liujumanguan": {"type": "bool", "id": 6}, "lian_zhuang": {"type": "uint32", "id": 7}, "hu": {"type": "GameRoundHuData", "id": 8}, "fangchongs": {"rule": "repeated", "type": "GameRoundPlayerFangChongInfo", "id": 9}, "liqi_fangchong": {"type": "bool", "id": 10}, "liqi_failed": {"type": "bool", "id": 11}}}, "GameRoundPlayer": {"fields": {"score": {"type": "int32", "id": 1}, "rank": {"type": "uint32", "id": 2}, "result": {"type": "GameRoundPlayerResult", "id": 3}}}, "GameRoundSnapshot": {"fields": {"ju": {"type": "uint32", "id": 1}, "ben": {"type": "uint32", "id": 2}, "players": {"rule": "repeated", "type": "GameRoundPlayer", "id": 3}}}, "GameFinalSnapshot": {"fields": {"uuid": {"type": "string", "id": 1}, "state": {"type": "uint32", "id": 2}, "category": {"type": "uint32", "id": 3}, "mode": {"type": "GameMode", "id": 4}, "meta": {"type": "GameMetaData", "id": 5}, "calculate_param": {"type": "CalculateParam", "id": 6}, "create_time": {"type": "uint32", "id": 7}, "start_time": {"type": "uint32", "id": 8}, "finish_time": {"type": "uint32", "id": 9}, "seats": {"rule": "repeated", "type": "GameSeat", "id": 10}, "rounds": {"rule": "repeated", "type": "GameRoundSnapshot", "id": 11}, "account_views": {"rule": "repeated", "type": "PlayerGameView", "id": 12}, "final_players": {"rule": "repeated", "type": "FinalPlayer", "id": 13}, "afk_info": {"rule": "repeated", "type": "AFKInfo", "id": 14}, "robot_views": {"rule": "repeated", "type": "PlayerGameView", "id": 15}}, "nested": {"CalculateParam": {"fields": {"init_point": {"type": "uint32", "id": 1}, "jingsuanyuandian": {"type": "uint32", "id": 2}, "rank_points": {"rule": "repeated", "type": "int32", "id": 3}}}, "GameSeat": {"fields": {"type": {"type": "uint32", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "notify_endpoint": {"type": "NetworkEndpoint", "id": 3}, "client_address": {"type": "string", "id": 4}, "is_connected": {"type": "bool", "id": 5}}}, "FinalPlayer": {"fields": {"seat": {"type": "uint32", "id": 1}, "total_point": {"type": "int32", "id": 2}, "part_point_1": {"type": "int32", "id": 3}, "part_point_2": {"type": "int32", "id": 4}, "grading_score": {"type": "int32", "id": 5}, "gold": {"type": "int32", "id": 6}}}, "AFKInfo": {"fields": {"deal_tile_count": {"type": "uint32", "id": 1}, "moqie_count": {"type": "uint32", "id": 2}, "seat": {"type": "uint32", "id": 3}}}}}, "RecordCollectedData": {"fields": {"uuid": {"type": "string", "id": 1}, "remarks": {"type": "string", "id": 2}, "start_time": {"type": "uint32", "id": 3}, "end_time": {"type": "uint32", "id": 4}}}, "ContestDetailRule": {"fields": {"init_point": {"type": "uint32", "id": 5}, "fandian": {"type": "uint32", "id": 6}, "can_jifei": {"type": "bool", "id": 7}, "tianbian_value": {"type": "uint32", "id": 8}, "liqibang_value": {"type": "uint32", "id": 9}, "changbang_value": {"type": "uint32", "id": 10}, "noting_fafu_1": {"type": "uint32", "id": 11}, "noting_fafu_2": {"type": "uint32", "id": 12}, "noting_fafu_3": {"type": "uint32", "id": 13}, "have_liujumanguan": {"type": "bool", "id": 14}, "have_qieshangmanguan": {"type": "bool", "id": 15}, "have_biao_dora": {"type": "bool", "id": 16}, "have_gang_biao_dora": {"type": "bool", "id": 17}, "ming_dora_immediately_open": {"type": "bool", "id": 18}, "have_li_dora": {"type": "bool", "id": 19}, "have_gang_li_dora": {"type": "bool", "id": 20}, "have_sifenglianda": {"type": "bool", "id": 21}, "have_sigangsanle": {"type": "bool", "id": 22}, "have_sijializhi": {"type": "bool", "id": 23}, "have_jiuzhongjiupai": {"type": "bool", "id": 24}, "have_sanjiahele": {"type": "bool", "id": 25}, "have_toutiao": {"type": "bool", "id": 26}, "have_helelianzhuang": {"type": "bool", "id": 27}, "have_helezhongju": {"type": "bool", "id": 28}, "have_tingpailianzhuang": {"type": "bool", "id": 29}, "have_tingpaizhongju": {"type": "bool", "id": 30}, "have_yifa": {"type": "bool", "id": 31}, "have_nanruxiru": {"type": "bool", "id": 32}, "jingsuanyuandian": {"type": "uint32", "id": 33}, "shunweima_2": {"type": "int32", "id": 34}, "shunweima_3": {"type": "int32", "id": 35}, "shunweima_4": {"type": "int32", "id": 36}, "bianjietishi": {"type": "bool", "id": 37}, "ai_level": {"type": "uint32", "id": 38}, "have_zimosun": {"type": "bool", "id": 39}, "disable_multi_yukaman": {"type": "bool", "id": 40}, "guyi_mode": {"type": "uint32", "id": 41}, "disable_leijiyiman": {"type": "bool", "id": 42}, "dora3_mode": {"type": "uint32", "id": 43}, "xuezhandaodi": {"type": "uint32", "id": 44}, "huansanzhang": {"type": "uint32", "id": 45}, "chuanma": {"type": "uint32", "id": 46}, "disable_double_yakuman": {"type": "uint32", "id": 62}, "disable_composite_yakuman": {"type": "uint32", "id": 63}, "enable_shiti": {"type": "uint32", "id": 64}, "enable_nontsumo_liqi": {"type": "uint32", "id": 65}, "disable_double_wind_four_fu": {"type": "uint32", "id": 66}, "disable_angang_guoshi": {"type": "uint32", "id": 67}, "enable_renhe": {"type": "uint32", "id": 68}, "enable_baopai_extend_settings": {"type": "uint32", "id": 69}, "fanfu": {"type": "uint32", "id": 70}}}, "ContestDetailRuleV2": {"fields": {"game_rule": {"type": "ContestDetailRule", "id": 1}, "extra_rule": {"type": "ExtraRule", "id": 2}}, "nested": {"ExtraRule": {"fields": {"required_level": {"type": "uint32", "id": 1}, "max_game_count": {"type": "uint32", "id": 2}}}}}, "GameRuleSetting": {"fields": {"round_type": {"type": "uint32", "id": 1}, "shiduan": {"type": "bool", "id": 2}, "dora_count": {"type": "uint32", "id": 3}, "thinking_type": {"type": "uint32", "id": 4}, "use_detail_rule": {"type": "bool", "id": 5}, "detail_rule_v2": {"type": "ContestDetailRuleV2", "id": 6}}}, "RecordTingPaiInfo": {"fields": {"tile": {"type": "string", "id": 1}, "haveyi": {"type": "bool", "id": 2}, "yiman": {"type": "bool", "id": 3}, "count": {"type": "uint32", "id": 4}, "fu": {"type": "uint32", "id": 5}, "biao_dora_count": {"type": "uint32", "id": 6}, "yiman_zimo": {"type": "bool", "id": 7}, "count_zimo": {"type": "uint32", "id": 8}, "fu_zimo": {"type": "uint32", "id": 9}}}, "RecordNoTilePlayerInfo": {"fields": {"tingpai": {"type": "bool", "id": 3}, "hand": {"rule": "repeated", "type": "string", "id": 4}, "tings": {"rule": "repeated", "type": "RecordTingPaiInfo", "id": 5}, "liuman": {"type": "bool", "id": 6}}}, "RecordHuleInfo": {"fields": {"hand": {"rule": "repeated", "type": "string", "id": 1}, "ming": {"rule": "repeated", "type": "string", "id": 2}, "hu_tile": {"type": "string", "id": 3}, "seat": {"type": "uint32", "id": 4}, "zimo": {"type": "bool", "id": 5}, "qinjia": {"type": "bool", "id": 6}, "liqi": {"type": "bool", "id": 7}, "doras": {"rule": "repeated", "type": "string", "id": 8}, "li_doras": {"rule": "repeated", "type": "string", "id": 9}, "yiman": {"type": "bool", "id": 10}, "count": {"type": "uint32", "id": 11}, "fans": {"rule": "repeated", "type": "RecordFanInfo", "id": 12}, "fu": {"type": "uint32", "id": 13}, "point_zimo_qin": {"type": "uint32", "id": 14}, "point_zimo_xian": {"type": "uint32", "id": 15}, "title_id": {"type": "uint32", "id": 16}, "point_sum": {"type": "uint32", "id": 17}, "dadian": {"type": "uint32", "id": 18}, "is_jue_zhang": {"type": "bool", "id": 19}, "xun": {"type": "uint32", "id": 20}, "ting_type": {"type": "uint32", "id": 21}, "ting_mian": {"type": "uint32", "id": 22}}, "nested": {"RecordFanInfo": {"fields": {"val": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}}}}}, "RecordHulesInfo": {"fields": {"seat": {"type": "int32", "id": 1}, "hules": {"rule": "repeated", "type": "RecordHuleInfo", "id": 2}}}, "RecordLiujuInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "RecordNoTileInfo": {"fields": {"liujumanguan": {"type": "bool", "id": 1}, "players": {"rule": "repeated", "type": "RecordNoTilePlayerInfo", "id": 2}}}, "RecordLiqiInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "score": {"type": "uint32", "id": 2}, "is_w": {"type": "bool", "id": 3}, "is_zhen_ting": {"type": "bool", "id": 4}, "xun": {"type": "uint32", "id": 5}, "is_success": {"type": "bool", "id": 6}}}, "RecordGangInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "pai": {"type": "string", "id": 3}, "is_dora": {"type": "bool", "id": 4}, "xun": {"type": "uint32", "id": 5}}}, "RecordBaBeiInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "is_zi_mo": {"type": "bool", "id": 2}, "is_chong": {"type": "bool", "id": 3}, "is_bei": {"type": "bool", "id": 4}}}, "RecordPeiPaiInfo": {"fields": {"dora_count": {"type": "uint32", "id": 1}, "r_dora_count": {"type": "uint32", "id": 2}, "bei_count": {"type": "uint32", "id": 3}}}, "RecordRoundInfo": {"fields": {"name": {"type": "string", "id": 1}, "chang": {"type": "uint32", "id": 2}, "ju": {"type": "uint32", "id": 3}, "ben": {"type": "uint32", "id": 4}, "scores": {"rule": "repeated", "type": "uint32", "id": 5}, "liqi_infos": {"rule": "repeated", "type": "RecordLiqiInfo", "id": 7}, "gang_infos": {"rule": "repeated", "type": "RecordGangInfo", "id": 8}, "peipai_infos": {"rule": "repeated", "type": "RecordPeiPaiInfo", "id": 9}, "babai_infos": {"rule": "repeated", "type": "RecordBaBeiInfo", "id": 10}, "hules_info": {"type": "RecordHulesInfo", "id": 11}, "liuju_info": {"type": "RecordLiujuInfo", "id": 12}, "no_tile_info": {"type": "RecordNoTileInfo", "id": 13}}}, "RecordAnalysisedData": {"fields": {"round_infos": {"rule": "repeated", "type": "RecordRoundInfo", "id": 1}}}, "VoteData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "vote": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}}}, "ActivityBuffData": {"fields": {"buff_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 5}, "count": {"type": "uint32", "id": 6}, "update_time": {"type": "uint32", "id": 7}}}, "AccountResourceSnapshot": {"fields": {"bag_item": {"rule": "repeated", "type": "BagItemSnapshot", "id": 1}, "currency": {"rule": "repeated", "type": "CurrencySnapshot", "id": 2}, "title": {"type": "TitleSnapshot", "id": 3}, "used_title": {"type": "UsedTitleSnapshot", "id": 4}, "currency_convert": {"type": "uint32", "id": 5}}, "nested": {"BagItemSnapshot": {"fields": {"resource_id": {"type": "uint32", "id": 1}, "resource_count": {"type": "uint32", "id": 2}, "resource_version": {"type": "uint32", "id": 3}}}, "CurrencySnapshot": {"fields": {"currency_id": {"type": "uint32", "id": 1}, "currency_count": {"type": "uint32", "id": 2}}}, "TitleSnapshot": {"fields": {"title_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "UsedTitleSnapshot": {"fields": {"title_id": {"type": "uint32", "id": 1}}}}}, "AccountCharacterSnapshot": {"fields": {"created_characters": {"rule": "repeated", "type": "uint32", "id": 1}, "removed_characters": {"rule": "repeated", "type": "Character", "id": 2}, "modified_characters": {"rule": "repeated", "type": "Character", "id": 3}, "main_character": {"type": "MainCharacterSnapshot", "id": 4}, "skins": {"type": "SkinsSnapshot", "id": 5}, "hidden_characters": {"type": "HiddenCharacter", "id": 6}}, "nested": {"MainCharacterSnapshot": {"fields": {"character_id": {"type": "uint32", "id": 1}}}, "SkinsSnapshot": {"fields": {"skin_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "HiddenCharacter": {"fields": {"hidden_list": {"rule": "repeated", "type": "uint32", "id": 1}}}}}, "AccountMailRecord": {"fields": {"created_mails": {"rule": "repeated", "type": "uint32", "id": 1}, "removed_mails": {"rule": "repeated", "type": "MailSnapshot", "id": 2}, "modified_mails": {"rule": "repeated", "type": "MailSnapshot", "id": 3}}, "nested": {"MailSnapshot": {"fields": {"mail_id": {"type": "uint32", "id": 1}, "reference_id": {"type": "uint32", "id": 2}, "create_time": {"type": "uint32", "id": 3}, "expire_time": {"type": "uint32", "id": 4}, "take_attachment": {"type": "uint32", "id": 5}, "attachments": {"rule": "repeated", "type": "RewardSlot", "id": 6}}}}}, "AccountAchievementSnapshot": {"fields": {"achievements": {"rule": "repeated", "type": "AchievementProgress", "id": 1}, "rewarded_group": {"type": "RewardedGroupSnapshot", "id": 2}, "version": {"type": "AchievementVersion", "id": 3}}, "nested": {"RewardedGroupSnapshot": {"fields": {"rewarded_id": {"type": "uint32", "id": 1}}}, "AchievementVersion": {"fields": {"version": {"type": "uint32", "id": 1}}}}}, "AccountMiscSnapshot": {"fields": {"faith_data": {"type": "FaithData", "id": 1}, "vip_reward_gained": {"type": "AccountVIPRewardSnapshot", "id": 2}, "vip": {"type": "AccountVIP", "id": 3}, "shop_info": {"type": "ShopInfo", "id": 4}, "month_ticket": {"type": "AccountMonthTicketSnapshot", "id": 5}, "recharged": {"type": "AccountRechargeInfo", "id": 6}, "month_ticket_v2": {"type": "AccountMonthTicketSnapshotV2", "id": 7}}, "nested": {"AccountVIPRewardSnapshot": {"fields": {"rewarded": {"rule": "repeated", "type": "uint32", "id": 1}}}, "MonthTicketInfo": {"fields": {"id": {"type": "uint32", "id": 1}, "end_time": {"type": "uint32", "id": 2}, "last_pay_time": {"type": "uint32", "id": 3}, "record_start_time": {"type": "uint32", "id": 4}, "history": {"rule": "repeated", "type": "uint32", "id": 5}}}, "AccountMonthTicketSnapshot": {"fields": {"tickets": {"rule": "repeated", "type": "MonthTicketInfo", "id": 1}}}, "AccountVIP": {"fields": {"vip": {"type": "uint32", "id": 1}}}, "AccountRechargeInfo": {"fields": {"records": {"rule": "repeated", "type": "RechargeRecord", "id": 1}, "has_data": {"type": "uint32", "id": 2}}, "nested": {"RechargeRecord": {"fields": {"level": {"type": "uint32", "id": 1}, "recharge_time": {"type": "uint32", "id": 2}}}}}, "AccountMonthTicketSnapshotV2": {"fields": {"end_time": {"type": "uint32", "id": 1}, "last_pay_time": {"type": "uint32", "id": 2}, "record_start_time": {"type": "uint32", "id": 3}, "history": {"rule": "repeated", "type": "uint32", "id": 4}}}}}, "AccountGiftCodeRecord": {"fields": {"used_gift_code": {"rule": "repeated", "type": "string", "id": 1}}}, "AccSn": {"fields": {"resource": {"type": "AccountResourceSnapshot", "id": 1}, "character": {"type": "AccountCharacterSnapshot", "id": 2}, "mail": {"type": "AccountMailRecord", "id": 3}, "achievement": {"type": "AccountAchievementSnapshot", "id": 4}, "misc": {"type": "AccountMiscSnapshot", "id": 5}, "gift_code": {"type": "AccountGiftCodeRecord", "id": 6}}}, "AccSnDa": {"fields": {"account_id": {"type": "uint32", "id": 1}, "time": {"type": "uint32", "id": 2}, "snapshot": {"type": "bytes", "id": 3}}}, "TransparentData": {"fields": {"method": {"type": "string", "id": 1}, "data": {"type": "bytes", "id": 2}, "session": {"type": "string", "id": 3}, "remote": {"type": "NetworkEndpoint", "id": 4}}}, "AmuletTile": {"fields": {"id": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}}}, "AmuletFan": {"fields": {"id": {"type": "uint32", "id": 1}, "val": {"type": "int64", "id": 2}, "count": {"type": "uint32", "id": 3}, "yiman": {"type": "bool", "id": 4}}}, "AmuletReplace": {"fields": {"id": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}}}, "AmuletMingInfo": {"fields": {"type": {"type": "uint32", "id": 1}, "tile_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "AmuletActivityHookEffect": {"fields": {"add_dora": {"rule": "repeated", "type": "uint32", "id": 1}, "add_tian_dora": {"rule": "repeated", "type": "string", "id": 3}, "add_effect": {"rule": "repeated", "type": "AmuletActivityAddHookEffect", "id": 4}, "remove_effect": {"rule": "repeated", "type": "uint32", "id": 5}, "add_buff": {"rule": "repeated", "type": "uint32", "id": 6}, "remove_buff": {"rule": "repeated", "type": "uint32", "id": 7}, "add_coin": {"type": "int32", "id": 9}, "tile_replace": {"rule": "repeated", "type": "AmuletReplace", "id": 11}, "add_fan": {"type": "string", "id": 12}, "add_base": {"type": "string", "id": 13}, "modify_fan": {"rule": "repeated", "type": "AmuletFan", "id": 14}, "id": {"type": "uint32", "id": 15}, "modify_dora": {"type": "bool", "id": 16}, "uid": {"type": "uint32", "id": 17}, "add_show_tile": {"rule": "repeated", "type": "uint32", "id": 18}, "add_dora_count": {"type": "int32", "id": 19}, "add_dora_no_hook": {"rule": "repeated", "type": "uint32", "id": 20}, "add_coin_no_hook": {"type": "int32", "id": 21}, "add_tile_score": {"rule": "repeated", "type": "AmuletGameTileScoreData", "id": 22}, "add_tile_score_no_hook": {"rule": "repeated", "type": "AmuletGameTileScoreData", "id": 23}}, "nested": {"AmuletActivityAddHookEffect": {"fields": {"id": {"type": "uint32", "id": 1}, "uid": {"type": "uint32", "id": 2}}}}}, "AmuletHuleInfo": {"fields": {"tile": {"type": "uint32", "id": 1}, "fan_list": {"rule": "repeated", "type": "AmuletFan", "id": 2}, "fan": {"type": "string", "id": 3}, "point": {"type": "string", "id": 4}, "base": {"type": "string", "id": 5}}}, "AmuletHuleOperateResult": {"fields": {"hu_final": {"type": "AmuletHuleInfo", "id": 2}, "hu_base": {"type": "AmuletHuleInfo", "id": 3}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 5}}}, "AmuletGangOperateResult": {"fields": {"new_dora": {"rule": "repeated", "type": "uint32", "id": 4}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 5}}}, "AmuletDealTileResult": {"fields": {"tile": {"type": "uint32", "id": 1}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 5}}}, "AmuletDiscardTileResult": {"fields": {"tile": {"type": "uint32", "id": 1}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 5}}}, "AmuletStartGameResult": {"fields": {"hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 5}}}, "AmuletRoundResult": {"fields": {"hu_result": {"type": "AmuletHuleOperateResult", "id": 2}, "deal_result": {"type": "AmuletDealTileResult", "id": 4}, "discard_result": {"type": "AmuletDiscardTileResult", "id": 5}}}, "AmuletUpgradeResult": {"fields": {"remain_rounds": {"rule": "repeated", "type": "AmuletRoundResult", "id": 1}, "point_coin": {"type": "uint32", "id": 2}, "level_coin": {"type": "uint32", "id": 3}, "shop": {"type": "AmuletGameShopData", "id": 4}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 5}, "reward_pack": {"rule": "repeated", "type": "uint32", "id": 6}}}, "QuestionnaireReward": {"fields": {"resource_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "QuestionnaireDetail": {"fields": {"id": {"type": "uint32", "id": 1}, "version_id": {"type": "uint32", "id": 2}, "effective_time_start": {"type": "uint32", "id": 3}, "effective_time_end": {"type": "uint32", "id": 4}, "rewards": {"rule": "repeated", "type": "QuestionnaireReward", "id": 5}, "banner_title": {"type": "string", "id": 6}, "title": {"type": "string", "id": 7}, "announcement_title": {"type": "string", "id": 8}, "announcement_content": {"type": "string", "id": 9}, "final_text": {"type": "string", "id": 10}, "questions": {"rule": "repeated", "type": "QuestionnaireQuestion", "id": 11}, "type": {"type": "uint32", "id": 12}}}, "QuestionnaireQuestion": {"fields": {"id": {"type": "uint32", "id": 1}, "title": {"type": "string", "id": 2}, "describe": {"type": "string", "id": 3}, "type": {"type": "string", "id": 4}, "sub_type": {"type": "string", "id": 5}, "options": {"rule": "repeated", "type": "QuestionOption", "id": 6}, "option_random_sort": {"type": "bool", "id": 7}, "require": {"type": "bool", "id": 8}, "max_choice": {"type": "uint32", "id": 9}, "next_question": {"rule": "repeated", "type": "NextQuestionData", "id": 10}, "matrix_row": {"rule": "repeated", "type": "string", "id": 11}, "option_random_sort_index": {"type": "int32", "id": 12}}, "nested": {"QuestionOption": {"fields": {"label": {"type": "string", "id": 1}, "value": {"type": "string", "id": 2}, "allow_input": {"type": "bool", "id": 3}}}, "NextQuestionData": {"fields": {"target_question_id": {"type": "uint32", "id": 1}, "conditions": {"rule": "repeated", "type": "QuestionconditionWrapper", "id": 10}}, "nested": {"QuestionCondition": {"fields": {"question_id": {"type": "uint32", "id": 1}, "op": {"type": "string", "id": 2}, "values": {"rule": "repeated", "type": "string", "id": 3}}}, "QuestionconditionWrapper": {"fields": {"conditions": {"rule": "repeated", "type": "QuestionCondition", "id": 1}}}}}}}, "QuestionnaireBrief": {"fields": {"id": {"type": "uint32", "id": 1}, "version_id": {"type": "uint32", "id": 2}, "effective_time_start": {"type": "uint32", "id": 3}, "effective_time_end": {"type": "uint32", "id": 4}, "rewards": {"rule": "repeated", "type": "QuestionnaireReward", "id": 5}, "banner_title": {"type": "string", "id": 6}, "title": {"type": "string", "id": 7}, "type": {"type": "uint32", "id": 8}}}, "SeerReport": {"fields": {"uuid": {"type": "string", "id": 1}, "events": {"rule": "repeated", "type": "SeerEvent", "id": 2}, "rounds": {"rule": "repeated", "type": "SeerRound", "id": 3}}}, "SeerEvent": {"fields": {"record_index": {"type": "int32", "id": 1}, "seer_index": {"type": "int32", "id": 2}, "recommends": {"rule": "repeated", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 3}}}, "SeerRecommend": {"fields": {"seat": {"type": "int32", "id": 1}, "predictions": {"rule": "repeated", "type": "SeerPrediction", "id": 2}}}, "SeerPrediction": {"fields": {"action": {"type": "int32", "id": 1}, "score": {"type": "int32", "id": 2}}}, "SeerRound": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "player_scores": {"rule": "repeated", "type": "SeerScore", "id": 4}}}, "SeerScore": {"fields": {"seat": {"type": "uint32", "id": 1}, "rating": {"type": "uint32", "id": 2}}}, "SeerBrief": {"fields": {"uuid": {"type": "string", "id": 1}, "state": {"type": "uint32", "id": 2}, "expire_time": {"type": "uint32", "id": 3}, "player_scores": {"rule": "repeated", "type": "SeerScore", "id": 4}, "create_time": {"type": "uint32", "id": 5}}}, "SimulationV2SeasonData": {"fields": {"round": {"type": "uint32", "id": 1}, "ability": {"type": "SimulationV2Ability", "id": 2}, "effect_list": {"rule": "repeated", "type": "SimulationV2Effect", "id": 3}, "match": {"type": "SimulationV2Match", "id": 4}, "event": {"type": "SimulationV2Event", "id": 5}, "event_history": {"rule": "repeated", "type": "SimulationV2EventHistory", "id": 6}, "record": {"type": "SimulationV2Record", "id": 7}, "total_score": {"type": "int32", "id": 8}, "match_history": {"rule": "repeated", "type": "SimulationV2MatchRecord", "id": 9}}}, "SimulationV2PlayerRecord": {"fields": {"id": {"type": "uint32", "id": 1}, "main": {"type": "bool", "id": 2}, "score": {"type": "int32", "id": 3}, "rank": {"type": "uint32", "id": 4}, "seat": {"type": "uint32", "id": 5}}}, "SimulationV2MatchRecord": {"fields": {"players": {"rule": "repeated", "type": "SimulationV2PlayerRecord", "id": 1}, "round": {"type": "uint32", "id": 2}}}, "SimulationV2EventHistory": {"fields": {"id": {"type": "uint32", "id": 1}, "round": {"type": "uint32", "id": 2}}}, "SimulationV2Event": {"fields": {"id": {"type": "uint32", "id": 1}, "selections": {"rule": "repeated", "type": "SimulationV2EventSelection", "id": 2}, "next_round": {"type": "uint32", "id": 3}}, "nested": {"SimulationV2EventSelection": {"fields": {"id": {"type": "uint32", "id": 1}, "results": {"rule": "repeated", "type": "SimulationV2EventResult", "id": 2}}, "nested": {"SimulationV2EventResult": {"fields": {"id": {"type": "uint32", "id": 1}, "weight": {"type": "uint32", "id": 2}}}}}}}, "SimulationV2Ability": {"fields": {"luk": {"type": "uint32", "id": 1}, "tec": {"type": "uint32", "id": 2}, "ins": {"type": "uint32", "id": 3}, "int": {"type": "uint32", "id": 4}, "res": {"type": "uint32", "id": 5}}}, "SimulationV2Buff": {"fields": {"id": {"type": "uint32", "id": 1}, "round": {"type": "uint32", "id": 2}, "store": {"rule": "repeated", "type": "uint32", "id": 3}}}, "SimulationV2Effect": {"fields": {"id": {"type": "uint32", "id": 1}}}, "SimulationV2MatchInfo": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "gong": {"type": "uint32", "id": 4}, "remain": {"type": "uint32", "id": 5}}}, "SimulationV2Record": {"fields": {"hu_count": {"type": "uint32", "id": 1}, "chong_count": {"type": "uint32", "id": 2}, "highest_hu": {"type": "uint32", "id": 3}, "rank": {"rule": "repeated", "type": "uint32", "id": 4}, "round_count": {"type": "uint32", "id": 5}}}, "SimulationV2MatchHistory": {"fields": {"type": {"type": "uint32", "id": 1}, "remain": {"type": "uint32", "id": 2}, "score_modify": {"rule": "repeated", "type": "int32", "id": 3}, "round_start": {"type": "RoundStartArgs", "id": 4}, "riichi": {"type": "RiichiArgs", "id": 5}, "fulu": {"type": "<PERSON>lu<PERSON><PERSON><PERSON>", "id": 6}, "hule": {"rule": "repeated", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 7}, "push_ting": {"type": "<PERSON>ush<PERSON>ingArg<PERSON>", "id": 8}, "find_ting": {"type": "FindTingArgs", "id": 9}, "liuju": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 10}, "story": {"type": "StoryArgs", "id": 11}}, "nested": {"RoundStartArgs": {"fields": {"info": {"type": "SimulationV2MatchInfo", "id": 1}, "scores": {"rule": "repeated", "type": "int32", "id": 2}, "ting": {"type": "uint32", "id": 3}, "effected_buff_list": {"rule": "repeated", "type": "uint32", "id": 4}}}, "RiichiArgs": {"fields": {"seat": {"type": "uint32", "id": 1}}}, "FuluArgs": {"fields": {"seat": {"type": "uint32", "id": 1}, "ting": {"type": "uint32", "id": 2}, "fulu": {"type": "uint32", "id": 3}}}, "HuleArgs": {"fields": {"seat": {"type": "uint32", "id": 1}, "zimo": {"type": "bool", "id": 2}, "chong_seat": {"type": "uint32", "id": 3}, "point": {"type": "int32", "id": 4}, "fan": {"type": "int32", "id": 5}, "score_modify": {"rule": "repeated", "type": "int32", "id": 6}}}, "PushTingArgs": {"fields": {"seat": {"type": "uint32", "id": 1}, "ting": {"type": "uint32", "id": 2}}}, "FindTingArgs": {"fields": {"seat": {"type": "uint32", "id": 1}, "target": {"type": "uint32", "id": 2}}}, "LiujuArgs": {"fields": {"ting": {"rule": "repeated", "type": "uint32", "id": 1}}}, "StoryArgs": {"fields": {"story_id": {"type": "uint32", "id": 1}}}}}, "SimulationV2Match": {"fields": {"info": {"type": "SimulationV2MatchInfo", "id": 1}, "players": {"rule": "repeated", "type": "SimulationV2Player", "id": 2}, "history": {"rule": "repeated", "type": "SimulationV2MatchHistory", "id": 3}, "rank": {"rule": "repeated", "type": "uint32", "id": 4}, "is_match_end": {"type": "bool", "id": 5}, "actions": {"rule": "repeated", "type": "SimulationActionData", "id": 6}, "buff_list": {"rule": "repeated", "type": "SimulationV2Buff", "id": 9}, "is_first_round": {"type": "bool", "id": 10}, "last_event_remain": {"type": "uint32", "id": 11}, "effected_buff_list": {"rule": "repeated", "type": "uint32", "id": 12}, "triggered_story": {"rule": "repeated", "type": "uint32", "id": 13}}, "nested": {"SimulationV2Player": {"fields": {"id": {"type": "uint32", "id": 1}, "main": {"type": "bool", "id": 2}, "ting": {"type": "uint32", "id": 4}, "score": {"type": "int32", "id": 5}, "fulu": {"type": "uint32", "id": 6}, "riichi": {"type": "bool", "id": 7}, "find_ting": {"rule": "repeated", "type": "uint32", "id": 8}, "seat": {"type": "uint32", "id": 9}, "con_push_ting": {"type": "uint32", "id": 10}, "con_keep_ting": {"type": "uint32", "id": 11}, "ippatsu": {"type": "bool", "id": 12}}}}}, "SimulationActionData": {"fields": {"type": {"type": "uint32", "id": 1}, "riichi": {"type": "ActionRiichiData", "id": 2}, "hule": {"type": "ActionHuleData", "id": 3}, "fulu": {"type": "ActionFuluData", "id": 4}, "discard_tile": {"type": "ActionDiscardData", "id": 5}, "deal_tile": {"type": "ActionDealTileData", "id": 6}}, "nested": {"ActionRiichiData": {"fields": {"seat": {"type": "uint32", "id": 1}}}, "ActionHuleData": {"fields": {"hule": {"rule": "repeated", "type": "HuleInfo", "id": 1}}, "nested": {"HuleInfo": {"fields": {"fan": {"type": "uint32", "id": 1}, "zimo": {"type": "bool", "id": 2}, "point": {"type": "uint32", "id": 3}, "oya": {"type": "bool", "id": 4}, "player": {"type": "uint32", "id": 5}, "chong": {"type": "uint32", "id": 6}, "toutiao": {"type": "bool", "id": 7}}}}}, "ActionFuluData": {"fields": {"seat": {"type": "uint32", "id": 1}}}, "ActionDiscardData": {"fields": {"seat": {"type": "uint32", "id": 1}, "riichi": {"type": "bool", "id": 2}}}, "ActionDealTileData": {"fields": {"seat": {"type": "uint32", "id": 1}}}}}, "Lobby": {"methods": {"fetchConnectionInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResConnectionInfo"}, "fetchQueueInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchQueueInfo"}, "cancelQueue": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "openidCheck": {"requestType": "ReqOpenidCheck", "responseType": "ResOauth2Check"}, "signup": {"requestType": "ReqSignupAccount", "responseType": "ResSignupAccount"}, "login": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "Res<PERSON><PERSON>in"}, "prepareLogin": {"requestType": "ReqPrepareLogin", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fastLogin": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFastLogin"}, "fetchInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchInfo"}, "loginSuccess": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchServerMaintenanceInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchServerMaintenanceInfo"}, "emailLogin": {"requestType": "ReqEmail<PERSON><PERSON><PERSON>", "responseType": "Res<PERSON><PERSON>in"}, "oauth2Auth": {"requestType": "ReqOauth2Auth", "responseType": "ResOauth2Auth"}, "oauth2Check": {"requestType": "ReqOauth2Check", "responseType": "ResOauth2Check"}, "oauth2Signup": {"requestType": "ReqOauth2Signup", "responseType": "ResOauth2Signup"}, "oauth2Login": {"requestType": "ReqOauth2Login", "responseType": "Res<PERSON><PERSON>in"}, "dmmPreLogin": {"requestType": "ReqDMMPreLogin", "responseType": "ResDMMPreLogin"}, "createPhoneVerifyCode": {"requestType": "ReqCreatePhoneVerifyCode", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "createEmailVerifyCode": {"requestType": "ReqCreateEmailVerifyCode", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "verfifyCodeForSecure": {"requestType": "ReqVerifyCodeForSecure", "responseType": "ResVerfiyCodeForSecure"}, "bindPhoneNumber": {"requestType": "ReqBindPhoneNumber", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "unbindPhoneNumber": {"requestType": "ReqUnbindPhoneNumber", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchPhoneLoginBind": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchPhoneLoginBind"}, "createPhoneLoginBind": {"requestType": "ReqCreatePhoneLoginBind", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "bindEmail": {"requestType": "ReqBindEmail", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "modifyPassword": {"requestType": "ReqModifyPassword", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "bindAccount": {"requestType": "ReqBindAccount", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "logout": {"requestType": "ReqLogout", "responseType": "ResLogout"}, "heatbeat": {"requestType": "ReqHeatBeat", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "searchAccountByEid": {"requestType": "ReqSearchAccountByEidLobby", "responseType": "ResSearchAccountbyEidLobby"}, "loginBeat": {"requestType": "ReqLoginBeat", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "createNickname": {"requestType": "ReqCreateNickname", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "modifyNickname": {"requestType": "ReqModifyNickname", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "modifyBirthday": {"requestType": "ReqModifyBirthday", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchRoom": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResSelfRoom"}, "fetchGamingInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchGamingInfo"}, "createRoom": {"requestType": "ReqCreateRoom", "responseType": "ResCreateRoom"}, "joinRoom": {"requestType": "ReqJoin<PERSON>oom", "responseType": "ResJoinRoom"}, "leaveRoom": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "readyPlay": {"requestType": "ReqRoomReady", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "dressingStatus": {"requestType": "ReqRoomDressing", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "startRoom": {"requestType": "ReqRoomStart", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "roomKickPlayer": {"requestType": "ReqRoomKickPlayer", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "modifyRoom": {"requestType": "ReqModifyRoom", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "addRoomRobot": {"requestType": "ReqAddRoomRobot", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "matchGame": {"requestType": "ReqJoinM<PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "cancelMatch": {"requestType": "ReqCancelMatchQueue", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchAccountInfo": {"requestType": "ReqAccountInfo", "responseType": "ResAccountInfo"}, "changeAvatar": {"requestType": "<PERSON>q<PERSON><PERSON>eAvat<PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveVersionReward": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchAccountStatisticInfo": {"requestType": "ReqAccountStatisticInfo", "responseType": "ResAccountStatisticInfo"}, "fetchAccountChallengeRankInfo": {"requestType": "ReqAccountInfo", "responseType": "ResAccountChallengeRankInfo"}, "fetchAccountCharacterInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResAccountCharacterInfo"}, "shopPurchase": {"requestType": "ReqShopPurchase", "responseType": "ResShopPurchase"}, "fetchGameRecord": {"requestType": "ReqGameRecord", "responseType": "ResGameRecord"}, "readGameRecord": {"requestType": "ReqGameRecord", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchGameRecordList": {"requestType": "ReqGameRecordList", "responseType": "ResGameRecordList"}, "fetchGameRecordListV2": {"requestType": "ReqGameRecordListV2", "responseType": "ResGameRecordListV2"}, "fetchNextGameRecordList": {"requestType": "ReqNextGameRecordList", "responseType": "ResNextGameRecordList"}, "fetchCollectedGameRecordList": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResCollectedGameRecordList"}, "fetchGameRecordsDetail": {"requestType": "ReqGameRecordsDetail", "responseType": "ResGameRecordsDetail"}, "fetchGameRecordsDetailV2": {"requestType": "ReqGameRecordsDetailV2", "responseType": "ResGameRecordsDetailV2"}, "addCollectedGameRecord": {"requestType": "ReqAddCollectedGameRecord", "responseType": "ResAddCollectedGameRecord"}, "removeCollectedGameRecord": {"requestType": "ReqRemoveCollectedGameRecord", "responseType": "ResRemoveCollectedGameRecord"}, "changeCollectedGameRecordRemarks": {"requestType": "ReqChangeCollectedGameRecordRemarks", "responseType": "ResChangeCollectedGameRecordRemarks"}, "fetchLevelLeaderboard": {"requestType": "ReqLevelLeaderboard", "responseType": "ResLevelLeaderboard"}, "fetchChallengeLeaderboard": {"requestType": "ReqChallangeLeaderboard", "responseType": "ResChallengeLeaderboard"}, "fetchMutiChallengeLevel": {"requestType": "ReqMutiChallengeLevel", "responseType": "ResMutiChallengeLevel"}, "fetchMultiAccountBrief": {"requestType": "ReqMultiAccountId", "responseType": "ResMultiAccountBrief"}, "fetchFriendList": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFriendList"}, "fetchFriendApplyList": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFriendApplyList"}, "applyFriend": {"requestType": "ReqApplyFriend", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "handleFriendApply": {"requestType": "ReqHandleFriendApply", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "removeFriend": {"requestType": "ReqRemoveFriend", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "searchAccountById": {"requestType": "ReqSearchAccountById", "responseType": "ResSearchAccountById"}, "searchAccountByPattern": {"requestType": "ReqSearchAccountByPattern", "responseType": "ResSearchAccountByPattern"}, "fetchAccountState": {"requestType": "ReqAccountList", "responseType": "ResAccountStates"}, "fetchBagInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResBagInfo"}, "useBagItem": {"requestType": "ReqUseBagItem", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "openManualItem": {"requestType": "ReqOpenManualItem", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "openRandomRewardItem": {"requestType": "ReqOpenRandomRewardItem", "responseType": "ResOpenRandomRewardItem"}, "openAllRewardItem": {"requestType": "ReqOpenAllRewardItem", "responseType": "ResOpenAllRewardItem"}, "composeShard": {"requestType": "ReqComposeShard", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchAnnouncement": {"requestType": "ReqFetchAnnouncement", "responseType": "ResAnnouncement"}, "readAnnouncement": {"requestType": "ReqReadAnnouncement", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchMailInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResMailInfo"}, "readMail": {"requestType": "ReqReadMail", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "deleteMail": {"requestType": "ReqDeleteMail", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "takeAttachmentFromMail": {"requestType": "ReqTakeAttachment", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveAchievementReward": {"requestType": "ReqReceiveAchievementReward", "responseType": "ResReceiveAchievementReward"}, "receiveAchievementGroupReward": {"requestType": "ReqReceiveAchievementGroupReward", "responseType": "ResReceiveAchievementGroupReward"}, "fetchAchievementRate": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchAchievementRate"}, "fetchAchievement": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResAchievement"}, "buyShiLian": {"requestType": "ReqBuy<PERSON>hi<PERSON>ian", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "matchShiLian": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "goNextShiLian": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "updateClientValue": {"requestType": "ReqUpdateClientValue", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchClientValue": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResClientValue"}, "clientMessage": {"requestType": "ReqClientMessage", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchCurrentMatchInfo": {"requestType": "ReqCurrentMatchInfo", "responseType": "ResCurrentMatchInfo"}, "userComplain": {"requestType": "ReqUserComplain", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchReviveCoinInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResReviveCoinInfo"}, "gainReviveCoin": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchDailyTask": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResDailyTask"}, "refreshDailyTask": {"requestType": "ReqRefreshDailyTask", "responseType": "ResRefreshDailyTask"}, "useGiftCode": {"requestType": "ReqUseGiftCode", "responseType": "ResUseGiftCode"}, "useSpecialGiftCode": {"requestType": "ReqUseGiftCode", "responseType": "ResUseSpecialGiftCode"}, "fetchTitleList": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResTitleList"}, "useTitle": {"requestType": "ReqUseTitle", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "sendClientMessage": {"requestType": "ReqSendClientMessage", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchGameLiveInfo": {"requestType": "ReqGameLiveInfo", "responseType": "ResGameLiveInfo"}, "fetchGameLiveLeftSegment": {"requestType": "ReqGameLiveLeftSegment", "responseType": "ResGameLiveLeftSegment"}, "fetchGameLiveList": {"requestType": "ReqGameLiveList", "responseType": "ResGameLiveList"}, "fetchCommentSetting": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResCommentSetting"}, "updateCommentSetting": {"requestType": "ReqUpdateCommentSetting", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchCommentList": {"requestType": "ReqFetchCommentList", "responseType": "ResFetchCommentList"}, "fetchCommentContent": {"requestType": "ReqFetchCommentContent", "responseType": "ResFetchCommentContent"}, "leaveComment": {"requestType": "ReqLeaveComment", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "deleteComment": {"requestType": "ReqDeleteComment", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "updateReadComment": {"requestType": "ReqUpdateReadComment", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchRollingNotice": {"requestType": "ReqFetchRollingNotice", "responseType": "ResFetchRollingNotice"}, "fetchMaintainNotice": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchMaintainNotice"}, "fetchServerTime": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResServerTime"}, "fetchPlatformProducts": {"requestType": "ReqPlatformBillingProducts", "responseType": "ResPlatformBillingProducts"}, "fetchRandomCharacter": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResRandomCharacter"}, "setRandomCharacter": {"requestType": "ReqRandomCharacter", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "cancelGooglePlayOrder": {"requestType": "ReqCancelGooglePlayOrder", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "openChest": {"requestType": "ReqOpenChest", "responseType": "ResOpenChest"}, "buyFromChestShop": {"requestType": "ReqBuyFromChestShop", "responseType": "ResBuyFromChestShop"}, "fetchDailySignInInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResDailySignInInfo"}, "doDailySignIn": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "doActivitySignIn": {"requestType": "ReqDoActivitySignIn", "responseType": "ResDoActivitySignIn"}, "fetchCharacterInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResCharacterInfo"}, "updateCharacterSort": {"requestType": "ReqUpdateCharacterSort", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "changeMainCharacter": {"requestType": "ReqChangeMainCharacter", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "changeCharacterSkin": {"requestType": "ReqChangeCharacterSkin", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "changeCharacterView": {"requestType": "ReqChangeCharacterView", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "setHiddenCharacter": {"requestType": "ReqSetHiddenCharacter", "responseType": "ResSetHiddenCharacter"}, "sendGiftToCharacter": {"requestType": "ReqSendGiftToCharacter", "responseType": "ResSendGiftToCharacter"}, "sellItem": {"requestType": "ReqSellItem", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchCommonView": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResCommonView"}, "changeCommonView": {"requestType": "ReqChangeCommonView", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "saveCommonViews": {"requestType": "ReqSaveCommonViews", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchCommonViews": {"requestType": "ReqCommonViews", "responseType": "ResCommonViews"}, "fetchAllCommonViews": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResAllcommonViews"}, "useCommonView": {"requestType": "ReqUseCommonView", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "upgradeCharacter": {"requestType": "ReqUpgradeCharacter", "responseType": "ResUpgradeCharacter"}, "addFinishedEnding": {"requestType": "ReqFinishedEnding", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveEndingReward": {"requestType": "ReqFinishedEnding", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "gameMasterCommand": {"requestType": "ReqGMCommand", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchShopInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResShopInfo"}, "buyFromShop": {"requestType": "ReqBuyFromShop", "responseType": "ResBuyFromShop"}, "buyFromZHP": {"requestType": "ReqBuyFromZHP", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "refreshZHPShop": {"requestType": "ReqReshZHPShop", "responseType": "ResRefreshZHPShop"}, "fetchMonthTicketInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResMonthTicketInfo"}, "payMonthTicket": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResPayMonthTicket"}, "exchangeCurrency": {"requestType": "ReqExchangeCurrency", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "exchangeChestStone": {"requestType": "ReqExchangeCurrency", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "exchangeDiamond": {"requestType": "ReqExchangeCurrency", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchServerSettings": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResServerSettings"}, "fetchAccountSettings": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResAccountSettings"}, "updateAccountSettings": {"requestType": "ReqUpdateAccountSettings", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchModNicknameTime": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResModNicknameTime"}, "createWechatNativeOrder": {"requestType": "ReqCreateWechatNativeOrder", "responseType": "ResCreateWechatNativeOrder"}, "createWechatAppOrder": {"requestType": "ReqCreateWechatAppOrder", "responseType": "ResCreateWechatAppOrder"}, "createAlipayOrder": {"requestType": "ReqCreateAlipayOrder", "responseType": "ResCreateAlipayOrder"}, "createAlipayScanOrder": {"requestType": "ReqCreateAlipayScanOrder", "responseType": "ResCreateAlipayScanOrder"}, "createAlipayAppOrder": {"requestType": "ReqCreateAlipayAppOrder", "responseType": "ResCreateAlipayAppOrder"}, "createJPCreditCardOrder": {"requestType": "ReqCreateJPCreditCardOrder", "responseType": "ResCreateJPCreditCardOrder"}, "createJPPaypalOrder": {"requestType": "ReqCreateJPPaypalOrder", "responseType": "ResCreateJPPaypalOrder"}, "createJPAuOrder": {"requestType": "ReqCreateJPAuOrder", "responseType": "ResCreateJPAuOrder"}, "createJPDocomoOrder": {"requestType": "ReqCreateJPDocomoOrder", "responseType": "ResCreateJPDocomoOrder"}, "createJPWebMoneyOrder": {"requestType": "ReqCreateJPWebMoneyOrder", "responseType": "ResCreateJPWebMoneyOrder"}, "createJPSoftbankOrder": {"requestType": "ReqCreateJPSoftbankOrder", "responseType": "ResCreateJPSoftbankOrder"}, "createJPPayPayOrder": {"requestType": "ReqCreateJPPayPayOrder", "responseType": "ResCreateJPPayPayOrder"}, "fetchJPCommonCreditCardOrder": {"requestType": "ReqFetchJPCommonCreditCardOrder", "responseType": "ResFetchJPCommonCreditCardOrder"}, "createJPGMOOrder": {"requestType": "ReqCreateJPGMOOrder", "responseType": "ResCreateJPGMOOrder"}, "createENPaypalOrder": {"requestType": "ReqCreateENPaypalOrder", "responseType": "ResCreateENPaypalOrder"}, "createENMasterCardOrder": {"requestType": "ReqCreateENMasterCardOrder", "responseType": "ResCreateENMasterCardOrder"}, "createENVisaOrder": {"requestType": "ReqCreateENVisaOrder", "responseType": "ResCreateENVisaOrder"}, "createENJCBOrder": {"requestType": "ReqCreateENJCBOrder", "responseType": "ResCreateENJCBOrder"}, "createENAlipayOrder": {"requestType": "ReqCreateENAlipayOrder", "responseType": "ResCreateENAlipayOrder"}, "createKRPaypalOrder": {"requestType": "ReqCreateKRPaypalOrder", "responseType": "ResCreateKRPaypalOrder"}, "createKRMasterCardOrder": {"requestType": "ReqCreateKRMasterCardOrder", "responseType": "ResCreateKRMasterCardOrder"}, "createKRVisaOrder": {"requestType": "ReqCreateKRVisaOrder", "responseType": "ResCreateKRVisaOrder"}, "createKRJCBOrder": {"requestType": "ReqCreateKRJCBOrder", "responseType": "ResCreateKRJCBOrder"}, "createKRAlipayOrder": {"requestType": "ReqCreateKRAlipayOrder", "responseType": "ResCreateKRAlipayOrder"}, "createDMMOrder": {"requestType": "ReqCreateDMMOrder", "responseType": "ResCreateDmmOrder"}, "createIAPOrder": {"requestType": "ReqCreateIAPOrder", "responseType": "ResCreateIAPOrder"}, "createSteamOrder": {"requestType": "ReqCreateSteamOrder", "responseType": "ResCreateSteamOrder"}, "verifySteamOrder": {"requestType": "ReqVerifySteamOrder", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "createMyCardAndroidOrder": {"requestType": "ReqCreateMyCardOrder", "responseType": "ResCreateMyCardOrder"}, "createMyCardWebOrder": {"requestType": "ReqCreateMyCardOrder", "responseType": "ResCreateMyCardOrder"}, "createPaypalOrder": {"requestType": "ReqCreatePaypalOrder", "responseType": "ResCreatePaypalOrder"}, "createXsollaOrder": {"requestType": "ReqCreateXsollaOrder", "responseType": "ResCreateXsollaOrder"}, "createXsollaV4Order": {"requestType": "ReqCreateXsollaOrder", "responseType": "ResCreateXsollaOrder"}, "verifyMyCardOrder": {"requestType": "ReqVerifyMyCardOrder", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "verificationIAPOrder": {"requestType": "ReqVerificationIAPOrder", "responseType": "ResVerificationIAPOrder"}, "createYostarSDKOrder": {"requestType": "ReqCreateYostarOrder", "responseType": "ResCreateYostarOrder"}, "createBillingOrder": {"requestType": "ReqCreateBillingOrder", "responseType": "ResCreateBillingOrder"}, "solveGooglePlayOrder": {"requestType": "ReqSolveGooglePlayOrder", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "solveGooglePayOrderV3": {"requestType": "ReqSolveGooglePlayOrderV3", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "deliverAA32Order": {"requestType": "ReqDeliverAA32Order", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchMisc": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResMisc"}, "modifySignature": {"requestType": "ReqModifySignature", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchIDCardInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResIDCardInfo"}, "updateIDCardInfo": {"requestType": "ReqUpdateIDCardInfo", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchVipReward": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResVipReward"}, "gainVipReward": {"requestType": "ReqGainVipReward", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchRefundOrder": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchRefundOrder"}, "fetchCustomizedContestList": {"requestType": "ReqFetchCustomizedContestList", "responseType": "ResFetchCustomizedContestList"}, "fetchCustomizedContestAuthInfo": {"requestType": "ReqFetchCustomizedContestAuthInfo", "responseType": "ResFetchCustomizedContestAuthInfo"}, "enterCustomizedContest": {"requestType": "ReqEnterCustomizedContest", "responseType": "ResEnterCustomizedContest"}, "leaveCustomizedContest": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchCustomizedContestOnlineInfo": {"requestType": "ReqFetchCustomizedContestOnlineInfo", "responseType": "ResFetchCustomizedContestOnlineInfo"}, "fetchCustomizedContestByContestId": {"requestType": "ReqFetchCustomizedContestByContestId", "responseType": "ResFetchCustomizedContestByContestId"}, "signupCustomizedContest": {"requestType": "ReqSignupCustomizedContest", "responseType": "ResSignupCustomizedContest"}, "startCustomizedContest": {"requestType": "ReqStartCustomizedContest", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "stopCustomizedContest": {"requestType": "ReqStopCustomizedContest", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "joinCustomizedContestChatRoom": {"requestType": "ReqJoinCustomizedContestChatRoom", "responseType": "ResJoinCustomizedContestChatRoom"}, "leaveCustomizedContestChatRoom": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "sayChatMessage": {"requestType": "ReqSayChatMessage", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchCustomizedContestGameRecords": {"requestType": "ReqFetchCustomizedContestGameRecords", "responseType": "ResFetchCustomizedContestGameRecords"}, "fetchCustomizedContestGameLiveList": {"requestType": "ReqFetchCustomizedContestGameLiveList", "responseType": "ResFetchCustomizedContestGameLiveList"}, "followCustomizedContest": {"requestType": "ReqTargetCustomizedContest", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "unfollowCustomizedContest": {"requestType": "ReqTargetCustomizedContest", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchActivityList": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResActivityList"}, "fetchAccountActivityData": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResAccountActivityData"}, "exchangeActivityItem": {"requestType": "ReqExchangeActivityItem", "responseType": "ResExchangeActivityItem"}, "completeActivityTask": {"requestType": "ReqCompleteActivityTask", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "completeActivityTaskBatch": {"requestType": "ReqCompleteActivityTaskBatch", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "completeActivityFlipTask": {"requestType": "ReqCompleteActivityTask", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "completePeriodActivityTask": {"requestType": "ReqCompleteActivityTask", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "completePeriodActivityTaskBatch": {"requestType": "ReqCompletePeriodActivityTaskBatch", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "completeRandomActivityTask": {"requestType": "ReqCompleteActivityTask", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "completeRandomActivityTaskBatch": {"requestType": "ReqCompleteActivityTaskBatch", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveActivityFlipTask": {"requestType": "ReqReceiveActivityFlipTask", "responseType": "ResReceiveActivityFlipTask"}, "completeSegmentTaskReward": {"requestType": "ReqCompleteSegmentTaskReward", "responseType": "ResCompleteSegmentTaskReward"}, "fetchActivityFlipInfo": {"requestType": "ReqFetchActivityFlipInfo", "responseType": "ResFetchActivityFlipInfo"}, "gainAccumulatedPointActivityReward": {"requestType": "ReqGainAccumulatedPointActivityReward", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "gainMultiPointActivityReward": {"requestType": "ReqGainMultiPointActivityReward", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchRankPointLeaderboard": {"requestType": "ReqFetchRankPointLeaderboard", "responseType": "ResFetchRankPointLeaderboard"}, "gainRankPointReward": {"requestType": "ReqGainRankPointReward", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "richmanActivityNextMove": {"requestType": "ReqRichmanNextMove", "responseType": "ResRichmanNextMove"}, "richmanAcitivitySpecialMove": {"requestType": "ReqRichmanSpecialMove", "responseType": "ResRichmanNextMove"}, "richmanActivityChestInfo": {"requestType": "ReqRichmanChestInfo", "responseType": "ResRichmanChestInfo"}, "createGameObserveAuth": {"requestType": "ReqCreateGameObserveAuth", "responseType": "ResCreateGameObserveAuth"}, "refreshGameObserveAuth": {"requestType": "ReqRefreshGameObserveAuth", "responseType": "ResRefreshGameObserveAuth"}, "fetchActivityBuff": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResActivityBuff"}, "upgradeActivityBuff": {"requestType": "ReqUpgradeActivityBuff", "responseType": "ResActivityBuff"}, "upgradeActivityLevel": {"requestType": "ReqUpgradeActivityLevel", "responseType": "ResUpgradeActivityLevel"}, "receiveUpgradeActivityReward": {"requestType": "ReqReceiveUpgradeActivityReward", "responseType": "ResReceiveUpgradeActivityReward"}, "upgradeChallenge": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResUpgradeChallenge"}, "refreshChallenge": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResRefreshChallenge"}, "fetchChallengeInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchChallengeInfo"}, "forceCompleteChallengeTask": {"requestType": "ReqForceCompleteChallengeTask", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchChallengeSeason": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResChallengeSeasonInfo"}, "receiveChallengeRankReward": {"requestType": "ReqReceiveChallengeRankReward", "responseType": "ResReceiveChallengeRankReward"}, "fetchABMatchInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchABMatch"}, "buyInABMatch": {"requestType": "ReqBuyInABMatch", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveABMatchReward": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "quitABMatch": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "startUnifiedMatch": {"requestType": "ReqStartUnifiedMatch", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "cancelUnifiedMatch": {"requestType": "ReqCancelUnifiedMatch", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchGamePointRank": {"requestType": "ReqGamePointRank", "responseType": "ResGamePointRank"}, "fetchSelfGamePointRank": {"requestType": "ReqGamePointRank", "responseType": "ResFetchSelfGamePointRank"}, "readSNS": {"requestType": "ReqReadSNS", "responseType": "ResReadSNS"}, "replySNS": {"requestType": "ReqReplySNS", "responseType": "ResReplySNS"}, "likeSNS": {"requestType": "ReqLikeSNS", "responseType": "ResLikeSNS"}, "digMine": {"requestType": "ReqDigMine", "responseType": "ResDigMine"}, "fetchLastPrivacy": {"requestType": "ReqFetchLastPrivacy", "responseType": "ResFetchLastPrivacy"}, "checkPrivacy": {"requestType": "ReqCheckPrivacy", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchRPGBattleHistory": {"requestType": "ReqFetchRPGBattleHistory", "responseType": "ResFetchRPGBattleHistory"}, "fetchRPGBattleHistoryV2": {"requestType": "ReqFetchRPGBattleHistory", "responseType": "ResFetchRPGBattleHistoryV2"}, "receiveRPGRewards": {"requestType": "ReqReceiveRPGRewards", "responseType": "ResReceiveRPGRewards"}, "receiveRPGReward": {"requestType": "ReqReceiveRPGReward", "responseType": "ResReceiveRPGRewards"}, "buyArenaTicket": {"requestType": "ReqBuyArenaTicket", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "enterArena": {"requestType": "ReqEnterArena", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveArenaReward": {"requestType": "ReqArenaReward", "responseType": "ResArenaReward"}, "fetchOBToken": {"requestType": "ReqFetchOBToken", "responseType": "ResFetchOBToken"}, "receiveCharacterRewards": {"requestType": "ReqReceiveCharacterRewards", "responseType": "ResReceiveCharacterRewards"}, "feedActivityFeed": {"requestType": "ReqFeedActivityFeed", "responseType": "ResFeedActivityFeed"}, "sendActivityGiftToFriend": {"requestType": "ReqSendActivityGiftToFriend", "responseType": "ResSendActivityGiftToFriend"}, "receiveActivityGift": {"requestType": "ReqReceiveActivityGift", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveAllActivityGift": {"requestType": "ReqReceiveAllActivityGift", "responseType": "ResReceiveAllActivityGift"}, "fetchFriendGiftActivityData": {"requestType": "ReqFetchFriendGiftActivityData", "responseType": "ResFetchFriendGiftActivityData"}, "openPreChestItem": {"requestType": "ReqOpenPreChestItem", "responseType": "ResOpenPreChestItem"}, "fetchVoteActivity": {"requestType": "ReqFetchVoteActivity", "responseType": "ResFetchVoteActivity"}, "voteActivity": {"requestType": "ReqVoteActivity", "responseType": "ResVoteActivity"}, "unlockActivitySpot": {"requestType": "ReqUnlockActivitySpot", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "unlockActivitySpotEnding": {"requestType": "ReqUnlockActivitySpotEnding", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveActivitySpotReward": {"requestType": "ReqReceiveActivitySpotReward", "responseType": "ResReceiveActivitySpotReward"}, "deleteAccount": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResD<PERSON>teAccount"}, "cancelDeleteAccount": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "logReport": {"requestType": "ReqLogReport", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "bindOauth2": {"requestType": "ReqBindOauth2", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchOauth2Info": {"requestType": "ReqFetchOauth2", "responseType": "ResFetchOauth2"}, "setLoadingImage": {"requestType": "ReqSetLoadingImage", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchShopInterval": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchShopInterval"}, "fetchActivityInterval": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchActivityInterval"}, "fetchRecentFriend": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchrecentFriend"}, "openGacha": {"requestType": "ReqOpenGacha", "responseType": "ResOpenGacha"}, "taskRequest": {"requestType": "ReqTaskRequest", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "simulationActivityTrain": {"requestType": "ReqSimulationActivityTrain", "responseType": "ResSimulationActivityTrain"}, "fetchSimulationGameRecord": {"requestType": "ReqFetchSimulationGameRecord", "responseType": "ResFetchSimulationGameRecord"}, "startSimulationActivityGame": {"requestType": "ReqStartSimulationActivityGame", "responseType": "ResStartSimulationActivityGame"}, "fetchSimulationGameRank": {"requestType": "ReqFetchSimulationGameRank", "responseType": "ResFetchSimulationGameRank"}, "generateCombiningCraft": {"requestType": "ReqGenerateCombiningCraft", "responseType": "ResGenerateCombiningCraft"}, "moveCombiningCraft": {"requestType": "ReqMoveCombiningCraft", "responseType": "ResMoveCombiningCraft"}, "combiningRecycleCraft": {"requestType": "ReqCombiningRecycleCraft", "responseType": "ResCombiningRecycleCraft"}, "recoverCombiningRecycle": {"requestType": "ReqRecoverCombiningRecycle", "responseType": "ResRecoverCombiningRecycle"}, "finishCombiningOrder": {"requestType": "ReqFinishCombiningOrder", "responseType": "ResFinishCombiningOrder"}, "upgradeVillageBuilding": {"requestType": "ReqUpgradeVillageBuilding", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveVillageBuildingReward": {"requestType": "ReqReceiveVillageBuildingReward", "responseType": "ResReceiveVillageBuildingReward"}, "startVillageTrip": {"requestType": "ReqStartVillageTrip", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "receiveVillageTripReward": {"requestType": "ReqReceiveVillageTripReward", "responseType": "ResReceiveVillageTripReward"}, "completeVillageTask": {"requestType": "ReqCompleteVillageTask", "responseType": "ResCompleteVillageTask"}, "getFriendVillageData": {"requestType": "ReqGetFriendVillageData", "responseType": "ResGetFriendVillageData"}, "setVillageWorker": {"requestType": "ReqSetVillageWorker", "responseType": "ResSetVillageWorker"}, "nextRoundVillage": {"requestType": "ReqNextRoundVillage", "responseType": "ResNextRoundVillage"}, "resolveFestivalActivityProposal": {"requestType": "ReqResolveFestivalActivityProposal", "responseType": "ResResolveFestivalActivityProposal"}, "resolveFestivalActivityEvent": {"requestType": "ReqResolveFestivalActivityEvent", "responseType": "ResResolveFestivalActivityEvent"}, "buyFestivalProposal": {"requestType": "ReqBuyFestivalProposal", "responseType": "ResBuyFestivalProposal"}, "islandActivityMove": {"requestType": "ReqIslandActivityMove", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "islandActivityBuy": {"requestType": "ReqIslandActivityBuy", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "islandActivitySell": {"requestType": "ReqIslandActivitySell", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "islandActivityTidyBag": {"requestType": "ReqIslandActivityTidyBag", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "islandActivityUnlockBagGrid": {"requestType": "ReqIslandActivityUnlockBagGrid", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "createCustomizedContest": {"requestType": "ReqCreateCustomizedContest", "responseType": "ResCreateCustomizedContest"}, "fetchManagerCustomizedContestList": {"requestType": "ReqFetchmanagerCustomizedContestList", "responseType": "ResFetchManagerCustomizedContestList"}, "fetchManagerCustomizedContest": {"requestType": "ReqFetchManagerCustomizedContest", "responseType": "ResFetchManagerCustomizedContest"}, "updateManagerCustomizedContest": {"requestType": "ReqUpdateManagerCustomizedContest", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchContestPlayerRank": {"requestType": "ReqFetchContestPlayerRank", "responseType": "ResFetchContestPlayerRank"}, "fetchReadyPlayerList": {"requestType": "ReqFetchReadyPlayerList", "responseType": "ResFetchReadyPlayerList"}, "createGamePlan": {"requestType": "ReqCreateGamePlan", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "generateContestManagerLoginCode": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResGenerateContestManagerLoginCode"}, "amuletActivityFetchInfo": {"requestType": "ReqAmuletActivityFetchInfo", "responseType": "ResAmuletActivityFetchInfo"}, "amuletActivityFetchBrief": {"requestType": "ReqAmuletActivityFetchBrief", "responseType": "ResAmuletActivityFetchBrief"}, "amuletActivityStartGame": {"requestType": "ReqAmuletActivityStartGame", "responseType": "ResAmuletActivityStartGame"}, "amuletActivityOperate": {"requestType": "ReqAmuletActivityOperate", "responseType": "ResAmuletActivityOperate"}, "amuletActivityChangeHands": {"requestType": "ReqAmuletActivityChangeHands", "responseType": "ResAmuletActivityChangeHands"}, "amuletActivityUpgrade": {"requestType": "ReqAmuletActivityUpgrade", "responseType": "ResAmuletActivityUpgrade"}, "amuletActivityBuy": {"requestType": "ReqAmuletActivityBuy", "responseType": "ResAmuletActivityBuy"}, "amuletActivitySelectPack": {"requestType": "ReqAmuletActivitySelectPack", "responseType": "ResAmuletActivitySelectPack"}, "amuletActivitySellEffect": {"requestType": "ReqAmuletActivitySellEffect", "responseType": "ResAmuletActivitySellEffect"}, "amuletActivityEffectSort": {"requestType": "ReqAmuletActivityEffectSort", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "amuletActivityGiveup": {"requestType": "ReqAmuletActivityGiveup", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "amuletActivityRefreshShop": {"requestType": "ReqAmuletActivityRefreshShop", "responseType": "ResAmuletActivityRefreshShop"}, "amuletActivitySelectFreeEffect": {"requestType": "ReqAmuletActivitySelectFreeEffect", "responseType": "ResAmuletActivitySelectFreeEffect"}, "amuletActivityUpgradeShopBuff": {"requestType": "ReqAmuletActivityUpgradeShopBuff", "responseType": "ResAmuletActivityUpgradeShopBuff"}, "amuletActivityEndShopping": {"requestType": "ReqAmuletActivityEndShopping", "responseType": "ResAmuletActivityEndShopping"}, "amuletActivitySetSkillLevel": {"requestType": "ReqAmuletActivitySetSkillLevel", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "amuletActivityMaintainInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResAmuletActivityMaintainInfo"}, "amuletActivitySelectRewardPack": {"requestType": "ReqAmuletActivitySelectRewardPack", "responseType": "ResAmuletActivitySelectRewardPack"}, "amuletActivityReceiveTaskReward": {"requestType": "ReqAmuletActivityReceiveTaskReward", "responseType": "ResAmuletActivityReceiveTaskReward"}, "storyActivityUnlock": {"requestType": "ReqStoryActivityUnlock", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "storyActivityUnlockEnding": {"requestType": "ReqStoryActivityUnlockEnding", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "storyActivityReceiveEndingReward": {"requestType": "ReqStoryActivityReceiveEndingReward", "responseType": "ResStoryReward"}, "storyActivityReceiveFinishReward": {"requestType": "ReqStoryActivityReceiveFinishReward", "responseType": "ResStoryReward"}, "storyActivityReceiveAllFinishReward": {"requestType": "ReqStoryActivityReceiveAllFinishReward", "responseType": "ResStoryReward"}, "storyActivityUnlockEndingAndReceive": {"requestType": "ReqStoryActivityUnlockEndingAndReceive", "responseType": "ResStoryActivityUnlockEndingAndReceive"}, "fetchActivityRank": {"requestType": "ReqFetchActivityRank", "responseType": "ResFetchActivityRank"}, "setVerifiedHidden": {"requestType": "ReqSetVerifiedHidden", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchQuestionnaireList": {"requestType": "ReqFetchQuestionnaireList", "responseType": "ResFetchQuestionnaireList"}, "fetchQuestionnaireDetail": {"requestType": "ReqFetchQuestionnaireDetail", "responseType": "ResFetchQuestionnaireDetail"}, "submitQuestionnaire": {"requestType": "ReqSubmitQuestionnaire", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "setFriendRoomRandomBotChar": {"requestType": "ReqSetFriendRoomRandomBotChar", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchAccountGameHuRecords": {"requestType": "ReqFetchAccountGameHuRecords", "responseType": "ResFetchAccountGameHuRecords"}, "fetchAccountInfoExtra": {"requestType": "ReqFetchAccountInfoExtra", "responseType": "ResFetchAccountInfoExtra"}, "setAccountFavoriteHu": {"requestType": "ReqSetAccountFavoriteHu", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchSeerReport": {"requestType": "ReqFetchSeerReport", "responseType": "ResFetchSeerReport"}, "createSeerReport": {"requestType": "ReqCreateSeerReport", "responseType": "ResCreateSeerReport"}, "fetchSeerReportList": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchSeerReportList"}, "fetchSeerInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchSeerInfo"}, "selectChestChooseUpActivity": {"requestType": "ReqSelectChestChooseUp", "responseType": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "generateAnnualReportToken": {"requestType": "ReqGenerateAnnualReportToken", "responseType": "ResGenerateAnnualReportToken"}, "fetchAnnualReportInfo": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResFetchAnnualReportInfo"}, "remarkFriend": {"requestType": "ReqRemarkFriend", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "simV2ActivityFetchInfo": {"requestType": "ReqSimV2ActivityFetchInfo", "responseType": "ResSimV2ActivityFetchInfo"}, "simV2ActivityStartSeason": {"requestType": "ReqSimV2ActivityStartSeason", "responseType": "ResSimV2ActivityStartSeason"}, "simV2ActivityTrain": {"requestType": "ReqSimV2ActivityTrain", "responseType": "ResSimV2ActivityTrain"}, "simV2ActivitySelectEvent": {"requestType": "ReqSimV2ActivitySelectEvent", "responseType": "ResSimV2ActivitySelectEvent"}, "simV2ActivityStartMatch": {"requestType": "ReqSimV2ActivityStartMatch", "responseType": "ResSimV2ActivityStartMatch"}, "simV2ActivityEndMatch": {"requestType": "ReqSimV2ActivityEndMatch", "responseType": "ResSimV2ActivityEndMatch"}, "simV2ActivityGiveUp": {"requestType": "ReqSimV2ActivityGiveUp", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "simV2ActivitySetUpgrade": {"requestType": "ReqSimV2ActivitySetUpgrade", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}}}, "ResConnectionInfo": {"fields": {"error": {"type": "Error", "id": 1}, "client_endpoint": {"type": "NetworkEndpoint", "id": 2}}}, "ResFetchQueueInfo": {"fields": {"error": {"type": "Error", "id": 1}, "remain": {"type": "uint32", "id": 2}, "rank": {"type": "uint32", "id": 3}}}, "ReqOpenidCheck": {"fields": {"type": {"type": "uint32", "id": 1}, "token": {"type": "string", "id": 2}}}, "ReqSignupAccount": {"fields": {"account": {"type": "string", "id": 1}, "password": {"type": "string", "id": 2}, "code": {"type": "string", "id": 3}, "type": {"type": "uint32", "id": 4}, "device": {"type": "ClientDeviceInfo", "id": 5}, "client_version_string": {"type": "string", "id": 6}, "tag": {"type": "string", "id": 7}}}, "ResSignupAccount": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqLogin": {"fields": {"account": {"type": "string", "id": 1}, "password": {"type": "string", "id": 2}, "reconnect": {"type": "bool", "id": 3}, "device": {"type": "ClientDeviceInfo", "id": 4}, "random_key": {"type": "string", "id": 5}, "client_version": {"type": "ClientVersionInfo", "id": 6}, "gen_access_token": {"type": "bool", "id": 7}, "currency_platforms": {"rule": "repeated", "type": "uint32", "id": 8}, "type": {"type": "uint32", "id": 9}, "version": {"type": "uint32", "id": 10}, "client_version_string": {"type": "string", "id": 11}, "tag": {"type": "string", "id": 12}}}, "ResLogin": {"fields": {"error": {"type": "Error", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "account": {"type": "Account", "id": 3}, "game_info": {"type": "GameConnectInfo", "id": 4}, "has_unread_announcement": {"type": "bool", "id": 5}, "access_token": {"type": "string", "id": 6}, "signup_time": {"type": "uint32", "id": 7}, "is_id_card_authed": {"type": "bool", "id": 8}, "country": {"type": "string", "id": 9}, "logined_version": {"rule": "repeated", "type": "uint32", "id": 10}, "rewarded_version": {"rule": "repeated", "type": "uint32", "id": 11}}}, "ReqPrepareLogin": {"fields": {"access_token": {"type": "string", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "ResFastLogin": {"fields": {"error": {"type": "Error", "id": 1}, "game_info": {"type": "GameConnectInfo", "id": 2}, "room": {"type": "Room", "id": 3}}}, "ReqEmailLogin": {"fields": {"email": {"type": "string", "id": 1}, "password": {"type": "string", "id": 2}, "reconnect": {"type": "bool", "id": 3}, "device": {"type": "ClientDeviceInfo", "id": 4}, "random_key": {"type": "string", "id": 5}, "client_version": {"type": "string", "id": 6}, "gen_access_token": {"type": "bool", "id": 7}, "currency_platforms": {"rule": "repeated", "type": "uint32", "id": 8}}}, "ReqBindAccount": {"fields": {"account": {"type": "string", "id": 1}, "password": {"type": "string", "id": 2}}}, "ReqCreatePhoneVerifyCode": {"fields": {"phone": {"type": "string", "id": 1}, "usage": {"type": "uint32", "id": 2}}}, "ReqCreateEmailVerifyCode": {"fields": {"email": {"type": "string", "id": 1}, "usage": {"type": "uint32", "id": 2}}}, "ReqVerifyCodeForSecure": {"fields": {"code": {"type": "string", "id": 1}, "operation": {"type": "uint32", "id": 2}}}, "ResVerfiyCodeForSecure": {"fields": {"error": {"type": "Error", "id": 1}, "secure_token": {"type": "string", "id": 2}}}, "ReqBindPhoneNumber": {"fields": {"code": {"type": "string", "id": 1}, "phone": {"type": "string", "id": 2}, "password": {"type": "string", "id": 3}, "multi_bind_version": {"type": "bool", "id": 4}}}, "ReqUnbindPhoneNumber": {"fields": {"code": {"type": "string", "id": 1}, "phone": {"type": "string", "id": 2}, "password": {"type": "string", "id": 3}}}, "ResFetchPhoneLoginBind": {"fields": {"error": {"type": "Error", "id": 1}, "phone_login": {"type": "uint32", "id": 2}}}, "ReqCreatePhoneLoginBind": {"fields": {"password": {"type": "string", "id": 1}}}, "ReqBindEmail": {"fields": {"email": {"type": "string", "id": 1}, "code": {"type": "string", "id": 2}, "password": {"type": "string", "id": 3}}}, "ReqModifyPassword": {"fields": {"new_password": {"type": "string", "id": 1}, "old_password": {"type": "string", "id": 2}, "secure_token": {"type": "string", "id": 3}}}, "ReqOauth2Auth": {"fields": {"type": {"type": "uint32", "id": 1}, "code": {"type": "string", "id": 2}, "uid": {"type": "string", "id": 3}, "client_version_string": {"type": "string", "id": 4}}}, "ResOauth2Auth": {"fields": {"error": {"type": "Error", "id": 1}, "access_token": {"type": "string", "id": 2}}}, "ReqOauth2Check": {"fields": {"type": {"type": "uint32", "id": 1}, "access_token": {"type": "string", "id": 2}}}, "ResOauth2Check": {"fields": {"error": {"type": "Error", "id": 1}, "has_account": {"type": "bool", "id": 2}}}, "ReqOauth2Signup": {"fields": {"type": {"type": "uint32", "id": 1}, "access_token": {"type": "string", "id": 2}, "email": {"type": "string", "id": 3}, "advertise_str": {"type": "string", "id": 4}, "device": {"type": "ClientDeviceInfo", "id": 5}, "client_version": {"type": "ClientVersionInfo", "id": 6}, "client_version_string": {"type": "string", "id": 7}, "tag": {"type": "string", "id": 8}}}, "ResOauth2Signup": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqOauth2Login": {"fields": {"type": {"type": "uint32", "id": 1}, "access_token": {"type": "string", "id": 2}, "reconnect": {"type": "bool", "id": 3}, "device": {"type": "ClientDeviceInfo", "id": 4}, "random_key": {"type": "string", "id": 5}, "client_version": {"type": "ClientVersionInfo", "id": 6}, "gen_access_token": {"type": "bool", "id": 7}, "currency_platforms": {"rule": "repeated", "type": "uint32", "id": 8}, "version": {"type": "uint32", "id": 9}, "client_version_string": {"type": "string", "id": 10}, "tag": {"type": "string", "id": 11}}}, "ReqDMMPreLogin": {"fields": {"finish_url": {"type": "string", "id": 1}}}, "ResDMMPreLogin": {"fields": {"error": {"type": "Error", "id": 2}, "parameter": {"type": "string", "id": 1}}}, "ReqLogout": {"fields": {}}, "ResLogout": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqHeatBeat": {"fields": {"no_operation_counter": {"type": "uint32", "id": 1}}}, "ReqSearchAccountByEidLobby": {"fields": {"eid": {"type": "uint32", "id": 1}}}, "ResSearchAccountbyEidLobby": {"fields": {"error": {"type": "Error", "id": 1}, "account_id": {"type": "uint32", "id": 2}}}, "ReqLoginBeat": {"fields": {"contract": {"type": "string", "id": 1}}}, "ReqJoinMatchQueue": {"fields": {"match_mode": {"type": "uint32", "id": 1}, "client_version_string": {"type": "string", "id": 2}}}, "ReqCancelMatchQueue": {"fields": {"match_mode": {"type": "uint32", "id": 1}}}, "ReqAccountInfo": {"fields": {"account_id": {"type": "uint32", "id": 1}}}, "ResAccountInfo": {"fields": {"error": {"type": "Error", "id": 1}, "account": {"type": "lq.Account", "id": 2}, "room": {"type": "Room", "id": 3}}}, "ReqCreateNickname": {"fields": {"nickname": {"type": "string", "id": 1}, "advertise_str": {"type": "string", "id": 2}, "tag": {"type": "string", "id": 3}}}, "ReqModifyNickname": {"fields": {"nickname": {"type": "string", "id": 1}, "use_item_id": {"type": "uint32", "id": 2}}}, "ReqModifyBirthday": {"fields": {"birthday": {"type": "int32", "id": 1}}}, "ResSelfRoom": {"fields": {"error": {"type": "Error", "id": 1}, "room": {"type": "Room", "id": 2}}}, "ResFetchGamingInfo": {"fields": {"error": {"type": "Error", "id": 1}, "game_info": {"type": "GameConnectInfo", "id": 2}}}, "ReqCreateRoom": {"fields": {"player_count": {"type": "uint32", "id": 1}, "mode": {"type": "GameMode", "id": 2}, "public_live": {"type": "bool", "id": 3}, "client_version_string": {"type": "string", "id": 4}, "pre_rule": {"type": "string", "id": 5}}}, "ResCreateRoom": {"fields": {"error": {"type": "Error", "id": 1}, "room": {"type": "Room", "id": 2}}}, "ReqJoinRoom": {"fields": {"room_id": {"type": "uint32", "id": 1}, "client_version_string": {"type": "string", "id": 2}}}, "ResJoinRoom": {"fields": {"error": {"type": "Error", "id": 1}, "room": {"type": "Room", "id": 2}}}, "ReqRoomReady": {"fields": {"ready": {"type": "bool", "id": 1}}}, "ReqRoomDressing": {"fields": {"dressing": {"type": "bool", "id": 1}}}, "ReqRoomStart": {"fields": {}}, "ReqRoomKickPlayer": {"fields": {"id": {"type": "uint32", "id": 1}}}, "ReqModifyRoom": {"fields": {"robot_count": {"type": "uint32", "id": 1}}}, "ReqAddRoomRobot": {"fields": {"position": {"type": "uint32", "id": 1}}}, "ReqChangeAvatar": {"fields": {"avatar_id": {"type": "uint32", "id": 1}}}, "ReqAccountStatisticInfo": {"fields": {"account_id": {"type": "uint32", "id": 1}}}, "ResAccountStatisticInfo": {"fields": {"error": {"type": "Error", "id": 1}, "statistic_data": {"rule": "repeated", "type": "AccountStatisticData", "id": 2}, "detail_data": {"type": "AccountDetailStatisticV2", "id": 3}}}, "ResAccountChallengeRankInfo": {"fields": {"error": {"type": "Error", "id": 1}, "season_info": {"rule": "repeated", "type": "ChallengeRank", "id": 2}}, "nested": {"ChallengeRank": {"fields": {"season": {"type": "uint32", "id": 1}, "rank": {"type": "uint32", "id": 2}, "level": {"type": "uint32", "id": 3}}}}}, "ResAccountCharacterInfo": {"fields": {"error": {"type": "Error", "id": 2}, "unlock_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ReqShopPurchase": {"fields": {"type": {"type": "string", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "ResShopPurchase": {"fields": {"error": {"type": "Error", "id": 1}, "update": {"type": "AccountUpdate", "id": 2}}}, "ReqGameRecord": {"fields": {"game_uuid": {"type": "string", "id": 1}, "client_version_string": {"type": "string", "id": 2}}}, "ResGameRecord": {"fields": {"error": {"type": "Error", "id": 1}, "head": {"type": "RecordGame", "id": 3}, "data": {"type": "bytes", "id": 4}, "data_url": {"type": "string", "id": 5}}}, "ReqGameRecordList": {"fields": {"start": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "ResGameRecordList": {"fields": {"error": {"type": "Error", "id": 1}, "total_count": {"type": "uint32", "id": 2}, "record_list": {"rule": "repeated", "type": "RecordGame", "id": 3}}}, "ReqGameRecordListV2": {"fields": {"tag": {"type": "uint32", "id": 1}, "begin_time": {"type": "uint32", "id": 2}, "end_time": {"type": "uint32", "id": 3}, "ranks": {"rule": "repeated", "type": "uint32", "id": 4}, "modes": {"rule": "repeated", "type": "uint32", "id": 5}, "max_hu_type": {"type": "uint32", "id": 6}, "level_mode": {"rule": "repeated", "type": "uint32", "id": 7}}}, "ResGameRecordListV2": {"fields": {"error": {"type": "Error", "id": 1}, "iterator": {"type": "string", "id": 2}, "iterator_expire": {"type": "uint32", "id": 3}, "actual_begin_time": {"type": "uint32", "id": 4}, "actual_end_time": {"type": "uint32", "id": 5}}}, "ReqNextGameRecordList": {"fields": {"iterator": {"type": "string", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResNextGameRecordList": {"fields": {"error": {"type": "Error", "id": 1}, "next": {"type": "bool", "id": 2}, "entries": {"rule": "repeated", "type": "RecordListEntry", "id": 3}, "iterator_expire": {"type": "uint32", "id": 4}, "next_end_time": {"type": "uint32", "id": 5}}}, "ResCollectedGameRecordList": {"fields": {"error": {"type": "Error", "id": 1}, "record_list": {"rule": "repeated", "type": "RecordCollectedData", "id": 2}, "record_collect_limit": {"type": "uint32", "id": 3}}}, "ReqGameRecordsDetail": {"fields": {"uuid_list": {"rule": "repeated", "type": "string", "id": 1}}}, "ResGameRecordsDetail": {"fields": {"error": {"type": "Error", "id": 1}, "record_list": {"rule": "repeated", "type": "RecordGame", "id": 2}}}, "ReqGameRecordsDetailV2": {"fields": {"uuid_list": {"rule": "repeated", "type": "string", "id": 1}}}, "ResGameRecordsDetailV2": {"fields": {"error": {"type": "Error", "id": 1}, "entries": {"rule": "repeated", "type": "RecordListEntry", "id": 2}}}, "ReqAddCollectedGameRecord": {"fields": {"uuid": {"type": "string", "id": 1}, "remarks": {"type": "string", "id": 2}, "start_time": {"type": "uint32", "id": 3}, "end_time": {"type": "uint32", "id": 4}}}, "ResAddCollectedGameRecord": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqRemoveCollectedGameRecord": {"fields": {"uuid": {"type": "string", "id": 1}}}, "ResRemoveCollectedGameRecord": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqChangeCollectedGameRecordRemarks": {"fields": {"uuid": {"type": "string", "id": 1}, "remarks": {"type": "string", "id": 2}}}, "ResChangeCollectedGameRecordRemarks": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqLevelLeaderboard": {"fields": {"type": {"type": "uint32", "id": 1}}}, "ResLevelLeaderboard": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 2}, "self_rank": {"type": "uint32", "id": 3}}, "nested": {"Item": {"fields": {"account_id": {"type": "uint32", "id": 1}, "level": {"type": "AccountLevel", "id": 2}}}}}, "ReqChallangeLeaderboard": {"fields": {"season": {"type": "uint32", "id": 1}}}, "ResChallengeLeaderboard": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 2}, "self_rank": {"type": "uint32", "id": 3}}, "nested": {"Item": {"fields": {"account_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}, "nickname": {"type": "string", "id": 3}}}}}, "ReqMutiChallengeLevel": {"fields": {"account_id_list": {"rule": "repeated", "type": "uint32", "id": 1}, "season": {"type": "uint32", "id": 2}}}, "ResMutiChallengeLevel": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 2}}, "nested": {"Item": {"fields": {"account_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}}}}}, "ReqMultiAccountId": {"fields": {"account_id_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ResMultiAccountBrief": {"fields": {"error": {"type": "Error", "id": 1}, "players": {"rule": "repeated", "type": "PlayerBaseView", "id": 2}}}, "ResFriendList": {"fields": {"error": {"type": "Error", "id": 1}, "friends": {"rule": "repeated", "type": "Friend", "id": 2}, "friend_max_count": {"type": "uint32", "id": 3}, "friend_count": {"type": "uint32", "id": 4}}}, "ResFriendApplyList": {"fields": {"error": {"type": "Error", "id": 1}, "applies": {"rule": "repeated", "type": "FriendApply", "id": 2}}, "nested": {"FriendApply": {"fields": {"account_id": {"type": "uint32", "id": 1}, "apply_time": {"type": "uint32", "id": 2}}}}}, "ReqApplyFriend": {"fields": {"target_id": {"type": "uint32", "id": 1}}}, "ReqHandleFriendApply": {"fields": {"target_id": {"type": "uint32", "id": 1}, "method": {"type": "uint32", "id": 2}}}, "ReqRemoveFriend": {"fields": {"target_id": {"type": "uint32", "id": 1}}}, "ReqSearchAccountByPattern": {"fields": {"search_next": {"type": "bool", "id": 1}, "pattern": {"type": "string", "id": 2}}}, "ResSearchAccountByPattern": {"fields": {"error": {"type": "Error", "id": 1}, "is_finished": {"type": "bool", "id": 2}, "match_accounts": {"rule": "repeated", "type": "uint32", "id": 3}, "decode_id": {"type": "uint32", "id": 4}}}, "ReqAccountList": {"fields": {"account_id_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ResAccountStates": {"fields": {"error": {"type": "Error", "id": 1}, "states": {"rule": "repeated", "type": "AccountActiveState", "id": 2}}}, "ReqSearchAccountById": {"fields": {"account_id": {"type": "uint32", "id": 1}}}, "ResSearchAccountById": {"fields": {"error": {"type": "Error", "id": 1}, "player": {"type": "PlayerBaseView", "id": 2}}}, "ResBagInfo": {"fields": {"error": {"type": "Error", "id": 1}, "bag": {"type": "Bag", "id": 2}}}, "ReqUseBagItem": {"fields": {"item_id": {"type": "uint32", "id": 1}}}, "ReqOpenManualItem": {"fields": {"item_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "select_id": {"type": "uint32", "id": 3}}}, "ReqOpenRandomRewardItem": {"fields": {"item_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResOpenRandomRewardItem": {"fields": {"error": {"type": "Error", "id": 1}, "results": {"rule": "repeated", "type": "OpenResult", "id": 2}}}, "ReqOpenAllRewardItem": {"fields": {"item_id": {"type": "uint32", "id": 1}}}, "ResOpenAllRewardItem": {"fields": {"error": {"type": "Error", "id": 1}, "results": {"rule": "repeated", "type": "OpenResult", "id": 2}}}, "ReqComposeShard": {"fields": {"item_id": {"type": "uint32", "id": 1}}}, "ReqFetchAnnouncement": {"fields": {"lang": {"type": "string", "id": 1}, "platform": {"type": "string", "id": 2}}}, "ResAnnouncement": {"fields": {"error": {"type": "Error", "id": 1}, "announcements": {"rule": "repeated", "type": "Announcement", "id": 2}, "sort": {"rule": "repeated", "type": "uint32", "id": 3}, "read_list": {"rule": "repeated", "type": "uint32", "id": 4}}}, "ResMailInfo": {"fields": {"error": {"type": "Error", "id": 1}, "mails": {"rule": "repeated", "type": "Mail", "id": 2}}}, "ReqReadMail": {"fields": {"mail_id": {"type": "uint32", "id": 1}}}, "ReqDeleteMail": {"fields": {"mail_id": {"type": "uint32", "id": 1}}}, "ReqTakeAttachment": {"fields": {"mail_id": {"type": "uint32", "id": 1}}}, "ReqReceiveAchievementGroupReward": {"fields": {"group_id": {"type": "uint32", "id": 1}}}, "ResReceiveAchievementGroupReward": {"fields": {"error": {"type": "Error", "id": 1}, "execute_reward": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqReceiveAchievementReward": {"fields": {"achievement_id": {"type": "uint32", "id": 1}}}, "ResReceiveAchievementReward": {"fields": {"error": {"type": "Error", "id": 1}, "execute_reward": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ResFetchAchievementRate": {"fields": {"error": {"type": "Error", "id": 2}, "rate": {"rule": "repeated", "type": "AchievementRate", "id": 1}}, "nested": {"AchievementRate": {"fields": {"id": {"type": "uint32", "id": 1}, "rate": {"type": "uint32", "id": 2}}}}}, "ResAchievement": {"fields": {"error": {"type": "Error", "id": 1}, "progresses": {"rule": "repeated", "type": "AchievementProgress", "id": 2}, "rewarded_group": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ResTitleList": {"fields": {"error": {"type": "Error", "id": 1}, "title_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ReqUseTitle": {"fields": {"title": {"type": "uint32", "id": 1}}}, "ReqBuyShiLian": {"fields": {"type": {"type": "uint32", "id": 1}}}, "ReqUpdateClientValue": {"fields": {"key": {"type": "uint32", "id": 1}, "value": {"type": "uint32", "id": 2}}}, "ResClientValue": {"fields": {"error": {"type": "Error", "id": 3}, "datas": {"rule": "repeated", "type": "Value", "id": 1}, "recharged_count": {"type": "uint32", "id": 2}}, "nested": {"Value": {"fields": {"key": {"type": "uint32", "id": 1}, "value": {"type": "uint32", "id": 2}}}}}, "ReqClientMessage": {"fields": {"timestamp": {"type": "uint32", "id": 1}, "message": {"type": "string", "id": 2}}}, "ReqCurrentMatchInfo": {"fields": {"mode_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ResCurrentMatchInfo": {"fields": {"error": {"type": "Error", "id": 1}, "matches": {"rule": "repeated", "type": "CurrentMatchInfo", "id": 2}}, "nested": {"CurrentMatchInfo": {"fields": {"mode_id": {"type": "uint32", "id": 1}, "playing_count": {"type": "uint32", "id": 2}}}}}, "ReqUserComplain": {"fields": {"target_id": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "content": {"type": "string", "id": 3}, "game_uuid": {"type": "string", "id": 4}, "round_info": {"type": "GameRoundInfo", "id": 5}}, "nested": {"GameRoundInfo": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "seat": {"type": "uint32", "id": 4}, "xun": {"type": "uint32", "id": 5}}}}}, "ReqReadAnnouncement": {"fields": {"announcement_id": {"type": "uint32", "id": 1}, "announcement_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResReviveCoinInfo": {"fields": {"error": {"type": "Error", "id": 1}, "has_gained": {"type": "bool", "id": 2}}}, "ResDailyTask": {"fields": {"error": {"type": "Error", "id": 1}, "progresses": {"rule": "repeated", "type": "TaskProgress", "id": 2}, "has_refresh_count": {"type": "bool", "id": 3}, "max_daily_task_count": {"type": "uint32", "id": 4}, "refresh_count": {"type": "uint32", "id": 5}}}, "ReqRefreshDailyTask": {"fields": {"task_id": {"type": "uint32", "id": 1}}}, "ResRefreshDailyTask": {"fields": {"error": {"type": "Error", "id": 1}, "progress": {"type": "TaskProgress", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}}}, "ReqUseGiftCode": {"fields": {"code": {"type": "string", "id": 1}}}, "ResUseGiftCode": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 6}}}, "ResUseSpecialGiftCode": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqSendClientMessage": {"fields": {"target_id": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "content": {"type": "string", "id": 3}}}, "ReqGameLiveInfo": {"fields": {"game_uuid": {"type": "string", "id": 1}}}, "ResGameLiveInfo": {"fields": {"error": {"type": "Error", "id": 1}, "left_start_seconds": {"type": "uint32", "id": 2}, "live_head": {"type": "GameLiveHead", "id": 3}, "segments": {"rule": "repeated", "type": "GameLiveSegmentUri", "id": 4}, "now_millisecond": {"type": "uint32", "id": 5}}}, "ReqGameLiveLeftSegment": {"fields": {"game_uuid": {"type": "string", "id": 1}, "last_segment_id": {"type": "uint32", "id": 2}}}, "ResGameLiveLeftSegment": {"fields": {"error": {"type": "Error", "id": 1}, "live_state": {"type": "uint32", "id": 2}, "segments": {"rule": "repeated", "type": "GameLiveSegmentUri", "id": 4}, "now_millisecond": {"type": "uint32", "id": 5}, "segment_end_millisecond": {"type": "uint32", "id": 6}}}, "ReqGameLiveList": {"fields": {"filter_id": {"type": "uint32", "id": 1}}}, "ResGameLiveList": {"fields": {"error": {"type": "Error", "id": 1}, "live_list": {"rule": "repeated", "type": "GameLiveHead", "id": 2}}}, "ResCommentSetting": {"fields": {"error": {"type": "Error", "id": 1}, "comment_allow": {"type": "uint32", "id": 2}}}, "ReqUpdateCommentSetting": {"fields": {"comment_allow": {"type": "uint32", "id": 1}}}, "ReqFetchCommentList": {"fields": {"target_id": {"type": "uint32", "id": 1}}}, "ResFetchCommentList": {"fields": {"error": {"type": "Error", "id": 1}, "comment_allow": {"type": "uint32", "id": 2}, "comment_id_list": {"rule": "repeated", "type": "uint32", "id": 3}, "last_read_id": {"type": "uint32", "id": 4}}}, "ReqFetchCommentContent": {"fields": {"target_id": {"type": "uint32", "id": 1}, "comment_id_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResFetchCommentContent": {"fields": {"error": {"type": "Error", "id": 1}, "comments": {"rule": "repeated", "type": "CommentItem", "id": 2}}}, "ReqLeaveComment": {"fields": {"target_id": {"type": "uint32", "id": 1}, "content": {"type": "string", "id": 2}}}, "ReqDeleteComment": {"fields": {"target_id": {"type": "uint32", "id": 1}, "delete_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ReqUpdateReadComment": {"fields": {"read_id": {"type": "uint32", "id": 1}}}, "ResFetchRollingNotice": {"fields": {"error": {"type": "Error", "id": 2}, "notice": {"type": "RollingNotice", "id": 3}}}, "ResFetchMaintainNotice": {"fields": {"error": {"type": "Error", "id": 1}, "notice": {"type": "MaintainNotice", "id": 2}}}, "ReqFetchRollingNotice": {"fields": {"lang": {"type": "string", "id": 1}}}, "ResServerTime": {"fields": {"server_time": {"type": "uint32", "id": 1}, "error": {"type": "Error", "id": 2}}}, "ReqPlatformBillingProducts": {"fields": {"shelves_id": {"type": "uint32", "id": 1}}}, "ResPlatformBillingProducts": {"fields": {"error": {"type": "Error", "id": 1}, "products": {"rule": "repeated", "type": "BillingProduct", "id": 2}}}, "ReqCreateBillingOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "payment_platform": {"type": "uint32", "id": 2}, "client_type": {"type": "uint32", "id": 3}, "account_id": {"type": "uint32", "id": 4}, "client_version_string": {"type": "string", "id": 5}}}, "ResCreateBillingOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqSolveGooglePlayOrder": {"fields": {"inapp_purchase_data": {"type": "string", "id": 2}, "inapp_data_signature": {"type": "string", "id": 3}}}, "ReqSolveGooglePlayOrderV3": {"fields": {"order_id": {"type": "string", "id": 1}, "transaction_id": {"type": "string", "id": 2}, "token": {"type": "string", "id": 3}, "account_id": {"type": "uint32", "id": 4}}}, "ReqCancelGooglePlayOrder": {"fields": {"order_id": {"type": "string", "id": 1}}}, "ReqCreateWechatNativeOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "account_ip": {"type": "string", "id": 4}, "client_version_string": {"type": "string", "id": 5}}}, "ResCreateWechatNativeOrder": {"fields": {"error": {"type": "Error", "id": 1}, "qrcode_buffer": {"type": "string", "id": 2}, "order_id": {"type": "string", "id": 3}}}, "ReqCreateWechatAppOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "account_ip": {"type": "string", "id": 4}, "client_version_string": {"type": "string", "id": 5}}}, "ResCreateWechatAppOrder": {"fields": {"error": {"type": "Error", "id": 1}, "call_wechat_app_param": {"type": "CallWechatAppParam", "id": 2}}, "nested": {"CallWechatAppParam": {"fields": {"appid": {"type": "string", "id": 1}, "partnerid": {"type": "string", "id": 2}, "prepayid": {"type": "string", "id": 3}, "package": {"type": "string", "id": 4}, "noncestr": {"type": "string", "id": 5}, "timestamp": {"type": "string", "id": 6}, "sign": {"type": "string", "id": 7}}}}}, "ReqCreateAlipayOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "alipay_trade_type": {"type": "string", "id": 4}, "return_url": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateAlipayOrder": {"fields": {"error": {"type": "Error", "id": 1}, "alipay_url": {"type": "string", "id": 2}}}, "ReqCreateAlipayScanOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "client_version_string": {"type": "string", "id": 4}}}, "ResCreateAlipayScanOrder": {"fields": {"error": {"type": "Error", "id": 1}, "qrcode_buffer": {"type": "string", "id": 2}, "order_id": {"type": "string", "id": 3}, "qr_code": {"type": "string", "id": 4}}}, "ReqCreateAlipayAppOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "client_version_string": {"type": "string", "id": 4}}}, "ResCreateAlipayAppOrder": {"fields": {"error": {"type": "Error", "id": 1}, "alipay_url": {"type": "string", "id": 2}}}, "ReqCreateJPCreditCardOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPCreditCardOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateJPPaypalOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPPaypalOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateJPAuOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPAuOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateJPDocomoOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPDocomoOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateJPWebMoneyOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPWebMoneyOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateJPSoftbankOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPSoftbankOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateJPPayPayOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPPayPayOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqFetchJPCommonCreditCardOrder": {"fields": {"order_id": {"type": "string", "id": 1}, "account_id": {"type": "uint32", "id": 2}}}, "ResFetchJPCommonCreditCardOrder": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqCreateJPGMOOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateJPGMOOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateYostarOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "order_type": {"type": "uint32", "id": 4}, "client_version_string": {"type": "string", "id": 5}}}, "ResCreateYostarOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateENPaypalOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateENPaypalOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateENJCBOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateENJCBOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateENMasterCardOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateENMasterCardOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateENVisaOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateENVisaOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateENAlipayOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateENAlipayOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateKRPaypalOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateKRPaypalOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateKRJCBOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateKRJCBOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateKRMasterCardOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateKRMasterCardOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateKRVisaOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateKRVisaOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateKRAlipayOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "return_url": {"type": "string", "id": 4}, "access_token": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateKRAlipayOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqCreateDMMOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "client_type": {"type": "uint32", "id": 3}, "client_version_string": {"type": "string", "id": 4}}}, "ResCreateDmmOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}, "transaction_id": {"type": "string", "id": 3}, "dmm_user_id": {"type": "string", "id": 4}, "token": {"type": "string", "id": 5}, "callback_url": {"type": "string", "id": 6}, "request_time": {"type": "string", "id": 9}, "dmm_app_id": {"type": "string", "id": 10}}}, "ReqCreateIAPOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "access_token": {"type": "string", "id": 4}, "debt_order_id": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}}}, "ResCreateIAPOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}}}, "ReqVerificationIAPOrder": {"fields": {"order_id": {"type": "string", "id": 1}, "transaction_id": {"type": "string", "id": 2}, "receipt_data": {"type": "string", "id": 3}, "account_id": {"type": "uint32", "id": 4}}}, "ResVerificationIAPOrder": {"fields": {"error": {"type": "Error", "id": 1}}}, "ReqCreateSteamOrder": {"fields": {"language": {"type": "string", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "client_type": {"type": "uint32", "id": 3}, "goods_id": {"type": "uint32", "id": 4}, "steam_id": {"type": "string", "id": 5}, "debt_order_id": {"type": "string", "id": 6}, "client_version_string": {"type": "string", "id": 7}}}, "ResCreateSteamOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}, "platform_order_id": {"type": "string", "id": 3}}}, "ResRandomCharacter": {"fields": {"error": {"type": "Error", "id": 1}, "enabled": {"type": "bool", "id": 2}, "pool": {"rule": "repeated", "type": "RandomCharacter", "id": 3}}}, "ReqRandomCharacter": {"fields": {"enabled": {"type": "bool", "id": 1}, "pool": {"rule": "repeated", "type": "RandomCharacter", "id": 2}}}, "ReqVerifySteamOrder": {"fields": {"order_id": {"type": "string", "id": 1}, "account_id": {"type": "uint32", "id": 2}}}, "ReqCreateMyCardOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "debt_order_id": {"type": "string", "id": 4}, "client_version_string": {"type": "string", "id": 5}}}, "ResCreateMyCardOrder": {"fields": {"error": {"type": "Error", "id": 1}, "auth_code": {"type": "string", "id": 2}, "order_id": {"type": "string", "id": 3}}}, "ReqVerifyMyCardOrder": {"fields": {"order_id": {"type": "string", "id": 1}, "account_id": {"type": "uint32", "id": 2}}}, "ReqCreatePaypalOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "debt_order_id": {"type": "string", "id": 4}, "client_version_string": {"type": "string", "id": 5}}}, "ResCreatePaypalOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}, "url": {"type": "string", "id": 3}}}, "ReqCreateXsollaOrder": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "client_type": {"type": "uint32", "id": 2}, "account_id": {"type": "uint32", "id": 3}, "payment_method": {"type": "uint32", "id": 4}, "debt_order_id": {"type": "string", "id": 5}, "client_version_string": {"type": "string", "id": 6}, "account_ip": {"type": "string", "id": 7}}}, "ResCreateXsollaOrder": {"fields": {"error": {"type": "Error", "id": 1}, "order_id": {"type": "string", "id": 2}, "url": {"type": "string", "id": 3}}}, "ReqDeliverAA32Order": {"fields": {"account_id": {"type": "uint32", "id": 1}, "nsa_id": {"type": "string", "id": 2}, "nsa_token": {"type": "string", "id": 3}}}, "ReqOpenChest": {"fields": {"chest_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "use_ticket": {"type": "bool", "id": 3}, "choose_up_activity_id": {"type": "uint32", "id": 4}}}, "ResOpenChest": {"fields": {"error": {"type": "Error", "id": 1}, "results": {"rule": "repeated", "type": "OpenResult", "id": 2}, "total_open_count": {"type": "uint32", "id": 3}, "faith_count": {"type": "uint32", "id": 4}, "chest_replace_up": {"rule": "repeated", "type": "ChestReplaceCountData", "id": 5}}, "nested": {"ChestReplaceCountData": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqBuyFromChestShop": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResBuyFromChestShop": {"fields": {"error": {"type": "Error", "id": 1}, "chest_id": {"type": "uint32", "id": 2}, "consume_count": {"type": "uint32", "id": 3}, "faith_count": {"type": "int32", "id": 4}}}, "ResDailySignInInfo": {"fields": {"error": {"type": "Error", "id": 1}, "sign_in_days": {"type": "uint32", "id": 2}}}, "ReqDoActivitySignIn": {"fields": {"activity_id": {"type": "uint32", "id": 2}}}, "ResDoActivitySignIn": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "RewardData", "id": 2}, "sign_in_count": {"type": "uint32", "id": 3}}, "nested": {"RewardData": {"fields": {"resource_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ResCharacterInfo": {"fields": {"error": {"type": "Error", "id": 1}, "characters": {"rule": "repeated", "type": "Character", "id": 2}, "skins": {"rule": "repeated", "type": "uint32", "id": 3}, "main_character_id": {"type": "uint32", "id": 4}, "send_gift_count": {"type": "uint32", "id": 5}, "send_gift_limit": {"type": "uint32", "id": 6}, "finished_endings": {"rule": "repeated", "type": "uint32", "id": 7}, "rewarded_endings": {"rule": "repeated", "type": "uint32", "id": 8}, "character_sort": {"rule": "repeated", "type": "uint32", "id": 9}, "hidden_characters": {"rule": "repeated", "type": "uint32", "id": 10}, "other_character_sort": {"rule": "repeated", "type": "uint32", "id": 11}}}, "ReqUpdateCharacterSort": {"fields": {"sort": {"rule": "repeated", "type": "uint32", "id": 1}, "other_sort": {"rule": "repeated", "type": "uint32", "id": 2}, "hidden_characters": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ReqChangeMainCharacter": {"fields": {"character_id": {"type": "uint32", "id": 1}}}, "ReqChangeCharacterSkin": {"fields": {"character_id": {"type": "uint32", "id": 1}, "skin": {"type": "uint32", "id": 2}}}, "ReqChangeCharacterView": {"fields": {"character_id": {"type": "uint32", "id": 1}, "slot": {"type": "uint32", "id": 2}, "item_id": {"type": "uint32", "id": 3}}}, "ReqSetHiddenCharacter": {"fields": {"chara_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ResSetHiddenCharacter": {"fields": {"error": {"type": "Error", "id": 1}, "hidden_characters": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ReqSendGiftToCharacter": {"fields": {"character_id": {"type": "uint32", "id": 1}, "gifts": {"rule": "repeated", "type": "Gift", "id": 2}}, "nested": {"Gift": {"fields": {"item_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ResSendGiftToCharacter": {"fields": {"error": {"type": "Error", "id": 1}, "level": {"type": "uint32", "id": 2}, "exp": {"type": "uint32", "id": 3}}}, "ReqSellItem": {"fields": {"sells": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 1}}, "nested": {"Item": {"fields": {"item_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ResCommonView": {"fields": {"error": {"type": "Error", "id": 1}, "slots": {"rule": "repeated", "type": "Slot", "id": 2}}, "nested": {"Slot": {"fields": {"slot": {"type": "uint32", "id": 1}, "value": {"type": "uint32", "id": 2}}}}}, "ReqChangeCommonView": {"fields": {"slot": {"type": "uint32", "id": 1}, "value": {"type": "uint32", "id": 2}}}, "ReqSaveCommonViews": {"fields": {"views": {"rule": "repeated", "type": "ViewSlot", "id": 1}, "save_index": {"type": "uint32", "id": 2}, "is_use": {"type": "uint32", "id": 3}, "name": {"type": "string", "id": 4}}}, "ReqCommonViews": {"fields": {"index": {"type": "uint32", "id": 1}}}, "ResCommonViews": {"fields": {"error": {"type": "Error", "id": 2}, "views": {"rule": "repeated", "type": "ViewSlot", "id": 1}, "name": {"type": "string", "id": 3}}}, "ResAllcommonViews": {"fields": {"views": {"rule": "repeated", "type": "Views", "id": 1}, "use": {"type": "uint32", "id": 2}, "error": {"type": "Error", "id": 3}}, "nested": {"Views": {"fields": {"values": {"rule": "repeated", "type": "ViewSlot", "id": 1}, "index": {"type": "uint32", "id": 2}, "name": {"type": "string", "id": 3}}}}}, "ReqUseCommonView": {"fields": {"index": {"type": "uint32", "id": 3}}}, "ReqUpgradeCharacter": {"fields": {"character_id": {"type": "uint32", "id": 1}}}, "ResUpgradeCharacter": {"fields": {"error": {"type": "Error", "id": 1}, "character": {"type": "Character", "id": 2}}}, "ReqFinishedEnding": {"fields": {"character_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}, "ending_id": {"type": "uint32", "id": 3}}}, "ReqGMCommand": {"fields": {"command": {"type": "string", "id": 1}}}, "ResShopInfo": {"fields": {"error": {"type": "Error", "id": 1}, "shop_info": {"type": "ShopInfo", "id": 2}}}, "ReqBuyFromShop": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "ver_price": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 3}, "ver_goods": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 4}}, "nested": {"Item": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ResBuyFromShop": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 2}}}, "ReqBuyFromZHP": {"fields": {"goods_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ReqPayMonthTicket": {"fields": {"ticket_id": {"type": "uint32", "id": 1}}}, "ResPayMonthTicket": {"fields": {"error": {"type": "Error", "id": 1}, "resource_id": {"type": "uint32", "id": 2}, "resource_count": {"type": "uint32", "id": 3}}}, "ReqReshZHPShop": {"fields": {"free_refresh": {"type": "uint32", "id": 1}, "cost_refresh": {"type": "uint32", "id": 2}}}, "ResRefreshZHPShop": {"fields": {"error": {"type": "Error", "id": 1}, "zhp": {"type": "ZHPShop", "id": 2}}}, "ResMonthTicketInfo": {"fields": {"error": {"type": "Error", "id": 2}, "month_ticket_info": {"type": "MonthTicketInfo", "id": 1}}}, "ReqExchangeCurrency": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResServerSettings": {"fields": {"error": {"type": "Error", "id": 2}, "settings": {"type": "ServerSettings", "id": 1}}}, "ResAccountSettings": {"fields": {"error": {"type": "Error", "id": 1}, "settings": {"rule": "repeated", "type": "AccountSetting", "id": 2}}}, "ReqUpdateAccountSettings": {"fields": {"setting": {"type": "AccountSetting", "id": 1}}}, "ResModNicknameTime": {"fields": {"error": {"type": "Error", "id": 2}, "last_mod_time": {"type": "uint32", "id": 1}}}, "ResMisc": {"fields": {"error": {"type": "Error", "id": 1}, "recharged_list": {"rule": "repeated", "type": "uint32", "id": 2}, "faiths": {"rule": "repeated", "type": "MiscFaithData", "id": 3}, "verified_hidden": {"type": "uint32", "id": 4}, "verified_value": {"type": "uint32", "id": 5}, "disable_room_random_bot_char": {"type": "uint32", "id": 6}}, "nested": {"MiscFaithData": {"fields": {"faith_id": {"type": "uint32", "id": 1}, "count": {"type": "int32", "id": 2}}}}}, "ReqModifySignature": {"fields": {"signature": {"type": "string", "id": 1}}}, "ResIDCardInfo": {"fields": {"error": {"type": "Error", "id": 1}, "is_authed": {"type": "bool", "id": 2}, "country": {"type": "string", "id": 3}}}, "ReqUpdateIDCardInfo": {"fields": {"fullname": {"type": "string", "id": 1}, "card_no": {"type": "string", "id": 2}}}, "ResVipReward": {"fields": {"error": {"type": "Error", "id": 1}, "gained_vip_levels": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResFetchRefundOrder": {"fields": {"error": {"type": "Error", "id": 1}, "orders": {"rule": "repeated", "type": "OrderInfo", "id": 2}, "clear_deadline": {"type": "uint32", "id": 3}, "message": {"rule": "repeated", "type": "I18nContext", "id": 4}}, "nested": {"OrderInfo": {"fields": {"success_time": {"type": "uint32", "id": 1}, "goods_id": {"type": "uint32", "id": 2}, "cleared": {"type": "uint32", "id": 3}, "order_id": {"type": "string", "id": 4}}}}}, "ReqGainVipReward": {"fields": {"vip_level": {"type": "uint32", "id": 1}}}, "ReqFetchCustomizedContestList": {"fields": {"start": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "lang": {"type": "string", "id": 3}}}, "ResFetchCustomizedContestList": {"fields": {"error": {"type": "Error", "id": 1}, "contests": {"rule": "repeated", "type": "CustomizedContestBase", "id": 2}, "follow_contests": {"rule": "repeated", "type": "CustomizedContestBase", "id": 3}}}, "ReqFetchCustomizedContestAuthInfo": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResFetchCustomizedContestAuthInfo": {"fields": {"error": {"type": "Error", "id": 1}, "observer_level": {"type": "uint32", "id": 2}}}, "ReqEnterCustomizedContest": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "lang": {"type": "string", "id": 2}}}, "ResEnterCustomizedContest": {"fields": {"error": {"type": "Error", "id": 1}, "detail_info": {"type": "CustomizedContestDetail", "id": 2}, "player_report": {"type": "CustomizedContestPlayerReport", "id": 3}, "is_followed": {"type": "bool", "id": 4}, "state": {"type": "uint32", "id": 5}, "is_admin": {"type": "bool", "id": 6}}}, "ReqFetchCustomizedContestOnlineInfo": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResFetchCustomizedContestOnlineInfo": {"fields": {"error": {"type": "Error", "id": 1}, "online_player": {"type": "uint32", "id": 2}}}, "ReqFetchCustomizedContestByContestId": {"fields": {"contest_id": {"type": "uint32", "id": 1}, "lang": {"type": "string", "id": 2}}}, "ResFetchCustomizedContestByContestId": {"fields": {"error": {"type": "Error", "id": 1}, "contest_info": {"type": "CustomizedContestAbstract", "id": 2}}}, "ReqSignupCustomizedContest": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "client_version_string": {"type": "string", "id": 2}}}, "ResSignupCustomizedContest": {"fields": {"error": {"type": "Error", "id": 1}, "state": {"type": "uint32", "id": 2}}}, "ReqStartCustomizedContest": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "client_version_string": {"type": "string", "id": 2}}}, "ReqStopCustomizedContest": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ReqJoinCustomizedContestChatRoom": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResJoinCustomizedContestChatRoom": {"fields": {"error": {"type": "Error", "id": 1}, "token": {"type": "string", "id": 2}}}, "ReqSayChatMessage": {"fields": {"content": {"type": "string", "id": 1}, "unique_id": {"type": "uint32", "id": 2}}}, "ReqFetchCustomizedContestGameLiveList": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResFetchCustomizedContestGameLiveList": {"fields": {"error": {"type": "Error", "id": 1}, "live_list": {"rule": "repeated", "type": "GameLiveHead", "id": 2}}}, "ReqFetchCustomizedContestGameRecords": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "last_index": {"type": "uint32", "id": 2}, "season_id": {"type": "uint32", "id": 3}}}, "ResFetchCustomizedContestGameRecords": {"fields": {"error": {"type": "Error", "id": 1}, "next_index": {"type": "uint32", "id": 2}, "record_list": {"rule": "repeated", "type": "RecordGame", "id": 3}}}, "ReqTargetCustomizedContest": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResActivityList": {"fields": {"error": {"type": "Error", "id": 1}, "activities": {"rule": "repeated", "type": "Activity", "id": 2}}}, "ResAccountActivityData": {"fields": {"error": {"type": "Error", "id": 1}, "exchange_records": {"rule": "repeated", "type": "ExchangeRecord", "id": 2}, "task_progress_list": {"rule": "repeated", "type": "TaskProgress", "id": 3}, "accumulated_point_list": {"rule": "repeated", "type": "ActivityAccumulatedPointData", "id": 4}, "rank_data_list": {"rule": "repeated", "type": "ActivityRankPointData", "id": 5}, "flip_task_progress_list": {"rule": "repeated", "type": "TaskProgress", "id": 6}, "sign_in_data": {"rule": "repeated", "type": "ActivitySignInData", "id": 7}, "richman_data": {"rule": "repeated", "type": "ActivityRichmanData", "id": 8}, "period_task_progress_list": {"rule": "repeated", "type": "TaskProgress", "id": 9}, "random_task_progress_list": {"rule": "repeated", "type": "TaskProgress", "id": 10}, "chest_up_data": {"rule": "repeated", "type": "ChestUpData", "id": 11}, "sns_data": {"type": "ActivitySNSData", "id": 12}, "mine_data": {"rule": "repeated", "type": "lq.MineActivityData", "id": 13}, "rpg_data": {"rule": "repeated", "type": "lq.RPGActivity", "id": 14}, "arena_data": {"rule": "repeated", "type": "lq.ActivityArenaData", "id": 15}, "feed_data": {"rule": "repeated", "type": "lq.FeedActivityData", "id": 16}, "segment_task_progress_list": {"rule": "repeated", "type": "lq.SegmentTaskProgress", "id": 17}, "vote_records": {"rule": "repeated", "type": "lq.<PERSON><PERSON><PERSON>", "id": 18}, "spot_data": {"rule": "repeated", "type": "lq.ActivitySpotData", "id": 19}, "friend_gift_data": {"rule": "repeated", "type": "lq.ActivityFriendGiftData", "id": 20}, "upgrade_data": {"rule": "repeated", "type": "lq.ActivityUpgradeData", "id": 21}, "gacha_data": {"rule": "repeated", "type": "lq.ActivityGachaUpdateData", "id": 22}, "simulation_data": {"rule": "repeated", "type": "lq.ActivitySimulationData", "id": 23}, "combining_data": {"rule": "repeated", "type": "lq.ActivityCombiningLQData", "id": 24}, "village_data": {"rule": "repeated", "type": "lq.ActivityVillageData", "id": 25}, "festival_data": {"rule": "repeated", "type": "lq.ActivityFestivalData", "id": 26}, "island_data": {"rule": "repeated", "type": "lq.ActivityIslandData", "id": 27}, "story_data": {"rule": "repeated", "type": "lq.ActivityStoryData", "id": 29}, "choose_up_data": {"rule": "repeated", "type": "lq.ActivityChooseUpData", "id": 30}}, "nested": {"ActivitySignInData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "sign_in_count": {"type": "uint32", "id": 2}, "last_sign_in_time": {"type": "uint32", "id": 3}}}, "BuffData": {"fields": {"type": {"type": "uint32", "id": 1}, "remain": {"type": "uint32", "id": 2}, "effect": {"type": "uint32", "id": 3}}}, "ActivityRichmanData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "location": {"type": "uint32", "id": 2}, "finished_count": {"type": "uint32", "id": 3}, "chest_position": {"type": "uint32", "id": 4}, "bank_save": {"type": "uint32", "id": 5}, "exp": {"type": "uint32", "id": 6}, "buff": {"rule": "repeated", "type": "BuffD<PERSON>", "id": 7}}}, "ChestUpData": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ActivitySNSData": {"fields": {"blog": {"rule": "repeated", "type": "SNSBlog", "id": 1}, "liked_id": {"rule": "repeated", "type": "uint32", "id": 2}, "reply": {"rule": "repeated", "type": "SNSReply", "id": 3}}}}}, "SNSBlog": {"fields": {"id": {"type": "uint32", "id": 1}, "read_time": {"type": "uint32", "id": 2}}}, "SNSReply": {"fields": {"id": {"type": "uint32", "id": 1}, "reply_time": {"type": "uint32", "id": 2}}}, "ReqExchangeActivityItem": {"fields": {"exchange_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResExchangeActivityItem": {"fields": {"error": {"type": "Error", "id": 1}, "execute_reward": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqCompleteActivityTask": {"fields": {"task_id": {"type": "uint32", "id": 1}}}, "ReqCompleteActivityTaskBatch": {"fields": {"task_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ReqCompletePeriodActivityTaskBatch": {"fields": {"task_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ReqReceiveActivityFlipTask": {"fields": {"task_id": {"type": "uint32", "id": 1}}}, "ResReceiveActivityFlipTask": {"fields": {"count": {"type": "uint32", "id": 1}, "error": {"type": "Error", "id": 2}}}, "ReqCompleteSegmentTaskReward": {"fields": {"task_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResCompleteSegmentTaskReward": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqFetchActivityFlipInfo": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResFetchActivityFlipInfo": {"fields": {"rewards": {"rule": "repeated", "type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "error": {"type": "Error", "id": 3}}}, "ReqGainAccumulatedPointActivityReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "reward_id": {"type": "uint32", "id": 2}}}, "ReqGainMultiPointActivityReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "reward_id_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ReqFetchRankPointLeaderboard": {"fields": {"leaderboard_id": {"type": "uint32", "id": 1}}}, "ResFetchRankPointLeaderboard": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 2}, "last_refresh_time": {"type": "uint32", "id": 3}}, "nested": {"Item": {"fields": {"account_id": {"type": "uint32", "id": 1}, "rank": {"type": "uint32", "id": 2}, "view": {"type": "PlayerBaseView", "id": 3}, "point": {"type": "uint32", "id": 4}}}}}, "ReqGainRankPointReward": {"fields": {"leaderboard_id": {"type": "uint32", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ReqRichmanNextMove": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResRichmanNextMove": {"fields": {"paths": {"rule": "repeated", "type": "PathData", "id": 1}, "dice": {"type": "uint32", "id": 2}, "location": {"type": "uint32", "id": 3}, "finished_count": {"type": "uint32", "id": 4}, "step": {"type": "uint32", "id": 5}, "buff": {"rule": "repeated", "type": "BuffD<PERSON>", "id": 6}, "bank_save": {"type": "uint32", "id": 7}, "chest_position": {"type": "uint32", "id": 8}, "exp": {"type": "uint32", "id": 9}, "bank_save_add": {"type": "uint32", "id": 10}, "error": {"type": "Error", "id": 11}}, "nested": {"RewardData": {"fields": {"resource_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "origin_count": {"type": "uint32", "id": 3}, "type": {"type": "uint32", "id": 5}}}, "PathData": {"fields": {"location": {"type": "uint32", "id": 1}, "rewards": {"rule": "repeated", "type": "RewardData", "id": 2}, "events": {"rule": "repeated", "type": "uint32", "id": 3}}}, "BuffData": {"fields": {"type": {"type": "uint32", "id": 1}, "remain": {"type": "uint32", "id": 2}, "effect": {"type": "uint32", "id": 3}}}}}, "ReqRichmanSpecialMove": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "step": {"type": "uint32", "id": 2}}}, "ReqRichmanChestInfo": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResRichmanChestInfo": {"fields": {"items": {"rule": "repeated", "type": "ItemData", "id": 1}, "error": {"type": "Error", "id": 2}}, "nested": {"ItemData": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqCreateGameObserveAuth": {"fields": {"game_uuid": {"type": "string", "id": 1}}}, "ResCreateGameObserveAuth": {"fields": {"error": {"type": "Error", "id": 1}, "token": {"type": "string", "id": 2}, "location": {"type": "string", "id": 3}}}, "ReqRefreshGameObserveAuth": {"fields": {"token": {"type": "string", "id": 1}}}, "ResRefreshGameObserveAuth": {"fields": {"error": {"type": "Error", "id": 1}, "ttl": {"type": "uint32", "id": 2}}}, "ResActivityBuff": {"fields": {"error": {"type": "Error", "id": 1}, "buff_list": {"rule": "repeated", "type": "lq.ActivityBuffData", "id": 2}}}, "ReqUpgradeActivityBuff": {"fields": {"buff_id": {"type": "uint32", "id": 1}}}, "ReqUpgradeActivityLevel": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "group": {"type": "uint32", "id": 2}, "count": {"type": "uint32", "id": 3}}}, "ResUpgradeActivityLevel": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqReceiveUpgradeActivityReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResReceiveUpgradeActivityReward": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqReceiveAllActivityGift": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResReceiveAllActivityGift": {"fields": {"error": {"type": "Error", "id": 1}, "rewards": {"rule": "repeated", "type": "ExecuteReward", "id": 2}, "receive_gift": {"rule": "repeated", "type": "ReceiveRewards", "id": 3}}, "nested": {"ReceiveRewards": {"fields": {"id": {"type": "uint32", "id": 1}, "from_account_id": {"type": "uint32", "id": 2}, "item_id": {"type": "uint32", "id": 3}, "count": {"type": "uint32", "id": 4}}}}}, "ResUpgradeChallenge": {"fields": {"error": {"type": "Error", "id": 1}, "task_progress": {"rule": "repeated", "type": "TaskProgress", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}, "level": {"type": "uint32", "id": 4}, "match_count": {"type": "uint32", "id": 5}, "ticket_id": {"type": "uint32", "id": 6}}}, "ResRefreshChallenge": {"fields": {"error": {"type": "Error", "id": 1}, "task_progress": {"rule": "repeated", "type": "TaskProgress", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}, "level": {"type": "uint32", "id": 4}, "match_count": {"type": "uint32", "id": 5}, "ticket_id": {"type": "uint32", "id": 6}}}, "ResFetchChallengeInfo": {"fields": {"error": {"type": "Error", "id": 1}, "task_progress": {"rule": "repeated", "type": "TaskProgress", "id": 2}, "refresh_count": {"type": "uint32", "id": 3}, "level": {"type": "uint32", "id": 4}, "match_count": {"type": "uint32", "id": 5}, "ticket_id": {"type": "uint32", "id": 6}, "rewarded_season": {"rule": "repeated", "type": "uint32", "id": 7}}}, "ReqForceCompleteChallengeTask": {"fields": {"task_id": {"type": "uint32", "id": 1}}}, "ResFetchABMatch": {"fields": {"error": {"type": "Error", "id": 1}, "match_id": {"type": "uint32", "id": 2}, "match_count": {"type": "uint32", "id": 3}, "buy_in_count": {"type": "uint32", "id": 4}, "point": {"type": "uint32", "id": 5}, "rewarded": {"type": "bool", "id": 6}, "match_max_point": {"rule": "repeated", "type": "MatchPoint", "id": 7}, "quit": {"type": "bool", "id": 8}}, "nested": {"MatchPoint": {"fields": {"match_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "ReqStartUnifiedMatch": {"fields": {"match_sid": {"type": "string", "id": 1}, "client_version_string": {"type": "string", "id": 2}}}, "ReqCancelUnifiedMatch": {"fields": {"match_sid": {"type": "string", "id": 1}}}, "ResChallengeSeasonInfo": {"fields": {"error": {"type": "Error", "id": 2}, "challenge_season_list": {"rule": "repeated", "type": "ChallengeInfo", "id": 1}}, "nested": {"ChallengeInfo": {"fields": {"season_id": {"type": "uint32", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "end_time": {"type": "uint32", "id": 3}, "state": {"type": "uint32", "id": 4}}}}}, "ReqReceiveChallengeRankReward": {"fields": {"season_id": {"type": "uint32", "id": 1}}}, "ResReceiveChallengeRankReward": {"fields": {"error": {"type": "Error", "id": 2}, "rewards": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 1}}, "nested": {"Reward": {"fields": {"resource_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqBuyInABMatch": {"fields": {"match_id": {"type": "uint32", "id": 1}}}, "ReqGamePointRank": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResGamePointRank": {"fields": {"error": {"type": "Error", "id": 1}, "rank": {"rule": "repeated", "type": "RankInfo", "id": 2}, "self_rank": {"type": "uint32", "id": 3}}, "nested": {"RankInfo": {"fields": {"account_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "ResFetchSelfGamePointRank": {"fields": {"error": {"type": "Error", "id": 1}, "self_rate": {"type": "uint32", "id": 2}}}, "ReqReadSNS": {"fields": {"id": {"type": "uint32", "id": 1}}}, "ResReadSNS": {"fields": {"error": {"type": "Error", "id": 1}, "sns_content": {"type": "SNSBlog", "id": 2}}}, "ReqReplySNS": {"fields": {"id": {"type": "uint32", "id": 1}}}, "ResReplySNS": {"fields": {"error": {"type": "Error", "id": 1}, "sns_reply": {"type": "SNSReply", "id": 2}}}, "ReqLikeSNS": {"fields": {"id": {"type": "uint32", "id": 1}}}, "ResLikeSNS": {"fields": {"error": {"type": "Error", "id": 1}, "is_liked": {"type": "uint32", "id": 2}}}, "ReqDigMine": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "point": {"type": "Point", "id": 2}}}, "ResDigMine": {"fields": {"error": {"type": "Error", "id": 1}, "map": {"rule": "repeated", "type": "MineReward", "id": 2}, "reward": {"rule": "repeated", "type": "lq.<PERSON><PERSON><PERSON>", "id": 3}}}, "ReqFetchLastPrivacy": {"fields": {"type": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ResFetchLastPrivacy": {"fields": {"error": {"type": "Error", "id": 1}, "privacy": {"rule": "repeated", "type": "PrivacyInfo", "id": 2}}, "nested": {"PrivacyInfo": {"fields": {"type": {"type": "uint32", "id": 1}, "version": {"type": "string", "id": 2}}}}}, "ReqCheckPrivacy": {"fields": {"device_type": {"type": "string", "id": 1}, "versions": {"rule": "repeated", "type": "Versions", "id": 2}}, "nested": {"Versions": {"fields": {"version": {"type": "string", "id": 1}, "type": {"type": "uint32", "id": 3}}}}}, "ReqFetchRPGBattleHistory": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResFetchRPGBattleHistory": {"fields": {"error": {"type": "Error", "id": 1}, "battle_result": {"rule": "repeated", "type": "BattleResult", "id": 2}, "start_state": {"type": "lq.RPGState", "id": 3}, "current_state": {"type": "lq.RPGState", "id": 4}}, "nested": {"BattleResult": {"fields": {"uuid": {"type": "string", "id": 14}, "chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "target": {"type": "uint32", "id": 4}, "damage": {"type": "uint32", "id": 5}, "heal": {"type": "uint32", "id": 6}, "monster_seq": {"type": "uint32", "id": 7}, "chain_atk": {"type": "uint32", "id": 8}, "killed": {"type": "uint32", "id": 9}, "is_luk": {"type": "uint32", "id": 10}, "is_dex": {"type": "uint32", "id": 11}, "is_extra": {"type": "uint32", "id": 12}, "reward": {"type": "string", "id": 13}, "points": {"type": "uint32", "id": 15}, "is_zimo": {"type": "uint32", "id": 16}}}}}, "ResFetchRPGBattleHistoryV2": {"fields": {"error": {"type": "Error", "id": 1}, "battle_result": {"rule": "repeated", "type": "BattleResultV2", "id": 2}, "start_state": {"type": "lq.RPGState", "id": 3}, "current_state": {"type": "lq.RPGState", "id": 4}, "recent_battle_result": {"rule": "repeated", "type": "BattleResultV2", "id": 5}}, "nested": {"BattleResultV2": {"fields": {"uuid": {"type": "string", "id": 14}, "chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "damage": {"type": "uint32", "id": 5}, "monster_seq": {"type": "uint32", "id": 7}, "killed": {"type": "uint32", "id": 9}, "buff": {"rule": "repeated", "type": "lq.ActivityBuffData", "id": 10}, "points": {"type": "uint32", "id": 11}}}}}, "ReqBuyArenaTicket": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ReqArenaReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ReqEnterArena": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResArenaReward": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "RewardItem", "id": 2}}, "nested": {"RewardItem": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqReceiveRPGRewards": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ReqReceiveRPGReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "monster_seq": {"type": "uint32", "id": 2}}}, "ResReceiveRPGRewards": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "RewardItem", "id": 2}}, "nested": {"RewardItem": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqFetchOBToken": {"fields": {"uuid": {"type": "string", "id": 1}}}, "ResFetchOBToken": {"fields": {"error": {"type": "Error", "id": 1}, "token": {"type": "string", "id": 2}, "create_time": {"type": "uint32", "id": 3}, "delay": {"type": "uint32", "id": 4}, "start_time": {"type": "uint32", "id": 5}}}, "ReqReceiveCharacterRewards": {"fields": {"character_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}}}, "ResReceiveCharacterRewards": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "RewardItem", "id": 2}}, "nested": {"RewardItem": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqFeedActivityFeed": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResFeedActivityFeed": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "RewardItem", "id": 2}, "feed_count": {"type": "uint32", "id": 3}}, "nested": {"RewardItem": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqSendActivityGiftToFriend": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "item_id": {"type": "uint32", "id": 2}, "target_id": {"type": "uint32", "id": 3}}}, "ResSendActivityGiftToFriend": {"fields": {"error": {"type": "Error", "id": 1}, "send_gift_count": {"type": "uint32", "id": 2}}}, "ReqReceiveActivityGift": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "ReqFetchFriendGiftActivityData": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "account_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResFetchFriendGiftActivityData": {"fields": {"error": {"type": "Error", "id": 1}, "list": {"rule": "repeated", "type": "FriendData", "id": 2}}, "nested": {"ItemCountData": {"fields": {"item": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "FriendData": {"fields": {"account_id": {"type": "uint32", "id": 1}, "items": {"rule": "repeated", "type": "ItemCountData", "id": 2}, "receive_count": {"type": "uint32", "id": 3}}}}}, "ReqOpenPreChestItem": {"fields": {"item_id": {"type": "uint32", "id": 1}, "pool_id": {"type": "uint32", "id": 2}}}, "ResOpenPreChestItem": {"fields": {"error": {"type": "Error", "id": 1}, "results": {"rule": "repeated", "type": "OpenResult", "id": 2}}}, "ReqFetchVoteActivity": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResFetchVoteActivity": {"fields": {"error": {"type": "Error", "id": 1}, "vote_rank": {"rule": "repeated", "type": "uint32", "id": 2}, "update_time": {"type": "uint32", "id": 3}}}, "ReqVoteActivity": {"fields": {"vote": {"type": "uint32", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ResVoteActivity": {"fields": {"error": {"type": "Error", "id": 1}, "vote_records": {"rule": "repeated", "type": "VoteData", "id": 2}}}, "ReqUnlockActivitySpot": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ReqUnlockActivitySpotEnding": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "ending_id": {"type": "uint32", "id": 2}}}, "ReqReceiveActivitySpotReward": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResReceiveActivitySpotReward": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "RewardItem", "id": 2}}, "nested": {"RewardItem": {"fields": {"id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqLogReport": {"fields": {"success": {"type": "uint32", "id": 1}, "failed": {"type": "uint32", "id": 2}}}, "ReqBindOauth2": {"fields": {"type": {"type": "uint32", "id": 1}, "token": {"type": "string", "id": 2}}}, "ReqFetchOauth2": {"fields": {"type": {"type": "uint32", "id": 1}}}, "ResFetchOauth2": {"fields": {"error": {"type": "Error", "id": 1}, "openid": {"type": "string", "id": 2}}}, "ResDeleteAccount": {"fields": {"error": {"type": "Error", "id": 1}, "delete_time": {"type": "uint32", "id": 2}}}, "ReqSetLoadingImage": {"fields": {"images": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ResFetchShopInterval": {"fields": {"error": {"type": "Error", "id": 1}, "result": {"rule": "repeated", "type": "ShopInterval", "id": 2}}, "nested": {"ShopInterval": {"fields": {"group_id": {"type": "uint32", "id": 1}, "interval": {"type": "uint32", "id": 2}}}}}, "ResFetchActivityInterval": {"fields": {"error": {"type": "Error", "id": 1}, "result": {"rule": "repeated", "type": "ActivityInterval", "id": 2}}, "nested": {"ActivityInterval": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "interval": {"type": "uint32", "id": 2}}}}}, "ResFetchrecentFriend": {"fields": {"error": {"type": "Error", "id": 1}, "account_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ReqOpenGacha": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "ResOpenGacha": {"fields": {"error": {"type": "Error", "id": 1}, "result_list": {"rule": "repeated", "type": "uint32", "id": 2}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 3}, "sp_reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 4}, "remain_count": {"type": "uint32", "id": 5}}}, "ReqTaskRequest": {"fields": {"params": {"rule": "repeated", "type": "uint32", "id": 1}}}, "ReqSimulationActivityTrain": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "ResSimulationActivityTrain": {"fields": {"error": {"type": "Error", "id": 1}, "result_type": {"type": "uint32", "id": 2}, "final_stats": {"rule": "repeated", "type": "uint32", "id": 4}}}, "ReqFetchSimulationGameRecord": {"fields": {"game_uuid": {"type": "string", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ResFetchSimulationGameRecord": {"fields": {"error": {"type": "Error", "id": 1}, "messages": {"rule": "repeated", "type": "ActivitySimulationGameRecordMessage", "id": 2}}}, "ReqStartSimulationActivityGame": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResStartSimulationActivityGame": {"fields": {"error": {"type": "Error", "id": 1}, "records": {"rule": "repeated", "type": "lq.ActivitySimulationGameRecord", "id": 2}}}, "ReqFetchSimulationGameRank": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "day": {"type": "uint32", "id": 2}}}, "ResFetchSimulationGameRank": {"fields": {"error": {"type": "Error", "id": 1}, "rank": {"rule": "repeated", "type": "RankInfo", "id": 2}}, "nested": {"RankInfo": {"fields": {"character": {"type": "uint32", "id": 1}, "score": {"type": "float", "id": 2}}}}}, "ReqGenerateCombiningCraft": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "bin_id": {"type": "uint32", "id": 2}}}, "ResGenerateCombiningCraft": {"fields": {"error": {"type": "Error", "id": 1}, "pos": {"type": "uint32", "id": 2}, "craft_id": {"type": "uint32", "id": 3}}}, "ReqMoveCombiningCraft": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "from": {"type": "uint32", "id": 2}, "to": {"type": "uint32", "id": 3}}}, "ResMoveCombiningCraft": {"fields": {"error": {"type": "Error", "id": 1}, "pos": {"type": "uint32", "id": 2}, "combined": {"type": "uint32", "id": 3}, "craft_id": {"type": "uint32", "id": 4}, "bonus": {"type": "BonusData", "id": 5}}, "nested": {"BonusData": {"fields": {"craft_id": {"type": "uint32", "id": 1}, "pos": {"type": "uint32", "id": 2}}}}}, "ReqCombiningRecycleCraft": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "pos": {"type": "uint32", "id": 2}}}, "ResCombiningRecycleCraft": {"fields": {"error": {"type": "Error", "id": 1}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqRecoverCombiningRecycle": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResRecoverCombiningRecycle": {"fields": {"error": {"type": "Error", "id": 1}, "craft_id": {"type": "uint32", "id": 2}, "pos": {"type": "uint32", "id": 3}}}, "ReqFinishCombiningOrder": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "craft_pos": {"type": "uint32", "id": 2}, "order_pos": {"type": "uint32", "id": 3}}}, "ResFinishCombiningOrder": {"fields": {"error": {"type": "Error", "id": 1}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ResFetchInfo": {"fields": {"error": {"type": "Error", "id": 1}, "server_time": {"type": "ResServerTime", "id": 2}, "server_setting": {"type": "ResServerSettings", "id": 3}, "client_value": {"type": "ResClientValue", "id": 4}, "friend_list": {"type": "ResFriendList", "id": 5}, "friend_apply_list": {"type": "ResFriendApplyList", "id": 6}, "recent_friend": {"type": "ResFetchrecentFriend", "id": 7}, "mail_info": {"type": "ResMailInfo", "id": 8}, "receive_coin_info": {"type": "ResReviveCoinInfo", "id": 9}, "title_list": {"type": "ResTitleList", "id": 10}, "bag_info": {"type": "ResBagInfo", "id": 11}, "shop_info": {"type": "ResShopInfo", "id": 12}, "shop_interval": {"type": "ResFetchShopInterval", "id": 13}, "activity_data": {"type": "ResAccountActivityData", "id": 14}, "activity_interval": {"type": "ResFetchActivityInterval", "id": 15}, "activity_buff": {"type": "ResActivityBuff", "id": 16}, "vip_reward": {"type": "ResVipReward", "id": 17}, "month_ticket_info": {"type": "ResMonthTicketInfo", "id": 18}, "achievement": {"type": "ResAchievement", "id": 19}, "comment_setting": {"type": "ResCommentSetting", "id": 20}, "account_settings": {"type": "ResAccountSettings", "id": 21}, "mod_nickname_time": {"type": "ResModNicknameTime", "id": 22}, "misc": {"type": "ResMisc", "id": 23}, "announcement": {"type": "ResAnnouncement", "id": 24}, "activity_list": {"type": "ResActivityList", "id": 26}, "character_info": {"type": "ResCharacterInfo", "id": 27}, "all_common_views": {"type": "ResAllcommonViews", "id": 28}, "collected_game_record_list": {"type": "ResCollectedGameRecordList", "id": 29}, "maintain_notice": {"type": "ResFetchMaintainNotice", "id": 30}, "random_character": {"type": "ResRandomCharacter", "id": 31}, "maintenance_info": {"type": "ResFetchServerMaintenanceInfo", "id": 32}, "seer_info": {"type": "ResFetchSeerInfo", "id": 33}, "annual_report_info": {"type": "ResFetchAnnualReportInfo", "id": 34}}}, "ResFetchSeerInfo": {"fields": {"error": {"type": "Error", "id": 1}, "remain_count": {"type": "uint32", "id": 2}, "date_limit": {"type": "uint32", "id": 3}, "expire_time": {"type": "uint32", "id": 4}}}, "ResFetchServerMaintenanceInfo": {"fields": {"function_maintenance": {"rule": "repeated", "type": "ServerFunctionMaintenanceInfo", "id": 1}}, "nested": {"ServerFunctionMaintenanceInfo": {"fields": {"name": {"type": "string", "id": 1}, "open": {"type": "bool", "id": 2}}}}}, "ReqUpgradeVillageBuilding": {"fields": {"building_id": {"type": "uint32", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ReqReceiveVillageBuildingReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "building_id": {"type": "uint32", "id": 2}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 3}}}, "ResReceiveVillageBuildingReward": {"fields": {"error": {"type": "Error", "id": 1}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqStartVillageTrip": {"fields": {"dest": {"type": "uint32", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ReqReceiveVillageTripReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "dest_id": {"type": "uint32", "id": 2}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 3}}}, "ResReceiveVillageTripReward": {"fields": {"error": {"type": "Error", "id": 1}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqCompleteVillageTask": {"fields": {"task_id": {"type": "uint32", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ResCompleteVillageTask": {"fields": {"error": {"type": "Error", "id": 1}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqGetFriendVillageData": {"fields": {"account_list": {"rule": "repeated", "type": "uint32", "id": 1}, "activity_id": {"type": "uint32", "id": 2}}}, "ResGetFriendVillageData": {"fields": {"error": {"type": "Error", "id": 1}, "list": {"rule": "repeated", "type": "FriendVillageData", "id": 2}}, "nested": {"FriendVillageData": {"fields": {"account_id": {"type": "uint32", "id": 1}, "level": {"type": "uint32", "id": 2}}}}}, "ReqSetVillageWorker": {"fields": {"building_id": {"type": "uint32", "id": 1}, "worker_pos": {"type": "uint32", "id": 2}, "activity_id": {"type": "uint32", "id": 3}}}, "ResSetVillageWorker": {"fields": {"error": {"type": "Error", "id": 1}, "building": {"type": "VillageBuildingData", "id": 2}, "update_time": {"type": "uint32", "id": 3}}}, "ReqNextRoundVillage": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResNextRoundVillage": {"fields": {"error": {"type": "Error", "id": 1}, "activity_data": {"type": "ActivityVillageData", "id": 2}}}, "ReqResolveFestivalActivityProposal": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}, "select": {"type": "uint32", "id": 3}}}, "ResResolveFestivalActivityProposal": {"fields": {"error": {"type": "Error", "id": 1}, "effected_buff": {"rule": "repeated", "type": "uint32", "id": 2}, "result": {"type": "uint32", "id": 3}, "reward_items": {"rule": "repeated", "type": "ExecuteResult", "id": 4}, "level": {"type": "uint32", "id": 5}}}, "ReqResolveFestivalActivityEvent": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}, "select": {"type": "uint32", "id": 3}}}, "ResResolveFestivalActivityEvent": {"fields": {"error": {"type": "Error", "id": 1}, "effected_buff": {"rule": "repeated", "type": "uint32", "id": 2}, "reward_items": {"rule": "repeated", "type": "ExecuteResult", "id": 4}, "ending_id": {"type": "uint32", "id": 5}, "level": {"type": "uint32", "id": 6}}}, "ReqBuyFestivalProposal": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResBuyFestivalProposal": {"fields": {"error": {"type": "Error", "id": 1}, "new_proposal": {"type": "FestivalProposalData", "id": 2}}}, "ReqIslandActivityMove": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "zone_id": {"type": "uint32", "id": 2}}}, "ReqIslandActivityBuy": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "items": {"rule": "repeated", "type": "BuyItems", "id": 2}}, "nested": {"BuyItems": {"fields": {"goods_id": {"type": "uint32", "id": 2}, "pos": {"rule": "repeated", "type": "uint32", "id": 3}, "rotate": {"type": "uint32", "id": 4}, "bag_id": {"type": "uint32", "id": 5}, "price": {"type": "uint32", "id": 6}}}}}, "ReqIslandActivitySell": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "items": {"rule": "repeated", "type": "SellItem", "id": 2}}, "nested": {"SellItem": {"fields": {"bag_id": {"type": "uint32", "id": 2}, "id": {"type": "uint32", "id": 3}, "price": {"type": "uint32", "id": 4}}}}}, "ReqIslandActivityTidyBag": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "bag_data": {"rule": "repeated", "type": "BagData", "id": 2}}, "nested": {"BagData": {"fields": {"bag_id": {"type": "uint32", "id": 2}, "items": {"rule": "repeated", "type": "ITemData", "id": 3}, "drops": {"rule": "repeated", "type": "uint32", "id": 4}}, "nested": {"ITemData": {"fields": {"id": {"type": "uint32", "id": 1}, "pos": {"rule": "repeated", "type": "uint32", "id": 2}, "rotate": {"type": "uint32", "id": 3}}}}}}}, "ReqIslandActivityUnlockBagGrid": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "bag_id": {"type": "uint32", "id": 2}, "pos": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ContestSetting": {"fields": {"level_limit": {"rule": "repeated", "type": "LevelLimit", "id": 1}, "game_limit": {"type": "uint32", "id": 2}, "system_broadcast": {"type": "uint32", "id": 3}}, "nested": {"LevelLimit": {"fields": {"type": {"type": "uint32", "id": 1}, "value": {"type": "uint32", "id": 2}}}}}, "ReqCreateCustomizedContest": {"fields": {"name": {"type": "string", "id": 1}, "open_show": {"type": "uint32", "id": 2}, "game_rule_setting": {"type": "GameMode", "id": 3}, "start_time": {"type": "uint32", "id": 4}, "end_time": {"type": "uint32", "id": 5}, "auto_match": {"type": "uint32", "id": 6}, "rank_rule": {"type": "uint32", "id": 7}, "contest_setting": {"type": "ContestSetting", "id": 8}}}, "ResCreateCustomizedContest": {"fields": {"error": {"type": "Error", "id": 1}, "unique_id": {"type": "uint32", "id": 2}}}, "ReqFetchmanagerCustomizedContestList": {"fields": {"lang": {"type": "string", "id": 1}}}, "ResFetchManagerCustomizedContestList": {"fields": {"error": {"type": "Error", "id": 1}, "contests": {"rule": "repeated", "type": "CustomizedContestBase", "id": 2}}}, "ReqFetchManagerCustomizedContest": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResFetchManagerCustomizedContest": {"fields": {"error": {"type": "Error", "id": 1}, "name": {"type": "string", "id": 2}, "open_show": {"type": "uint32", "id": 3}, "game_rule_setting": {"type": "GameMode", "id": 4}, "start_time": {"type": "uint32", "id": 5}, "end_time": {"type": "uint32", "id": 6}, "auto_match": {"type": "uint32", "id": 7}, "rank_rule": {"type": "uint32", "id": 8}, "check_state": {"type": "uint32", "id": 9}, "checking_name": {"type": "string", "id": 10}, "contest_setting": {"type": "ContestSetting", "id": 11}}}, "ReqUpdateManagerCustomizedContest": {"fields": {"name": {"type": "string", "id": 1}, "open_show": {"type": "uint32", "id": 2}, "game_rule_setting": {"type": "GameMode", "id": 3}, "start_time": {"type": "uint32", "id": 4}, "end_time": {"type": "uint32", "id": 5}, "unique_id": {"type": "uint32", "id": 6}, "auto_match": {"type": "uint32", "id": 7}, "rank_rule": {"type": "uint32", "id": 8}, "contest_setting": {"type": "ContestSetting", "id": 9}}}, "ReqFetchContestPlayerRank": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "limit": {"type": "uint32", "id": 2}, "offset": {"type": "uint32", "id": 3}}}, "ResFetchContestPlayerRank": {"fields": {"error": {"type": "Error", "id": 1}, "total": {"type": "uint32", "id": 2}, "rank": {"rule": "repeated", "type": "SeasonRank", "id": 3}, "player_data": {"type": "PlayerData", "id": 4}}, "nested": {"ContestPlayerAccountData": {"fields": {"total_game_count": {"type": "uint32", "id": 1}, "recent_games": {"rule": "repeated", "type": "ContestGameResult", "id": 2}, "highest_series_points": {"rule": "repeated", "type": "ContestSeriesGameResult", "id": 3}}, "nested": {"ContestGameResult": {"fields": {"rank": {"type": "uint32", "id": 1}, "total_point": {"type": "int32", "id": 2}}}, "ContestSeriesGameResult": {"fields": {"key": {"type": "uint32", "id": 1}, "results": {"rule": "repeated", "type": "ContestGameResult", "id": 2}}}}}, "SeasonRank": {"fields": {"account_id": {"type": "uint32", "id": 1}, "nickname": {"type": "string", "id": 2}, "data": {"type": "ContestPlayerAccountData", "id": 3}}}, "PlayerData": {"fields": {"rank": {"type": "uint32", "id": 1}, "data": {"type": "ContestPlayerAccountData", "id": 2}}}}}, "ReqFetchReadyPlayerList": {"fields": {"unique_id": {"type": "uint32", "id": 1}}}, "ResFetchReadyPlayerList": {"fields": {"error": {"type": "Error", "id": 1}, "list": {"rule": "repeated", "type": "Player", "id": 2}}, "nested": {"Player": {"fields": {"account_id": {"type": "uint32", "id": 1}, "nickname": {"type": "string", "id": 2}}}}}, "ReqCreateGamePlan": {"fields": {"unique_id": {"type": "uint32", "id": 1}, "account_list": {"rule": "repeated", "type": "uint32", "id": 2}, "game_start_time": {"type": "uint32", "id": 3}, "shuffle_seats": {"type": "uint32", "id": 4}, "ai_level": {"type": "uint32", "id": 5}}}, "ResGenerateContestManagerLoginCode": {"fields": {"error": {"type": "Error", "id": 1}, "code": {"type": "string", "id": 2}}}, "ReqAmuletActivityFetchInfo": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResAmuletActivityFetchInfo": {"fields": {"error": {"type": "Error", "id": 1}, "data": {"type": "ActivityAmuletData", "id": 2}}}, "ReqAmuletActivityFetchBrief": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResAmuletActivityFetchBrief": {"fields": {"error": {"type": "Error", "id": 1}, "upgrade": {"type": "ActivityAmuletUpgradeData", "id": 4}, "illustrated_book": {"type": "ActivityAmuletIllustratedBookData", "id": 5}, "task": {"type": "ActivityAmuletTaskData", "id": 6}}}, "ReqAmuletActivityStartGame": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResAmuletActivityStartGame": {"fields": {"error": {"type": "Error", "id": 1}, "game": {"type": "AmuletGameData", "id": 2}}}, "ReqAmuletActivityOperate": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "tile": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ResAmuletActivityOperate": {"fields": {"error": {"type": "Error", "id": 1}, "hu_result": {"type": "AmuletHuleOperateResult", "id": 2}, "gang_result": {"type": "AmuletGangOperateResult", "id": 3}, "deal_result": {"type": "AmuletDealTileResult", "id": 4}, "upgrade_result": {"type": "AmuletUpgradeResult", "id": 5}, "upgraded": {"type": "bool", "id": 6}, "failed": {"type": "bool", "id": 7}, "game_update": {"type": "AmuletGameUpdateData", "id": 8}, "discard_result": {"type": "AmuletDiscardTileResult", "id": 9}, "start_result": {"type": "AmuletStartGameResult", "id": 10}}}, "ReqAmuletActivityChangeHands": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "hands": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResAmuletActivityChangeHands": {"fields": {"error": {"type": "Error", "id": 1}, "hands": {"rule": "repeated", "type": "uint32", "id": 2}, "remain_change_tile_count": {"type": "uint32", "id": 3}, "ting_list": {"rule": "repeated", "type": "AmuletActivityTingInfo", "id": 4}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 5}}}, "ReqAmuletActivityUpgrade": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResAmuletActivityUpgrade": {"fields": {"error": {"type": "Error", "id": 1}, "game": {"type": "AmuletGameData", "id": 2}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 3}}}, "ReqAmuletActivitySelectPack": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "ResAmuletActivitySelectPack": {"fields": {"error": {"type": "Error", "id": 1}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 2}, "shop": {"type": "AmuletGameShopData", "id": 3}}}, "ReqAmuletActivityBuy": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 3}}}, "ResAmuletActivityBuy": {"fields": {"error": {"type": "Error", "id": 1}, "coin": {"type": "uint32", "id": 2}, "shop": {"type": "AmuletGameShopData", "id": 3}, "stage": {"type": "uint32", "id": 4}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 5}, "total_consumed_coin": {"type": "uint32", "id": 6}}}, "ReqAmuletActivitySellEffect": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "ResAmuletActivitySellEffect": {"fields": {"error": {"type": "Error", "id": 1}, "coin": {"type": "uint32", "id": 2}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 3}, "game_update": {"type": "AmuletGameUpdateData", "id": 4}, "remain_change_tile_count": {"type": "uint32", "id": 5}, "hook_effect": {"rule": "repeated", "type": "AmuletActivityHookEffect", "id": 6}, "shop": {"type": "AmuletGameShopData", "id": 7}}}, "ReqAmuletActivityEffectSort": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "sorted_id": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ReqAmuletActivityGiveup": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ReqAmuletActivityRefreshShop": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResAmuletActivityRefreshShop": {"fields": {"error": {"type": "Error", "id": 1}, "shop": {"type": "AmuletGameShopData", "id": 2}, "coin": {"type": "uint32", "id": 3}, "effect_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 4}}}, "ReqAmuletActivitySelectFreeEffect": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "selected_id": {"type": "uint32", "id": 2}}}, "ResAmuletActivitySelectFreeEffect": {"fields": {"error": {"type": "Error", "id": 1}, "game_update": {"type": "AmuletGameUpdateData", "id": 3}, "remain_change_tile_count": {"type": "uint32", "id": 4}, "locked_tile": {"rule": "repeated", "type": "uint32", "id": 5}, "locked_tile_count": {"type": "uint32", "id": 6}}}, "ReqAmuletActivityUpgradeShopBuff": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "ResAmuletActivityUpgradeShopBuff": {"fields": {"error": {"type": "Error", "id": 1}, "game_update": {"type": "AmuletGameUpdateData", "id": 3}, "shop_buff_list": {"rule": "repeated", "type": "AmuletEffectData", "id": 4}, "total_consumed_coin": {"type": "uint32", "id": 5}}}, "ReqAmuletActivityEndShopping": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResAmuletActivityEndShopping": {"fields": {"error": {"type": "Error", "id": 1}, "game_update": {"type": "AmuletGameUpdateData", "id": 3}}}, "ReqAmuletActivitySetSkillLevel": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "skill": {"rule": "repeated", "type": "AmuletSkillData", "id": 2}}}, "ResAmuletActivityMaintainInfo": {"fields": {"error": {"type": "Error", "id": 1}, "mode": {"type": "string", "id": 2}}}, "ReqAmuletActivitySelectRewardPack": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "id": {"type": "uint32", "id": 2}}}, "ResAmuletActivitySelectRewardPack": {"fields": {"error": {"type": "Error", "id": 1}, "game_update": {"type": "AmuletGameUpdateData", "id": 2}, "shop": {"type": "AmuletGameShopData", "id": 3}}}, "ReqAmuletActivityReceiveTaskReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "task_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResAmuletActivityReceiveTaskReward": {"fields": {"error": {"type": "Error", "id": 1}, "task": {"type": "ActivityAmuletTaskData", "id": 2}}}, "ReqStoryActivityUnlock": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}}}, "ReqStoryActivityUnlockEnding": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}, "ending_id": {"type": "uint32", "id": 3}}}, "ReqStoryActivityReceiveEndingReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}, "ending_id": {"type": "uint32", "id": 3}}}, "ResStoryReward": {"fields": {"error": {"type": "Error", "id": 1}, "reward_items": {"rule": "repeated", "type": "ExecuteReward", "id": 2}}}, "ReqStoryActivityReceiveFinishReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}}}, "ReqStoryActivityReceiveAllFinishReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}}}, "ReqStoryActivityUnlockEndingAndReceive": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "story_id": {"type": "uint32", "id": 2}, "ending_id": {"type": "uint32", "id": 3}}}, "ResStoryActivityUnlockEndingAndReceive": {"fields": {"error": {"type": "Error", "id": 1}, "ending_reward": {"rule": "repeated", "type": "ExecuteReward", "id": 2}, "finish_reward": {"rule": "repeated", "type": "ExecuteReward", "id": 3}, "all_finish_reward": {"rule": "repeated", "type": "ExecuteReward", "id": 4}}}, "ReqFetchActivityRank": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "account_list": {"rule": "repeated", "type": "uint32", "id": 2}}}, "ResFetchActivityRank": {"fields": {"error": {"type": "Error", "id": 1}, "items": {"rule": "repeated", "type": "ActivityRankItem", "id": 4}, "self": {"type": "ActivityRankItem", "id": 5}}, "nested": {"ActivityRankItem": {"fields": {"account_id": {"type": "uint32", "id": 1}, "score": {"type": "uint64", "id": 2}, "data": {"type": "string", "id": 3}, "rank": {"type": "uint32", "id": 4}}}}}, "ReqFetchQuestionnaireList": {"fields": {"lang": {"type": "string", "id": 1}, "channel": {"type": "string", "id": 2}}}, "ResFetchQuestionnaireList": {"fields": {"error": {"type": "Error", "id": 1}, "list": {"rule": "repeated", "type": "QuestionnaireBrief", "id": 2}, "finished_list": {"rule": "repeated", "type": "uint32", "id": 3}}}, "ReqFetchQuestionnaireDetail": {"fields": {"id": {"type": "uint32", "id": 1}, "lang": {"type": "string", "id": 2}, "channel": {"type": "string", "id": 3}}}, "ResFetchQuestionnaireDetail": {"fields": {"error": {"type": "Error", "id": 1}, "detail": {"type": "QuestionnaireDetail", "id": 2}}}, "ReqSetVerifiedHidden": {"fields": {"verified_hidden": {"type": "uint32", "id": 1}}}, "ReqSubmitQuestionnaire": {"fields": {"questionnaire_id": {"type": "uint32", "id": 1}, "questionnaire_version_id": {"type": "uint32", "id": 2}, "answers": {"rule": "repeated", "type": "QuestionnaireAnswer", "id": 3}, "open_time": {"type": "uint32", "id": 4}, "finish_time": {"type": "uint32", "id": 5}, "client": {"type": "string", "id": 6}}, "nested": {"QuestionnaireAnswer": {"fields": {"question_id": {"type": "uint32", "id": 1}, "values": {"rule": "repeated", "type": "QuestionnaireAnswerValue", "id": 2}}, "nested": {"QuestionnaireAnswerValue": {"fields": {"value": {"type": "string", "id": 1}, "custom_input": {"type": "string", "id": 2}}}}}}}, "ReqSetFriendRoomRandomBotChar": {"fields": {"disable_random_char": {"type": "uint32", "id": 1}}}, "ReqFetchAccountGameHuRecords": {"fields": {"uuid": {"type": "string", "id": 1}, "category": {"type": "uint32", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "ResFetchAccountGameHuRecords": {"fields": {"error": {"type": "Error", "id": 1}, "records": {"rule": "repeated", "type": "GameHuRecords", "id": 2}}, "nested": {"GameHuRecords": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "title_id": {"type": "uint32", "id": 4}, "hands": {"rule": "repeated", "type": "string", "id": 5}, "ming": {"rule": "repeated", "type": "string", "id": 6}, "hupai": {"type": "string", "id": 7}, "hu_fans": {"rule": "repeated", "type": "uint32", "id": 8}}}}}, "ReqFetchAccountInfoExtra": {"fields": {"account_id": {"type": "uint32", "id": 1}, "category": {"type": "uint32", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "ResFetchAccountInfoExtra": {"fields": {"error": {"type": "Error", "id": 1}, "recent_games": {"rule": "repeated", "type": "AccountInfoGameRecord", "id": 2}, "hu_type_details": {"rule": "repeated", "type": "GameHuTypeDetail", "id": 3}, "game_rank_details": {"rule": "repeated", "type": "AccountGameRankDetail", "id": 4}}, "nested": {"AccountInfoGameRecord": {"fields": {"uuid": {"type": "string", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "end_time": {"type": "uint32", "id": 3}, "tag": {"type": "uint32", "id": 4}, "sub_tag": {"type": "uint32", "id": 5}, "rank": {"type": "uint32", "id": 6}, "final_point": {"type": "uint32", "id": 7}, "results": {"rule": "repeated", "type": "AccountGameResult", "id": 8}}, "nested": {"AccountGameResult": {"fields": {"rank": {"type": "uint32", "id": 1}, "account_id": {"type": "uint32", "id": 2}, "nickname": {"type": "string", "id": 3}, "verified": {"type": "uint32", "id": 4}, "grading_score": {"type": "int32", "id": 5}, "final_point": {"type": "int32", "id": 6}, "seat": {"type": "uint32", "id": 7}, "level": {"type": "AccountLevel", "id": 8}, "level3": {"type": "AccountLevel", "id": 9}}}}}, "GameHuTypeDetail": {"fields": {"type": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}, "AccountGameRankDetail": {"fields": {"rank": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}}}}}, "ReqSetAccountFavoriteHu": {"fields": {"mode": {"type": "uint32", "id": 1}, "category": {"type": "uint32", "id": 2}, "type": {"type": "uint32", "id": 3}, "uuid": {"type": "string", "id": 4}, "chang": {"type": "uint32", "id": 5}, "ju": {"type": "uint32", "id": 6}, "ben": {"type": "uint32", "id": 7}}}, "ReqFetchSeerReport": {"fields": {"uuid": {"type": "string", "id": 1}}}, "ResFetchSeerReport": {"fields": {"error": {"type": "Error", "id": 1}, "report": {"type": "<PERSON><PERSON><PERSON>ep<PERSON>", "id": 2}}}, "ReqCreateSeerReport": {"fields": {"uuid": {"type": "string", "id": 1}}}, "ResCreateSeerReport": {"fields": {"error": {"type": "Error", "id": 1}, "seer_report": {"type": "<PERSON><PERSON><PERSON><PERSON>", "id": 2}}}, "ResFetchSeerReportList": {"fields": {"error": {"type": "Error", "id": 1}, "seer_report_list": {"rule": "repeated", "type": "<PERSON><PERSON><PERSON><PERSON>", "id": 2}}}, "ReqSelectChestChooseUp": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "selection": {"type": "uint32", "id": 2}, "chest_id": {"type": "uint32", "id": 3}}}, "ReqGenerateAnnualReportToken": {"fields": {"lang": {"type": "string", "id": 1}}}, "ResGenerateAnnualReportToken": {"fields": {"error": {"type": "Error", "id": 1}, "token": {"type": "string", "id": 2}, "url": {"type": "string", "id": 3}}}, "ResFetchAnnualReportInfo": {"fields": {"error": {"type": "Error", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "end_time": {"type": "uint32", "id": 3}}}, "ReqRemarkFriend": {"fields": {"account_id": {"type": "uint32", "id": 1}, "remark": {"type": "string", "id": 2}}}, "ReqSimV2ActivityFetchInfo": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResSimV2ActivityFetchInfo": {"fields": {"error": {"type": "Error", "id": 1}, "data": {"type": "SimulationV2Data", "id": 2}}}, "ReqSimV2ActivityStartSeason": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResSimV2ActivityStartSeason": {"fields": {"error": {"type": "Error", "id": 1}, "season": {"type": "SimulationV2SeasonData", "id": 2}}}, "ReqSimV2ActivityTrain": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "ability": {"type": "uint32", "id": 2}, "skip": {"type": "uint32", "id": 3}}}, "ResSimV2ActivityTrain": {"fields": {"error": {"type": "Error", "id": 1}, "event": {"type": "SimulationV2Event", "id": 2}, "ability": {"type": "SimulationV2Ability", "id": 3}, "round": {"type": "uint32", "id": 4}, "effect_list": {"rule": "repeated", "type": "SimulationV2Effect", "id": 5}, "train_result": {"type": "uint32", "id": 6}, "is_end": {"type": "bool", "id": 7}, "record": {"type": "SimulationV2Record", "id": 8}}}, "ReqSimV2ActivitySelectEvent": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "selection_id": {"type": "uint32", "id": 2}}}, "ResSimV2ActivitySelectEvent": {"fields": {"error": {"type": "Error", "id": 1}, "event": {"type": "SimulationV2Event", "id": 2}, "ability": {"type": "SimulationV2Ability", "id": 3}, "match": {"type": "SimulationV2Match", "id": 4}, "effect_list": {"rule": "repeated", "type": "SimulationV2Effect", "id": 5}, "round": {"type": "uint32", "id": 7}, "is_end": {"type": "bool", "id": 8}, "result_id": {"type": "uint32", "id": 9}, "record": {"type": "SimulationV2Record", "id": 10}, "effected_buff_list": {"rule": "repeated", "type": "uint32", "id": 11}}}, "ReqSimV2ActivityStartMatch": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResSimV2ActivityStartMatch": {"fields": {"error": {"type": "Error", "id": 1}, "event": {"type": "SimulationV2Event", "id": 2}, "match": {"type": "SimulationV2Match", "id": 4}, "effect_list": {"rule": "repeated", "type": "SimulationV2Effect", "id": 5}, "is_match_end": {"type": "bool", "id": 6}}}, "ReqSimV2ActivityEndMatch": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ResSimV2ActivityEndMatch": {"fields": {"error": {"type": "Error", "id": 1}, "round": {"type": "uint32", "id": 2}, "is_end": {"type": "bool", "id": 3}, "record": {"type": "SimulationV2Record", "id": 4}, "total_score": {"type": "int32", "id": 5}, "match_history": {"rule": "repeated", "type": "SimulationV2MatchRecord", "id": 6}, "rewards": {"rule": "repeated", "type": "SimulationV2MatchReward", "id": 7}, "effect_list": {"rule": "repeated", "type": "SimulationV2Effect", "id": 8}, "ability": {"type": "SimulationV2Ability", "id": 9}}, "nested": {"SimulationV2MatchReward": {"fields": {"type": {"type": "uint32", "id": 1}, "params": {"rule": "repeated", "type": "uint32", "id": 2}}}}}, "ReqSimV2ActivityGiveUp": {"fields": {"activity_id": {"type": "uint32", "id": 1}}}, "ReqSimV2ActivitySetUpgrade": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "upgrade": {"type": "SimulationV2Ability", "id": 2}}}, "FastTest": {"methods": {"authGame": {"requestType": "ReqAuthGame", "responseType": "ResAuthGame"}, "enterGame": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResEnterGame"}, "syncGame": {"requestType": "ReqSyncGame", "responseType": "ResSyncGame"}, "finishSyncGame": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "terminateGame": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "inputOperation": {"requestType": "ReqSelfOperation", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "inputChiPengGang": {"requestType": "ReqChiPengGang", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "confirmNewRound": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "broadcastInGame": {"requestType": "ReqBroadcastInGame", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "inputGameGMCommand": {"requestType": "ReqGMCommandInGaming", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "fetchGamePlayerState": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResGamePlayerState"}, "checkNetworkDelay": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "clearLeaving": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "voteGameEnd": {"requestType": "ReqVoteGameEnd", "responseType": "ResGameEndVote"}, "authObserve": {"requestType": "ReqAuthObserve", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}, "startObserve": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "ResStartObserve"}, "stopObserve": {"requestType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "responseType": "<PERSON>s<PERSON><PERSON><PERSON>"}}}, "ReqAuthGame": {"fields": {"account_id": {"type": "uint32", "id": 1}, "token": {"type": "string", "id": 2}, "game_uuid": {"type": "string", "id": 3}, "session": {"type": "string", "id": 4}, "gift": {"type": "string", "id": 5}, "vs": {"type": "uint32", "id": 6}}}, "ResAuthGame": {"fields": {"error": {"type": "Error", "id": 1}, "players": {"rule": "repeated", "type": "PlayerGameView", "id": 2}, "seat_list": {"rule": "repeated", "type": "uint32", "id": 3}, "is_game_start": {"type": "bool", "id": 4}, "game_config": {"type": "GameConfig", "id": 5}, "ready_id_list": {"rule": "repeated", "type": "uint32", "id": 6}, "robots": {"rule": "repeated", "type": "PlayerGameView", "id": 7}}}, "GameRestore": {"fields": {"snapshot": {"type": "GameSnapshot", "id": 1}, "actions": {"rule": "repeated", "type": "ActionPrototype", "id": 2}, "passed_waiting_time": {"type": "uint32", "id": 3}, "game_state": {"type": "uint32", "id": 4}, "start_time": {"type": "uint32", "id": 5}, "last_pause_time_ms": {"type": "uint32", "id": 6}}}, "ResEnterGame": {"fields": {"error": {"type": "Error", "id": 1}, "is_end": {"type": "bool", "id": 2}, "step": {"type": "uint32", "id": 3}, "game_restore": {"type": "GameRestore", "id": 4}}}, "ReqSyncGame": {"fields": {"round_id": {"type": "string", "id": 1}, "step": {"type": "uint32", "id": 2}}}, "ResSyncGame": {"fields": {"error": {"type": "Error", "id": 1}, "is_end": {"type": "bool", "id": 2}, "step": {"type": "uint32", "id": 3}, "game_restore": {"type": "GameRestore", "id": 4}}}, "ReqSelfOperation": {"fields": {"type": {"type": "uint32", "id": 1}, "index": {"type": "uint32", "id": 2}, "tile": {"type": "string", "id": 3}, "cancel_operation": {"type": "bool", "id": 4}, "moqie": {"type": "bool", "id": 5}, "timeuse": {"type": "uint32", "id": 6}, "tile_state": {"type": "int32", "id": 7}, "change_tiles": {"rule": "repeated", "type": "string", "id": 8}, "tile_states": {"rule": "repeated", "type": "int32", "id": 9}, "gap_type": {"type": "uint32", "id": 10}}}, "ReqChiPengGang": {"fields": {"type": {"type": "uint32", "id": 1}, "index": {"type": "uint32", "id": 2}, "cancel_operation": {"type": "bool", "id": 3}, "timeuse": {"type": "uint32", "id": 6}}}, "ReqBroadcastInGame": {"fields": {"content": {"type": "string", "id": 1}, "except_self": {"type": "bool", "id": 2}}}, "ReqGMCommandInGaming": {"fields": {"json_data": {"type": "string", "id": 1}}}, "ResGamePlayerState": {"fields": {"error": {"type": "Error", "id": 1}, "state_list": {"rule": "repeated", "type": "GamePlayerState", "id": 2}}}, "ReqVoteGameEnd": {"fields": {"yes": {"type": "bool", "id": 1}}}, "ResGameEndVote": {"fields": {"success": {"type": "bool", "id": 1}, "vote_cd_end_time": {"type": "uint32", "id": 2}, "error": {"type": "Error", "id": 3}}}, "ReqAuthObserve": {"fields": {"token": {"type": "string", "id": 1}}}, "ResStartObserve": {"fields": {"head": {"type": "GameLiveHead", "id": 1}, "passed": {"type": "GameLiveSegment", "id": 2}}}, "NotifyNewGame": {"fields": {"game_uuid": {"type": "string", "id": 1}, "player_list": {"rule": "repeated", "type": "string", "id": 2}}}, "NotifyPlayerLoadGameReady": {"fields": {"ready_id_list": {"rule": "repeated", "type": "uint32", "id": 1}}}, "NotifyGameBroadcast": {"fields": {"seat": {"type": "uint32", "id": 1}, "content": {"type": "string", "id": 2}}}, "NotifyGameEndResult": {"fields": {"result": {"type": "GameEndResult", "id": 1}}}, "NotifyGameTerminate": {"fields": {"reason": {"type": "string", "id": 1}}}, "NotifyPlayerConnectionState": {"fields": {"seat": {"type": "uint32", "id": 1}, "state": {"type": "GamePlayerState", "id": 2}}}, "NotifyAccountLevelChange": {"fields": {"origin": {"type": "AccountLevel", "id": 1}, "final": {"type": "AccountLevel", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "NotifyGameFinishReward": {"fields": {"mode_id": {"type": "uint32", "id": 1}, "level_change": {"type": "LevelChange", "id": 2}, "match_chest": {"type": "MatchChest", "id": 3}, "main_character": {"type": "MainCharacter", "id": 4}, "character_gift": {"type": "CharacterGift", "id": 5}, "badges": {"rule": "repeated", "type": "BadgeAchieveProgress", "id": 6}}, "nested": {"LevelChange": {"fields": {"origin": {"type": "AccountLevel", "id": 1}, "final": {"type": "AccountLevel", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "MatchChest": {"fields": {"chest_id": {"type": "uint32", "id": 1}, "origin": {"type": "uint32", "id": 2}, "final": {"type": "uint32", "id": 3}, "is_graded": {"type": "bool", "id": 4}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 5}}}, "MainCharacter": {"fields": {"level": {"type": "uint32", "id": 1}, "exp": {"type": "uint32", "id": 2}, "add": {"type": "uint32", "id": 3}}}, "CharacterGift": {"fields": {"origin": {"type": "uint32", "id": 1}, "final": {"type": "uint32", "id": 2}, "add": {"type": "uint32", "id": 3}, "is_graded": {"type": "bool", "id": 4}}}}}, "NotifyActivityReward": {"fields": {"activity_reward": {"rule": "repeated", "type": "ActivityReward", "id": 1}}, "nested": {"ActivityReward": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "rewards": {"rule": "repeated", "type": "RewardSlot", "id": 2}}}}}, "NotifyActivityPoint": {"fields": {"activity_points": {"rule": "repeated", "type": "ActivityPoint", "id": 1}}, "nested": {"ActivityPoint": {"fields": {"activity_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "NotifyLeaderboardPoint": {"fields": {"leaderboard_points": {"rule": "repeated", "type": "LeaderboardPoint", "id": 1}}, "nested": {"LeaderboardPoint": {"fields": {"leaderboard_id": {"type": "uint32", "id": 1}, "point": {"type": "uint32", "id": 2}}}}}, "NotifyGamePause": {"fields": {"paused": {"type": "bool", "id": 1}}}, "NotifyEndGameVote": {"fields": {"results": {"rule": "repeated", "type": "VoteResult", "id": 1}, "start_time": {"type": "uint32", "id": 2}, "duration_time": {"type": "uint32", "id": 3}}, "nested": {"VoteResult": {"fields": {"account_id": {"type": "uint32", "id": 1}, "yes": {"type": "bool", "id": 2}}}}}, "NotifyObserveData": {"fields": {"unit": {"type": "GameLiveUnit", "id": 1}}}, "ActionMJStart": {"fields": {}}, "NewRoundOpenedTiles": {"fields": {"seat": {"type": "uint32", "id": 1}, "tiles": {"rule": "repeated", "type": "string", "id": 2}, "count": {"rule": "repeated", "type": "uint32", "id": 3}}}, "MuyuInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "count": {"type": "uint32", "id": 2}, "count_max": {"type": "uint32", "id": 3}, "id": {"type": "uint32", "id": 4}}}, "ChuanmaGang": {"fields": {"old_scores": {"rule": "repeated", "type": "int32", "id": 1}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 2}, "scores": {"rule": "repeated", "type": "int32", "id": 3}, "gameend": {"type": "GameEnd", "id": 4}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 5}}}, "YongchangInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "moqie_count": {"type": "uint32", "id": 2}, "moqie_bonus": {"type": "uint32", "id": 3}, "shouqie_count": {"type": "uint32", "id": 4}, "shouqie_bonus": {"type": "uint32", "id": 5}}}, "ActionNewCard": {"fields": {"field_spell": {"type": "uint32", "id": 1}}}, "RecordNewCard": {"fields": {"field_spell": {"type": "uint32", "id": 1}}}, "ActionNewRound": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "tiles": {"rule": "repeated", "type": "string", "id": 4}, "dora": {"type": "string", "id": 5}, "scores": {"rule": "repeated", "type": "int32", "id": 6}, "operation": {"type": "OptionalOperationList", "id": 7}, "liqibang": {"type": "uint32", "id": 8}, "tingpais0": {"rule": "repeated", "type": "TingPaiDiscardInfo", "id": 9}, "tingpais1": {"rule": "repeated", "type": "TingPaiInfo", "id": 10}, "al": {"type": "bool", "id": 11}, "md5": {"type": "string", "id": 12}, "left_tile_count": {"type": "uint32", "id": 13}, "doras": {"rule": "repeated", "type": "string", "id": 14}, "opens": {"rule": "repeated", "type": "NewRoundOpenedTiles", "id": 15}, "muyu": {"type": "MuyuInfo", "id": 16}, "ju_count": {"type": "uint32", "id": 17}, "field_spell": {"type": "uint32", "id": 18}, "sha256": {"type": "string", "id": 19}, "yongchang": {"type": "YongchangInfo", "id": 20}, "saltSha256": {"type": "string", "id": 21}}}, "RecordNewRound": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "dora": {"type": "string", "id": 4}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "liqibang": {"type": "uint32", "id": 6}, "tiles0": {"rule": "repeated", "type": "string", "id": 7}, "tiles1": {"rule": "repeated", "type": "string", "id": 8}, "tiles2": {"rule": "repeated", "type": "string", "id": 9}, "tiles3": {"rule": "repeated", "type": "string", "id": 10}, "tingpai": {"rule": "repeated", "type": "TingPai", "id": 11}, "operation": {"type": "OptionalOperationList", "id": 12}, "md5": {"type": "string", "id": 13}, "paishan": {"type": "string", "id": 14}, "left_tile_count": {"type": "uint32", "id": 15}, "doras": {"rule": "repeated", "type": "string", "id": 16}, "opens": {"rule": "repeated", "type": "NewRoundOpenedTiles", "id": 17}, "muyu": {"type": "MuyuInfo", "id": 18}, "operations": {"rule": "repeated", "type": "OptionalOperationList", "id": 19}, "ju_count": {"type": "uint32", "id": 20}, "field_spell": {"type": "uint32", "id": 21}, "sha256": {"type": "string", "id": 22}, "yongchang": {"type": "YongchangInfo", "id": 23}, "saltSha256": {"type": "string", "id": 24}, "salt": {"type": "string", "id": 25}}, "nested": {"TingPai": {"fields": {"seat": {"type": "uint32", "id": 1}, "tingpais1": {"rule": "repeated", "type": "TingPaiInfo", "id": 2}}}}}, "GameSnapshot": {"fields": {"chang": {"type": "uint32", "id": 1}, "ju": {"type": "uint32", "id": 2}, "ben": {"type": "uint32", "id": 3}, "index_player": {"type": "uint32", "id": 4}, "left_tile_count": {"type": "uint32", "id": 5}, "hands": {"rule": "repeated", "type": "string", "id": 6}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "liqibang": {"type": "uint32", "id": 8}, "players": {"rule": "repeated", "type": "PlayerSnapshot", "id": 9}, "zhenting": {"type": "bool", "id": 10}}, "nested": {"PlayerSnapshot": {"fields": {"score": {"type": "int32", "id": 1}, "liqiposition": {"type": "int32", "id": 2}, "tilenum": {"type": "uint32", "id": 3}, "qipais": {"rule": "repeated", "type": "string", "id": 4}, "mings": {"rule": "repeated", "type": "<PERSON><PERSON>", "id": 5}}, "nested": {"Fulu": {"fields": {"type": {"type": "uint32", "id": 1}, "tile": {"rule": "repeated", "type": "string", "id": 2}, "from": {"rule": "repeated", "type": "uint32", "id": 3}}}}}}}, "ActionPrototype": {"fields": {"step": {"type": "uint32", "id": 1}, "name": {"type": "string", "id": 2}, "data": {"type": "bytes", "id": 3}}}, "GameDetailRecords": {"fields": {"records": {"rule": "repeated", "type": "bytes", "id": 1}, "version": {"type": "uint32", "id": 2}, "actions": {"rule": "repeated", "type": "GameAction", "id": 3}, "bar": {"type": "bytes", "id": 4}}}, "GameSelfOperation": {"fields": {"type": {"type": "uint32", "id": 1}, "index": {"type": "uint32", "id": 2}, "tile": {"type": "string", "id": 3}, "cancel_operation": {"type": "bool", "id": 4}, "moqie": {"type": "bool", "id": 5}, "timeuse": {"type": "uint32", "id": 6}, "tile_state": {"type": "int32", "id": 7}, "change_tiles": {"rule": "repeated", "type": "string", "id": 8}, "tile_states": {"rule": "repeated", "type": "int32", "id": 9}, "gap_type": {"type": "uint32", "id": 10}}}, "GameChiPengGang": {"fields": {"type": {"type": "uint32", "id": 1}, "index": {"type": "uint32", "id": 2}, "cancel_operation": {"type": "bool", "id": 3}, "timeuse": {"type": "uint32", "id": 6}}}, "GameVoteGameEnd": {"fields": {"yes": {"type": "bool", "id": 1}}}, "GameUserInput": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "emo": {"type": "uint32", "id": 3}, "operation": {"type": "GameSelfOperation", "id": 10}, "cpg": {"type": "GameChiPengGang", "id": 11}, "vote": {"type": "GameVoteGameEnd", "id": 12}}}, "GameUserEvent": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}}}, "GameAction": {"fields": {"passed": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "result": {"type": "bytes", "id": 3}, "user_input": {"type": "GameUserInput", "id": 4}, "user_event": {"type": "GameUserEvent", "id": 5}, "game_event": {"type": "uint32", "id": 6}}}, "OptionalOperation": {"fields": {"type": {"type": "uint32", "id": 1}, "combination": {"rule": "repeated", "type": "string", "id": 2}, "change_tiles": {"rule": "repeated", "type": "string", "id": 3}, "change_tile_states": {"rule": "repeated", "type": "int32", "id": 4}, "gap_type": {"type": "uint32", "id": 5}}}, "OptionalOperationList": {"fields": {"seat": {"type": "uint32", "id": 1}, "operation_list": {"rule": "repeated", "type": "OptionalOperation", "id": 2}, "time_add": {"type": "uint32", "id": 4}, "time_fixed": {"type": "uint32", "id": 5}}}, "LiQiSuccess": {"fields": {"seat": {"type": "uint32", "id": 1}, "score": {"type": "int32", "id": 2}, "liqibang": {"type": "uint32", "id": 3}, "failed": {"type": "bool", "id": 4}, "liqi_type_beishuizhizhan": {"type": "uint32", "id": 5}}}, "FanInfo": {"fields": {"name": {"type": "string", "id": 1}, "val": {"type": "uint32", "id": 2}, "id": {"type": "uint32", "id": 3}}}, "HuleInfo": {"fields": {"hand": {"rule": "repeated", "type": "string", "id": 1}, "ming": {"rule": "repeated", "type": "string", "id": 2}, "hu_tile": {"type": "string", "id": 3}, "seat": {"type": "uint32", "id": 4}, "zimo": {"type": "bool", "id": 5}, "qinjia": {"type": "bool", "id": 6}, "liqi": {"type": "bool", "id": 7}, "doras": {"rule": "repeated", "type": "string", "id": 8}, "li_doras": {"rule": "repeated", "type": "string", "id": 9}, "yiman": {"type": "bool", "id": 10}, "count": {"type": "uint32", "id": 11}, "fans": {"rule": "repeated", "type": "FanInfo", "id": 12}, "fu": {"type": "uint32", "id": 13}, "title": {"type": "string", "id": 14}, "point_rong": {"type": "uint32", "id": 15}, "point_zimo_qin": {"type": "uint32", "id": 16}, "point_zimo_xian": {"type": "uint32", "id": 17}, "title_id": {"type": "uint32", "id": 18}, "point_sum": {"type": "uint32", "id": 19}, "dadian": {"type": "uint32", "id": 20}, "baopai": {"type": "uint32", "id": 21}, "baopai_seats": {"rule": "repeated", "type": "uint32", "id": 22}, "lines": {"rule": "repeated", "type": "string", "id": 23}, "tianming_bonus": {"type": "uint32", "id": 24}, "baida_changed": {"rule": "repeated", "type": "string", "id": 25}, "hu_tile_baiDa_changed": {"type": "string", "id": 26}}}, "TingPaiInfo": {"fields": {"tile": {"type": "string", "id": 1}, "haveyi": {"type": "bool", "id": 2}, "yiman": {"type": "bool", "id": 3}, "count": {"type": "uint32", "id": 4}, "fu": {"type": "uint32", "id": 5}, "biao_dora_count": {"type": "uint32", "id": 6}, "yiman_zimo": {"type": "bool", "id": 7}, "count_zimo": {"type": "uint32", "id": 8}, "fu_zimo": {"type": "uint32", "id": 9}}}, "TingPaiDiscardInfo": {"fields": {"tile": {"type": "string", "id": 1}, "zhenting": {"type": "bool", "id": 2}, "infos": {"rule": "repeated", "type": "TingPaiInfo", "id": 3}}}, "HunZhiYiJiBuffInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "continue_deal_count": {"type": "uint32", "id": 2}, "overload": {"type": "bool", "id": 3}}}, "GameEnd": {"fields": {"scores": {"rule": "repeated", "type": "int32", "id": 1}}}, "ActionSelectGap": {"fields": {"gap_types": {"rule": "repeated", "type": "uint32", "id": 1}, "tingpais0": {"rule": "repeated", "type": "TingPaiDiscardInfo", "id": 2}, "tingpais1": {"rule": "repeated", "type": "TingPaiInfo", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}}}, "RecordSelectGap": {"fields": {"gap_types": {"rule": "repeated", "type": "uint32", "id": 1}, "tingpai": {"rule": "repeated", "type": "TingPai", "id": 2}, "operation": {"type": "OptionalOperationList", "id": 3}}, "nested": {"TingPai": {"fields": {"seat": {"type": "uint32", "id": 1}, "tingpais1": {"rule": "repeated", "type": "TingPaiInfo", "id": 2}}}}}, "ActionChangeTile": {"fields": {"in_tiles": {"rule": "repeated", "type": "string", "id": 1}, "in_tile_states": {"rule": "repeated", "type": "int32", "id": 2}, "out_tiles": {"rule": "repeated", "type": "string", "id": 3}, "out_tile_states": {"rule": "repeated", "type": "int32", "id": 4}, "doras": {"rule": "repeated", "type": "string", "id": 5}, "tingpais0": {"rule": "repeated", "type": "TingPaiDiscardInfo", "id": 6}, "tingpais1": {"rule": "repeated", "type": "TingPaiInfo", "id": 7}, "operation": {"type": "OptionalOperationList", "id": 8}, "change_type": {"type": "uint32", "id": 9}}}, "RecordChangeTile": {"fields": {"doras": {"rule": "repeated", "type": "string", "id": 1}, "tingpai": {"rule": "repeated", "type": "TingPai", "id": 2}, "change_tile_infos": {"rule": "repeated", "type": "ChangeTile", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}, "change_type": {"type": "uint32", "id": 5}, "operations": {"rule": "repeated", "type": "OptionalOperationList", "id": 6}}, "nested": {"TingPai": {"fields": {"seat": {"type": "uint32", "id": 1}, "tingpais1": {"rule": "repeated", "type": "TingPaiInfo", "id": 2}}}, "ChangeTile": {"fields": {"in_tiles": {"rule": "repeated", "type": "string", "id": 1}, "in_tile_states": {"rule": "repeated", "type": "int32", "id": 2}, "out_tiles": {"rule": "repeated", "type": "string", "id": 3}, "out_tile_states": {"rule": "repeated", "type": "int32", "id": 4}}}}}, "ActionRevealTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "is_liqi": {"type": "bool", "id": 2}, "is_wliqi": {"type": "bool", "id": 3}, "moqie": {"type": "bool", "id": 4}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "liqibang": {"type": "uint32", "id": 6}, "operation": {"type": "OptionalOperationList", "id": 7}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 8}, "tile": {"type": "string", "id": 9}, "zhenting": {"type": "bool", "id": 10}}}, "RecordRevealTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "is_liqi": {"type": "bool", "id": 2}, "is_wliqi": {"type": "bool", "id": 3}, "moqie": {"type": "bool", "id": 4}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "liqibang": {"type": "uint32", "id": 6}, "operations": {"rule": "repeated", "type": "OptionalOperationList", "id": 7}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 8}, "tile": {"type": "string", "id": 9}, "zhenting": {"rule": "repeated", "type": "bool", "id": 10}}}, "ActionUnveilTile": {"fields": {"seat": {"type": "int32", "id": 1}, "scores": {"rule": "repeated", "type": "int32", "id": 2}, "liqibang": {"type": "uint32", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}}}, "RecordUnveilTile": {"fields": {"seat": {"type": "int32", "id": 1}, "scores": {"rule": "repeated", "type": "int32", "id": 2}, "liqibang": {"type": "uint32", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}}}, "ActionLockTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "scores": {"rule": "repeated", "type": "int32", "id": 2}, "liqibang": {"type": "uint32", "id": 3}, "tile": {"type": "string", "id": 4}, "operation": {"type": "OptionalOperationList", "id": 5}, "zhenting": {"type": "bool", "id": 6}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 7}, "doras": {"rule": "repeated", "type": "string", "id": 8}, "lock_state": {"type": "int32", "id": 9}}}, "RecordLockTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "scores": {"rule": "repeated", "type": "int32", "id": 2}, "liqibang": {"type": "uint32", "id": 3}, "tile": {"type": "string", "id": 4}, "operation": {"rule": "repeated", "type": "OptionalOperationList", "id": 5}, "zhentings": {"rule": "repeated", "type": "bool", "id": 6}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 7}, "doras": {"rule": "repeated", "type": "string", "id": 8}, "lock_state": {"type": "int32", "id": 9}}}, "ActionDiscardTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}, "is_liqi": {"type": "bool", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}, "moqie": {"type": "bool", "id": 5}, "zhenting": {"type": "bool", "id": 6}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 7}, "doras": {"rule": "repeated", "type": "string", "id": 8}, "is_wliqi": {"type": "bool", "id": 9}, "tile_state": {"type": "uint32", "id": 10}, "muyu": {"type": "MuyuInfo", "id": 11}, "revealed": {"type": "bool", "id": 12}, "scores": {"rule": "repeated", "type": "int32", "id": 13}, "liqibang": {"type": "uint32", "id": 14}, "yongchang": {"type": "YongchangInfo", "id": 25}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 26}, "liqi_type_beishuizhizhan": {"type": "uint32", "id": 27}}}, "RecordDiscardTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}, "is_liqi": {"type": "bool", "id": 3}, "moqie": {"type": "bool", "id": 5}, "zhenting": {"rule": "repeated", "type": "bool", "id": 6}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 7}, "doras": {"rule": "repeated", "type": "string", "id": 8}, "is_wliqi": {"type": "bool", "id": 9}, "operations": {"rule": "repeated", "type": "OptionalOperationList", "id": 10}, "tile_state": {"type": "uint32", "id": 11}, "muyu": {"type": "MuyuInfo", "id": 12}, "yongchang": {"type": "YongchangInfo", "id": 13}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 14}, "liqi_type_beishuizhizhan": {"type": "uint32", "id": 27}}}, "ActionDealTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}, "left_tile_count": {"type": "uint32", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}, "liqi": {"type": "LiQiSuccess", "id": 5}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "zhenting": {"type": "bool", "id": 7}, "tingpais": {"rule": "repeated", "type": "TingPaiDiscardInfo", "id": 8}, "tile_state": {"type": "uint32", "id": 9}, "muyu": {"type": "MuyuInfo", "id": 10}, "tile_index": {"type": "uint32", "id": 11}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 12}}}, "RecordDealTile": {"fields": {"seat": {"type": "uint32", "id": 1}, "tile": {"type": "string", "id": 2}, "left_tile_count": {"type": "uint32", "id": 3}, "liqi": {"type": "LiQiSuccess", "id": 5}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "zhenting": {"rule": "repeated", "type": "bool", "id": 7}, "operation": {"type": "OptionalOperationList", "id": 8}, "tile_state": {"type": "uint32", "id": 9}, "muyu": {"type": "MuyuInfo", "id": 11}, "tile_index": {"type": "uint32", "id": 12}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 13}}}, "ActionFillAwaitingTiles": {"fields": {"awaiting_tiles": {"rule": "repeated", "type": "string", "id": 1}, "left_tile_count": {"type": "uint32", "id": 2}, "operation": {"type": "OptionalOperationList", "id": 3}, "liqi": {"type": "LiQiSuccess", "id": 4}}}, "RecordFillAwaitingTiles": {"fields": {"awaiting_tiles": {"rule": "repeated", "type": "string", "id": 1}, "left_tile_count": {"type": "uint32", "id": 2}, "operation": {"type": "OptionalOperationList", "id": 3}, "liqi": {"type": "LiQiSuccess", "id": 4}}}, "ActionChiPengGang": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "tiles": {"rule": "repeated", "type": "string", "id": 3}, "froms": {"rule": "repeated", "type": "uint32", "id": 4}, "liqi": {"type": "LiQiSuccess", "id": 5}, "operation": {"type": "OptionalOperationList", "id": 6}, "zhenting": {"type": "bool", "id": 7}, "tingpais": {"rule": "repeated", "type": "TingPaiDiscardInfo", "id": 8}, "tile_states": {"rule": "repeated", "type": "uint32", "id": 9}, "muyu": {"type": "MuyuInfo", "id": 10}, "scores": {"rule": "repeated", "type": "int32", "id": 11}, "liqibang": {"type": "uint32", "id": 12}, "yongchang": {"type": "YongchangInfo", "id": 13}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 14}}}, "RecordChiPengGang": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "tiles": {"rule": "repeated", "type": "string", "id": 3}, "froms": {"rule": "repeated", "type": "uint32", "id": 4}, "liqi": {"type": "LiQiSuccess", "id": 5}, "zhenting": {"rule": "repeated", "type": "bool", "id": 7}, "operation": {"type": "OptionalOperationList", "id": 8}, "tile_states": {"rule": "repeated", "type": "uint32", "id": 9}, "muyu": {"type": "MuyuInfo", "id": 10}, "scores": {"rule": "repeated", "type": "int32", "id": 11}, "liqibang": {"type": "uint32", "id": 12}, "yongchang": {"type": "YongchangInfo", "id": 13}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 14}}}, "ActionGangResult": {"fields": {"gang_infos": {"type": "Chuanma<PERSON>", "id": 1}}}, "RecordGangResult": {"fields": {"gang_infos": {"type": "Chuanma<PERSON>", "id": 1}}}, "ActionGangResultEnd": {"fields": {"gang_infos": {"type": "Chuanma<PERSON>", "id": 1}}}, "RecordGangResultEnd": {"fields": {"gang_infos": {"type": "Chuanma<PERSON>", "id": 1}}}, "ActionAnGangAddGang": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "tiles": {"type": "string", "id": 3}, "operation": {"type": "OptionalOperationList", "id": 4}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "zhenting": {"type": "bool", "id": 7}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 8}, "muyu": {"type": "MuyuInfo", "id": 9}}}, "RecordAnGangAddGang": {"fields": {"seat": {"type": "uint32", "id": 1}, "type": {"type": "uint32", "id": 2}, "tiles": {"type": "string", "id": 3}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "operations": {"rule": "repeated", "type": "OptionalOperationList", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}}}, "ActionBaBei": {"fields": {"seat": {"type": "uint32", "id": 1}, "operation": {"type": "OptionalOperationList", "id": 4}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "zhenting": {"type": "bool", "id": 7}, "tingpais": {"rule": "repeated", "type": "TingPaiInfo", "id": 8}, "moqie": {"type": "bool", "id": 9}, "tile_state": {"type": "uint32", "id": 10}, "muyu": {"type": "MuyuInfo", "id": 11}}}, "RecordBaBei": {"fields": {"seat": {"type": "uint32", "id": 1}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "operations": {"rule": "repeated", "type": "OptionalOperationList", "id": 7}, "moqie": {"type": "bool", "id": 8}, "tile_state": {"type": "uint32", "id": 10}, "muyu": {"type": "MuyuInfo", "id": 11}}}, "ActionHule": {"fields": {"hules": {"rule": "repeated", "type": "HuleInfo", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "wait_timeout": {"type": "uint32", "id": 4}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "gameend": {"type": "GameEnd", "id": 6}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}, "baopai": {"type": "int32", "id": 9}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 10}}}, "RecordHule": {"fields": {"hules": {"rule": "repeated", "type": "HuleInfo", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "wait_timeout": {"type": "uint32", "id": 4}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "gameend": {"type": "GameEnd", "id": 6}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}, "baopai": {"type": "int32", "id": 9}, "hun_zhi_yi_ji_info": {"type": "HunZhiYiJiBuffInfo", "id": 10}}}, "HuInfoXueZhanMid": {"fields": {"seat": {"type": "uint32", "id": 1}, "hand_count": {"type": "uint32", "id": 2}, "hand": {"rule": "repeated", "type": "string", "id": 3}, "ming": {"rule": "repeated", "type": "string", "id": 4}, "hu_tile": {"type": "string", "id": 5}, "zimo": {"type": "bool", "id": 6}, "yiman": {"type": "bool", "id": 7}, "count": {"type": "uint32", "id": 8}, "fans": {"rule": "repeated", "type": "FanInfo", "id": 9}, "fu": {"type": "uint32", "id": 10}, "title_id": {"type": "uint32", "id": 11}}}, "ActionHuleXueZhanMid": {"fields": {"hules": {"rule": "repeated", "type": "HuInfoXueZhanMid", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}, "liqi": {"type": "LiQiSuccess", "id": 9}, "zhenting": {"type": "bool", "id": 10}}}, "RecordHuleXueZhanMid": {"fields": {"hules": {"rule": "repeated", "type": "HuInfoXueZhanMid", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "scores": {"rule": "repeated", "type": "int32", "id": 5}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}, "liqi": {"type": "LiQiSuccess", "id": 9}, "zhenting": {"rule": "repeated", "type": "bool", "id": 10}}}, "ActionHuleXueZhanEnd": {"fields": {"hules": {"rule": "repeated", "type": "HuInfoXueZhanMid", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "scores": {"rule": "repeated", "type": "int32", "id": 4}, "wait_timeout": {"type": "uint32", "id": 5}, "gameend": {"type": "GameEnd", "id": 6}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 9}}}, "RecordHuleXueZhanEnd": {"fields": {"hules": {"rule": "repeated", "type": "HuInfoXueZhanMid", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "scores": {"rule": "repeated", "type": "int32", "id": 4}, "wait_timeout": {"type": "uint32", "id": 5}, "gameend": {"type": "GameEnd", "id": 6}, "doras": {"rule": "repeated", "type": "string", "id": 7}, "muyu": {"type": "MuyuInfo", "id": 8}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 9}}}, "ActionLiuJu": {"fields": {"type": {"type": "uint32", "id": 1}, "gameend": {"type": "GameEnd", "id": 2}, "seat": {"type": "uint32", "id": 3}, "tiles": {"rule": "repeated", "type": "string", "id": 4}, "liqi": {"type": "LiQiSuccess", "id": 5}, "allplayertiles": {"rule": "repeated", "type": "string", "id": 6}, "muyu": {"type": "MuyuInfo", "id": 7}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 9}}}, "RecordLiuJu": {"fields": {"type": {"type": "uint32", "id": 1}, "gameend": {"type": "GameEnd", "id": 2}, "seat": {"type": "uint32", "id": 3}, "tiles": {"rule": "repeated", "type": "string", "id": 4}, "liqi": {"type": "LiQiSuccess", "id": 5}, "allplayertiles": {"rule": "repeated", "type": "string", "id": 6}, "muyu": {"type": "MuyuInfo", "id": 7}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 9}}}, "NoTilePlayerInfo": {"fields": {"tingpai": {"type": "bool", "id": 3}, "hand": {"rule": "repeated", "type": "string", "id": 4}, "tings": {"rule": "repeated", "type": "TingPaiInfo", "id": 5}, "already_hule": {"type": "bool", "id": 6}}}, "NoTileScoreInfo": {"fields": {"seat": {"type": "uint32", "id": 1}, "old_scores": {"rule": "repeated", "type": "int32", "id": 2}, "delta_scores": {"rule": "repeated", "type": "int32", "id": 3}, "hand": {"rule": "repeated", "type": "string", "id": 4}, "ming": {"rule": "repeated", "type": "string", "id": 5}, "doras": {"rule": "repeated", "type": "string", "id": 6}, "score": {"type": "uint32", "id": 7}, "taxes": {"rule": "repeated", "type": "int32", "id": 8}, "lines": {"rule": "repeated", "type": "string", "id": 9}}}, "ActionNoTile": {"fields": {"liujumanguan": {"type": "bool", "id": 1}, "players": {"rule": "repeated", "type": "NoTilePlayerInfo", "id": 2}, "scores": {"rule": "repeated", "type": "NoTileScoreInfo", "id": 3}, "gameend": {"type": "bool", "id": 4}, "muyu": {"type": "MuyuInfo", "id": 5}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 9}}}, "RecordNoTile": {"fields": {"liujumanguan": {"type": "bool", "id": 1}, "players": {"rule": "repeated", "type": "NoTilePlayerInfo", "id": 2}, "scores": {"rule": "repeated", "type": "NoTileScoreInfo", "id": 3}, "gameend": {"type": "bool", "id": 4}, "muyu": {"type": "MuyuInfo", "id": 5}, "hules_history": {"rule": "repeated", "type": "HuleInfo", "id": 9}}}, "PlayerLeaving": {"fields": {"seat": {"type": "uint32", "id": 1}}}, "Route": {"methods": {"requestConnection": {"requestType": "ReqRequestConnection", "responseType": "ResRequestConnection"}, "requestRouteChange": {"requestType": "ReqRequestRouteChange", "responseType": "ResRequestRouteChange"}, "heartbeat": {"requestType": "ReqHeartbeat", "responseType": "ResHeartbeat"}}}, "ReqRequestConnection": {"fields": {"type": {"type": "uint32", "id": 2}, "route_id": {"type": "string", "id": 3}, "timestamp": {"type": "uint64", "id": 4}}}, "ResRequestConnection": {"fields": {"error": {"type": "Error", "id": 1}, "timestamp": {"type": "uint64", "id": 2}, "result": {"type": "uint32", "id": 3}}}, "ReqRequestRouteChange": {"fields": {"before": {"type": "string", "id": 1}, "route_id": {"type": "string", "id": 2}, "type": {"type": "uint32", "id": 3}}}, "ResRequestRouteChange": {"fields": {"error": {"type": "Error", "id": 1}, "result": {"type": "uint32", "id": 3}}}, "ReqHeartbeat": {"fields": {"delay": {"type": "uint32", "id": 1}, "no_operation_counter": {"type": "uint32", "id": 2}, "platform": {"type": "uint32", "id": 3}, "network_quality": {"type": "uint32", "id": 4}}}, "ResHeartbeat": {"fields": {"error": {"type": "Error", "id": 1}}}}}}}
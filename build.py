#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雀魂MAX独立版构建脚本
设计者：Nfilmjon (小约)
基于原作者Avenshy的思路进行独立设计和优化
解决批处理文件编码问题的Python版本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("\n" + "="*60)
    print("           雀魂MAX独立版构建工具")
    print("           设计者：Nfilmjon (小约)")
    print("="*60)
    print("基于原作者Avenshy的思路进行独立设计和优化")
    print("此工具将把雀魂MAX独立版编译成独立的exe文件")
    print("编译后可在没有Python环境的机器上运行")
    print()
    print("📋 可用构建选项:")
    print("1. 命令行版本 (main.py)")
    print("2. GUI图形界面版本 (gui.py)")
    print()

def check_python():
    """检查Python版本"""
    print("[检查] Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print("❌ 错误: 需要Python 3.10或更高版本")
        print("请从 https://www.python.org/downloads/ 下载安装")
        return False
    
    print("✅ Python版本检查通过")
    return True

def get_build_choice():
    """获取用户构建选择"""
    while True:
        try:
            choice = input("请选择构建版本 (1=命令行版, 2=GUI版): ").strip()
            if choice in ['1', '2']:
                return int(choice)
            else:
                print("❌ 请输入 1 或 2")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return None

def check_project_files(build_choice):
    """检查项目文件"""
    print("\n[检查] 项目文件...")

    # 基础文件
    required_files = ['addons.py']
    required_dirs = ['config', 'proto', 'plugin']

    # 根据构建选择添加特定文件
    if build_choice == 1:
        required_files.append('main.py')
        print("构建目标: 命令行版本")
    elif build_choice == 2:
        required_files.extend(['gui.py', 'favicon.ico', '1.png'])
        print("构建目标: GUI图形界面版本")

    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    for dir in required_dirs:
        if not os.path.exists(dir):
            missing_files.append(dir)

    if missing_files:
        print(f"❌ 错误: 缺少必要文件/目录: {', '.join(missing_files)}")
        print("请确保在项目根目录运行此脚本")
        if build_choice == 2:
            print("GUI版本需要: gui.py, favicon.ico (图标), 1.png (背景图)")
        return False

    print("✅ 项目文件检查通过")
    return True

def install_dependencies():
    """安装依赖"""
    print("\n[步骤1/4] 安装依赖...")
    
    # 优先使用修复后的requirements文件
    req_file = "requirements_fixed.txt" if os.path.exists("requirements_fixed.txt") else "requirements.txt"
    print(f"使用依赖文件: {req_file}")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", req_file
        ], stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        print("请检查网络连接或手动运行: pip install -r requirements.txt")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("\n[步骤2/4] 安装PyInstaller...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "pyinstaller"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT)
        print("✅ PyInstaller安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def clean_build():
    """清理构建目录"""
    print("\n[步骤3/4] 清理旧的构建文件...")

    try:
        dirs_to_clean = ["build", "dist"]

        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"已删除目录: {dir_name}")

        # 删除spec文件
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
            print(f"已删除文件: {spec_file}")

        print("✅ 清理完成")
        return True
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def build_exe(build_choice):
    """构建exe文件"""
    print("\n[步骤4/4] 构建exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")

    # 基础命令参数
    base_cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--add-data=config;config",
        "--add-data=proto;proto",
        "--add-data=plugin;plugin",
        "--hidden-import=mitmproxy.tools.dump",
        "--hidden-import=mitmproxy.options",
        "--hidden-import=mitmproxy.http",
        "--hidden-import=mitmproxy.ctx",
        "--hidden-import=liqi_new",
        "--hidden-import=plugin.helper",
        "--hidden-import=plugin.mod",
        "--hidden-import=plugin.update_liqi",
        "--hidden-import=proto.liqi_pb2",
        "--hidden-import=proto.config_pb2",
        "--hidden-import=proto.sheets_pb2",
        "--hidden-import=proto.basic_pb2",
        "--hidden-import=ruamel.yaml",
        "--hidden-import=loguru",
        "--hidden-import=google.protobuf.json_format",
    ]

    # 根据构建选择配置特定参数
    if build_choice == 1:
        # 命令行版本
        cmd = base_cmd + [
            "--console",
            "--name=MajsoulMax",
            "main.py"
        ]
    elif build_choice == 2:
        # GUI版本
        cmd = base_cmd + [
            "--console",  # 显示控制台窗口用于调试
            "--name=MajsoulMax-GUI",
            "--icon=favicon.ico",  # 设置图标
            "--add-data=favicon.ico;.",  # 包含图标文件
            "--add-data=1.png;.",  # 包含背景图
            # GUI相关的隐藏导入
            "--hidden-import=customtkinter",
            "--hidden-import=tkinter",
            "--hidden-import=PIL",
            "--hidden-import=PIL.Image",
            "--hidden-import=PIL.ImageTk",
            "--hidden-import=threading",
            "--hidden-import=queue",
            "--hidden-import=subprocess",
            "--hidden-import=psutil",
            "gui.py"
        ]
    
    try:
        # 显示构建进度
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 universal_newlines=True, encoding='utf-8')
        
        for line in process.stdout:
            if "INFO:" in line or "WARNING:" in line or "ERROR:" in line:
                print(f"  {line.strip()}")
        
        process.wait()
        
        if process.returncode == 0:
            print("✅ 构建完成")
            return True
        else:
            print("❌ 构建失败")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def show_results(build_choice):
    """显示构建结果"""
    if build_choice == 1:
        exe_path = Path("dist/MajsoulMax.exe")
        exe_name = "MajsoulMax.exe"
        version_type = "命令行版本"
    else:
        exe_path = Path("dist/MajsoulMax-GUI.exe")
        exe_name = "MajsoulMax-GUI.exe"
        version_type = "GUI图形界面版本"

    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB

        print("\n" + "="*60)
        print(f"           雀魂MAX独立版构建成功完成！({version_type})")
        print("           设计者：Nfilmjon (小约)")
        print("="*60)
        print(f"📁 文件位置: {exe_path.absolute()}")
        print(f"📊 文件大小: {file_size:.1f} MB")
        print()

        if build_choice == 1:
            print("🚀 使用方法 (命令行版):")
            print(f"1. 将 {exe_name} 复制到任意位置")
            print("2. 双击运行 (等同于: mitmdump -p 23410 -s addons.py)")
            print("3. 按照程序提示配置浏览器或游戏客户端")
            print()
            print("⚠️  注意事项:")
            print("- 首次运行需要网络连接来下载协议文件")
            print("- 某些杀毒软件可能误报，请添加白名单")
            print("- 程序运行时会显示控制台窗口，请勿关闭")
        else:
            print("🚀 使用方法 (GUI版):")
            print(f"1. 将 {exe_name} 复制到任意位置")
            print("2. 双击运行，会显示图形界面")
            print("3. 在界面中配置和启动雀魂助手")
            print("4. 支持透明度调节、一键启动等功能")
            print()
            print("⚠️  注意事项:")
            print("- 首次运行需要网络连接来下载协议文件")
            print("- 某些杀毒软件可能误报，请添加白名单")
            print("- GUI版本包含完整的图形界面和背景图")
            print("- 支持系统托盘和窗口最小化")

        print("="*50)
        return True
    else:
        print(f"\n❌ 构建失败: 未找到输出文件 {exe_path}")
        return False

def main():
    """主函数"""
    print_header()

    # 获取用户选择
    build_choice = get_build_choice()
    if build_choice is None:
        return 1

    # 检查环境
    if not check_python():
        input("\n按回车键退出...")
        return 1

    if not check_project_files(build_choice):
        input("\n按回车键退出...")
        return 1

    # 构建流程
    steps = [
        install_dependencies,
        install_pyinstaller,
        clean_build,
        lambda: build_exe(build_choice)  # 传递构建选择参数
    ]

    for step in steps:
        if not step():
            print(f"\n❌ 构建失败于步骤: {step.__name__}")
            input("\n按回车键退出...")
            return 1

    # 显示结果
    if show_results(build_choice):
        input("\n✅ 构建完成！按回车键退出...")
        return 0
    else:
        input("\n❌ 构建失败！按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())

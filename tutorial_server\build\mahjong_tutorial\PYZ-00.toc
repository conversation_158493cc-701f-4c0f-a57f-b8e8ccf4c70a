('D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\PYZ-00.pyz',
 [('_compat_pickle', 'D:\\dev\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\dev\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\dev\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\dev\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\dev\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\dev\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\dev\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\dev\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\dev\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\dev\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\dev\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\dev\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\dev\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\dev\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\dev\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\dev\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\dev\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\dev\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\dev\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\dev\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\dev\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\dev\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\dev\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\dev\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\dev\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\dev\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\dev\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'D:\\dev\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\dev\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\dev\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\dev\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\dev\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\dev\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'D:\\dev\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\dev\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\dev\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'D:\\dev\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\dev\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\dev\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'D:\\dev\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\dev\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('embedded_static',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\embedded_static.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\dev\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\dev\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\dev\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\dev\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\dev\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\dev\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('html', 'D:\\dev\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\dev\\Python312\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\dev\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\dev\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.server', 'D:\\dev\\Python312\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'D:\\dev\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\dev\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\dev\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\dev\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\dev\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\dev\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\dev\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\dev\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\dev\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\dev\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('logging', 'D:\\dev\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\dev\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\dev\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('numbers', 'D:\\dev\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\dev\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\dev\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\dev\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\dev\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\dev\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\dev\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\dev\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\dev\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\dev\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\dev\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\dev\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\dev\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\dev\\Python312\\Lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'D:\\dev\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\dev\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\dev\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\dev\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\dev\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\dev\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\dev\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\dev\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\dev\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\dev\\Python312\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\dev\\Python312\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\dev\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\dev\\Python312\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\dev\\Python312\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\dev\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\dev\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\dev\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\dev\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\dev\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\dev\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('webbrowser', 'D:\\dev\\Python312\\Lib\\webbrowser.py', 'PYMODULE'),
  ('zipfile', 'D:\\dev\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\dev\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\dev\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])

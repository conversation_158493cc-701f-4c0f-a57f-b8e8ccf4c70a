#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雀魂MAX独立版GUI界面
设计者：Nfilmjon
基于iOS设计风格的现代化GUI界面
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import threading
import asyncio
import os
import subprocess
import queue
import time
import logging
import sys
from datetime import datetime
from PIL import Image, ImageTk, ImageFilter, ImageEnhance
from ruamel.yaml import YAML
# 延迟导入addons，只在需要时导入

def get_resource_path(relative_path):
    """获取资源文件的绝对路径，支持PyInstaller打包后的exe"""
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except AttributeError:
        # 如果不是打包后的exe，使用当前脚本目录
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, relative_path)

# 配置日志系统
def setup_logging():
    """设置日志系统"""
    try:
        log_dir = get_resource_path("log")
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_file = os.path.join(log_dir, f"gui_{datetime.now().strftime('%Y%m%d')}.log")

        # 清除现有的处理器
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s | %(levelname)s | %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ],
            force=True  # 强制重新配置
        )
        return logging.getLogger(__name__)
    except Exception as e:
        # 如果日志设置失败，创建一个基本的控制台日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s | %(levelname)s | %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)],
            force=True
        )
        logger = logging.getLogger(__name__)
        logger.error(f"日志文件设置失败，使用控制台输出: {e}")
        return logger

# 初始化日志
try:
    logger = setup_logging()
    if logger is None:
        # 如果日志设置失败，创建一个基本的日志器
        import logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
        logger = logging.getLogger(__name__)
        logger.warning("日志系统初始化失败，使用基本日志器")
except Exception as e:
    # 最后的备用方案
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
    logger = logging.getLogger(__name__)
    logger.error(f"日志系统初始化异常: {e}")

# 设置外观模式和颜色主题
ctk.set_appearance_mode("light")  # iOS风格使用浅色模式
ctk.set_default_color_theme("blue")  # 使用蓝色主题

# 优化的配色方案 - iOS风格
class Colors:
    # 主色调
    PRIMARY_BLUE = "#007AFF"
    PRIMARY_BLUE_HOVER = "#0056CC"
    PRIMARY_BLUE_PRESSED = "#004499"

    # 次要颜色
    SECONDARY_GRAY = "#F2F2F7"
    SECONDARY_GRAY_HOVER = "#E5E5EA"
    SECONDARY_GRAY_PRESSED = "#D1D1D6"

    # 状态颜色
    SUCCESS_GREEN = "#34C759"
    SUCCESS_GREEN_HOVER = "#30D158"
    WARNING_ORANGE = "#FF9500"
    WARNING_ORANGE_HOVER = "#FF9F0A"
    ERROR_RED = "#FF3B30"
    ERROR_RED_HOVER = "#FF453A"

    # 文本颜色
    TEXT_PRIMARY = "#1D1D1F"
    TEXT_SECONDARY = "#86868B"
    TEXT_TERTIARY = "#C7C7CC"

    # 背景颜色
    BACKGROUND_PRIMARY = "#FFFFFF"
    BACKGROUND_SECONDARY = "#F2F2F7"
    BACKGROUND_CARD = "#FFFFFF"

    # 阴影颜色
    SHADOW_LIGHT = "rgba(0, 0, 0, 0.1)"
    SHADOW_MEDIUM = "rgba(0, 0, 0, 0.15)"
    SHADOW_HEAVY = "rgba(0, 0, 0, 0.25)"

class MajsoulMaxGUI:
    def __init__(self):
        logger.info("🚀 启动雀魂MAX GUI界面...")

        self.root = ctk.CTk()
        self.root.title("雀魂果茶大助手")

        # 获取屏幕尺寸并设置合适的初始窗口大小
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 设置窗口大小为屏幕的70%，但不小于最小尺寸
        min_width, min_height = 900, 700
        init_width = max(min_width, int(screen_width * 0.7))
        init_height = max(min_height, int(screen_height * 0.7))

        self.root.geometry(f"{init_width}x{init_height}")
        self.root.minsize(min_width, min_height)  # 设置最小尺寸
        self.root.resizable(True, True)

        # 窗口居中显示
        x = (screen_width - init_width) // 2
        y = (screen_height - init_height) // 2
        self.root.geometry(f"{init_width}x{init_height}+{x}+{y}")

        # 设置窗口背景色
        self.root.configure(fg_color=Colors.BACKGROUND_PRIMARY)

        # 设置窗口图标 - 支持高分辨率图标
        self._load_window_icon()

        # 初始化配置文件
        self._init_config_files()

        # 初始化变量
        self.is_running = False
        self.helper_running = False
        self.mitm_task = None
        self.helper_process = None
        self.yaml = YAML()

        # 背景增强参数（设计文档：实时模糊与亮度调节）
        self.bg_blur_level = 0            # 0-10 级
        self.bg_brightness = 1.0          # 0.3 - 1.5
        self.bg_image_path = get_resource_path("1.png")
        self._bg_original_image = None    # 原始背景图（PIL.Image）

        # 主题/外观模式
        self.appearance_mode = "light"  # light/dark

        # 快捷键
        self.root.bind('<F11>', lambda *_: self.toggle_fullscreen())
        self.root.bind('<Control-t>', lambda *_: self.toggle_hide_interface())
        self.root.bind('<Control-b>', lambda *_: self.change_background_image())

        # 助手相关变量
        self.helper_output_queue = queue.Queue()
        self.helper_thread = None
        self.helper_embedded = True  # 默认使用嵌入模式
        self.helper_reader_thread = None

        # 字体大小设置
        self.font_sizes = {
            'small': 10,
            'medium': 12,
            'large': 14,
            'xlarge': 16
        }
        self.current_font_size = 'medium'

        # 组件透明度设置（用于显示背景图片）
        self.component_transparency_levels = {
            '完全不透明': ("gray95", "gray15"),
            '轻微透明': ("gray85", "gray25"),
            '半透明': ("gray75", "gray35"),
            '高透明': ("gray65", "gray45"),
            '很透明': ("gray50", "gray60"),
            '极透明': ("gray35", "gray75"),  # 非常透明
            '几乎隐形': ("gray20", "gray85"),  # 极度透明
            '隐藏界面': "HIDE_MODE",  # 特殊模式：隐藏界面显示背景
            '纯文字模式': "TEXT_ONLY_MODE",  # 新模式：只显示文字和按钮
            '完全透明背景': "FULL_TRANSPARENT_MODE"  # 最激进模式：所有背景完全透明
        }
        self.current_component_transparency = '半透明'
        self.hidden_components = []  # 存储被隐藏的组件

        # 字体颜色设置
        self.font_colors = {
            '黑色': '#000000',
            '深灰': '#333333',
            '蓝色': '#0066CC',
            '绿色': '#006600',
            '红色': '#CC0000',
            '紫色': '#6600CC',
            '白色': '#FFFFFF',
            '亮绿': '#00FF00',
            '亮蓝': '#0099FF',
            '橙色': '#FF6600'
        }
        self.current_font_color = '黑色'

        # 动画相关变量
        self.animation_running = False

        # 绑定窗口事件
        self.root.bind('<Configure>', self.on_window_resize)

        # 创建界面
        self.create_widgets()
        self.load_settings()

        # 启动时的欢迎动画
        self.welcome_animation()

        # 启动输出更新定时器
        self.update_helper_output()

        logger.info("✅ GUI界面初始化完成")

    def setup_background(self):
        """设置背景图片（支持模糊/亮度/自适应文字）"""
        try:
            self.bg_image_path = get_resource_path("1.png")
            if self.bg_image_path and os.path.exists(self.bg_image_path):
                self._bg_original_image = Image.open(self.bg_image_path)
                # 初次渲染
                self._render_background()
                # 监听窗口变化
                self.root.bind('<Configure>', self.on_window_configure)
                logger.info(f"✅ 背景图片加载成功: {self.bg_image_path}")
            else:
                logger.warning(f"⚠️ 背景图片不存在: {self.bg_image_path}")
        except Exception as e:
            logger.error(f"❌ 背景图片加载失败: {e}")

    def on_window_configure(self, event):
        """窗口配置改变事件处理"""
        if event.widget == self.root:
            # 更新背景图片大小
            self.update_background_size()
            # 调用原来的窗口大小改变处理
            self.on_window_resize(event)

    def _load_window_icon(self):
        """加载窗口图标，支持高分辨率图标适配"""
        try:
            # 优先使用favicon.ico（使用资源路径）
            icon_path = get_resource_path("favicon.ico")
            if os.path.exists(icon_path):
                # 对于CustomTkinter，使用iconbitmap方法
                self.root.iconbitmap(icon_path)

                # 同时设置wm_iconbitmap以确保兼容性
                try:
                    self.root.wm_iconbitmap(icon_path)
                except:
                    pass

                # 如果是高DPI环境，尝试设置图标照片（用于任务栏等）
                try:
                    from PIL import Image
                    icon_image = Image.open(icon_path)

                    # 为不同尺寸创建图标
                    icon_sizes = [16, 32, 48, 64, 128]
                    icon_photos = []

                    for size in icon_sizes:
                        if icon_image.size[0] >= size:
                            resized_icon = icon_image.resize((size, size), Image.Resampling.LANCZOS)
                            icon_photo = ImageTk.PhotoImage(resized_icon)
                            icon_photos.append(icon_photo)

                    # 保存引用防止垃圾回收
                    self.icon_photos = icon_photos

                    # 设置主图标（使用最大尺寸）
                    if icon_photos:
                        self.root.iconphoto(True, *icon_photos)

                except Exception as icon_photo_error:
                    logger.debug(f"图标照片设置失败（不影响基本功能）: {icon_photo_error}")

                logger.info("✅ 窗口图标加载成功: favicon.ico (128x128)")

            else:
                # 尝试备用图标
                icon_path = get_resource_path("icon.ico")
                if os.path.exists(icon_path):
                    self.root.iconbitmap(icon_path)
                    try:
                        self.root.wm_iconbitmap(icon_path)
                    except:
                        pass
                    logger.info("✅ 窗口图标加载成功: icon.ico")
                else:
                    logger.warning("⚠️ 未找到图标文件 (favicon.ico 或 icon.ico)")

        except Exception as e:
            logger.warning(f"⚠️ 窗口图标加载失败: {e}")
            # 尝试设置默认图标
            try:
                # 创建一个简单的默认图标
                self._create_default_icon()
            except:
                pass

    def _create_default_icon(self):
        """创建默认图标（当找不到图标文件时）"""
        try:
            from PIL import Image, ImageDraw

            # 创建128x128的默认图标
            icon_size = 128
            icon_image = Image.new('RGBA', (icon_size, icon_size), (70, 130, 180, 255))  # 钢蓝色背景
            draw = ImageDraw.Draw(icon_image)

            # 绘制简单的"M"字母（代表MajsoulMax）
            font_size = icon_size // 2
            text = "M"

            # 计算文字位置（居中）
            try:
                # 尝试使用默认字体
                bbox = draw.textbbox((0, 0), text)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                x = (icon_size - text_width) // 2
                y = (icon_size - text_height) // 2

                # 绘制白色文字
                draw.text((x, y), text, fill=(255, 255, 255, 255))
            except:
                # 如果textbbox不可用，使用简单的居中
                draw.text((icon_size//3, icon_size//3), text, fill=(255, 255, 255, 255))

            # 转换为PhotoImage并设置
            icon_photo = ImageTk.PhotoImage(icon_image)
            self.root.iconphoto(True, icon_photo)
            self.default_icon_photo = icon_photo  # 保存引用

            logger.info("✅ 已创建默认窗口图标")

        except Exception as e:
            logger.debug(f"默认图标创建失败: {e}")

    def _init_config_files(self):
        """初始化配置文件，确保在打包环境中正确处理"""
        try:
            # 检查配置文件是否存在
            settings_path = get_resource_path('config/settings.yaml')

            if not os.path.exists(settings_path):
                logger.warning("⚠️ 配置文件不存在，创建默认配置")

                # 确保配置目录存在
                config_dir = get_resource_path("config")
                if not os.path.exists(config_dir):
                    os.makedirs(config_dir)

                # 创建默认配置
                default_config = {
                    'plugin_enable': {
                        'mod': True,
                        'helper': True
                    },
                    'liqi': {
                        'auto_update': True,
                        'liqi_version': 'latest'
                    }
                }

                # 保存默认配置
                with open(settings_path, 'w', encoding='utf-8') as f:
                    self.yaml.dump(default_config, f)

                logger.info(f"✅ 默认配置已创建: {settings_path}")
            else:
                logger.info(f"✅ 配置文件已找到: {settings_path}")

        except Exception as e:
            logger.error(f"❌ 配置文件初始化失败: {e}")

    def _render_background(self, width=None, height=None):
        """根据当前窗口尺寸与增强参数渲染背景图到Canvas。
        - 模糊: 0-10级
        - 亮度: 0.3-1.5
        """
        try:
            # 源图准备
            if self._bg_original_image is None:
                if self.bg_image_path and os.path.exists(self.bg_image_path):
                    self._bg_original_image = Image.open(self.bg_image_path)
                else:
                    return

            # 目标尺寸
            if width is None or height is None:
                width = max(1, self.root.winfo_width())
                height = max(1, self.root.winfo_height())
                if width <= 1 or height <= 1:
                    width, height = 900, 700

            # 先cover到窗口尺寸
            img = self._compose_background_image(self._bg_original_image, width, height)

            # 应用模糊
            blur = max(0, min(10, int(self.bg_blur_level)))
            if blur > 0:
                img = img.filter(ImageFilter.GaussianBlur(radius=blur))

            # 应用亮度
            brightness = max(0.3, min(1.5, float(self.bg_brightness)))
            if abs(brightness - 1.0) > 1e-3:
                img = ImageEnhance.Brightness(img).enhance(brightness)

            # 输出到画布
            self.bg_photo = ImageTk.PhotoImage(img)

            if not hasattr(self, 'bg_canvas') or not self.bg_canvas.winfo_exists():
                self.bg_canvas = tk.Canvas(
                    self.root, width=width, height=height, highlightthickness=0, bd=0
                )
                self.bg_canvas.place(x=0, y=0)
            else:
                self.bg_canvas.configure(width=width, height=height)
                self.bg_canvas.delete("all")

            self.bg_canvas.create_image(0, 0, anchor="nw", image=self.bg_photo)
            try:
                self.bg_canvas.lower()
            except Exception:
                pass

            # 根据背景亮度自适应主题/文字色
            self.apply_background_aware_ui(img)
        except Exception as e:
            logger.error(f"❌ 渲染背景失败: {e}")

    def refresh_background(self, delay_ms: int = 40):
        """节流刷新背景，避免滑块连续调整导致卡顿。"""
        try:
            if hasattr(self, '_bg_render_job') and self._bg_render_job:
                self.root.after_cancel(self._bg_render_job)
        except Exception:
            pass
        self._bg_render_job = self.root.after(delay_ms, lambda: self._render_background())

    def apply_background_aware_ui(self, img=None):
        """根据背景图亮度调整部分文本和控件的颜色对比度。"""
        try:
            src = img if img is not None else getattr(self, '_bg_original_image', None)
            if src is None:
                return
            # 取样缩放，计算平均亮度
            small = src.copy()
            small.thumbnail((64, 64))
            gray = small.convert('L')
            avg = sum(gray.getdata()) / (gray.width * gray.height) / 255.0
            # 亮背景 -> 深色文字；暗背景 -> 浅色文字
            primary_text = "#1D1D1F" if avg > 0.55 else "#FFFFFF"
            secondary_text = "#333333" if avg > 0.55 else "#DDDDDD"

            # 顶部标题
            try:
                for w_name in ['title_frame']:
                    if hasattr(self, w_name):
                        frame = getattr(self, w_name)
                        for child in frame.winfo_children():
                            if isinstance(child, ctk.CTkLabel):
                                child.configure(text_color=primary_text)
            except Exception:
                pass

            # 选项卡文字颜色
            if hasattr(self, 'tabview'):
                try:
                    self.tabview.configure(text_color=primary_text)
                except Exception:
                    pass

            # 状态/标题等常见Label
            for name in ['status_label', 'helper_status_label']:
                if hasattr(self, name):
                    try:
                        getattr(self, name).configure(text_color=primary_text)
                    except Exception:
                        pass
        except Exception as e:
            logger.debug(f"背景自适应UI失败: {e}")

    def _compose_background_image(self, src_img, target_w, target_h):
        """按窗口铺满方式生成背景图，返回裁剪后的 PIL.Image。"""
        try:
            img_w, img_h = src_img.size
            scale = max(target_w / img_w, target_h / img_h)  # cover
            new_w, new_h = int(img_w * scale), int(img_h * scale)
            resized = src_img.resize((new_w, new_h), Image.Resampling.LANCZOS)
            x0 = max(0, (new_w - target_w) // 2)
            y0 = max(0, (new_h - target_h) // 2)
            cropped = resized.crop((x0, y0, x0 + target_w, y0 + target_h))
            return cropped
        except Exception as e:
            logger.error(f"❌ 生成背景图失败: {e}")
            return src_img

    def update_background_size(self):
        """更新背景图片大小（随窗口缩放重渲染）"""
        try:
            self._render_background()
        except Exception as e:
            logger.error(f"❌ 更新背景图片失败: {e}")

    def on_window_resize(self, event):
        """窗口大小改变事件处理"""
        if event.widget == self.root:
            # 获取当前窗口尺寸
            width = self.root.winfo_width()

            # 根据窗口大小调整字体
            if hasattr(self, 'helper_output_text'):
                if width < 1000:
                    base_size = self.font_sizes['small']
                elif width < 1200:
                    base_size = self.font_sizes['medium']
                else:
                    base_size = self.font_sizes[self.current_font_size]

                # 更新输出文本框字体
                try:
                    current_font = self.helper_output_text.cget("font")
                    if isinstance(current_font, ctk.CTkFont):
                        current_font.configure(size=base_size)
                except:
                    pass

    def change_font_size(self, selected_label):
        """改变字体大小"""
        font_labels = {"小": "small", "中": "medium", "大": "large", "特大": "xlarge"}
        size_key = font_labels.get(selected_label, "medium")
        self.current_font_size = size_key

        # 更新助手输出文本框字体
        if hasattr(self, 'helper_output_text'):
            try:
                new_font = ctk.CTkFont(
                    size=self.font_sizes[size_key],
                    family="Microsoft YaHei UI"
                )
                self.helper_output_text.configure(font=new_font)
                logger.info(f"📝 字体大小已调整为: {selected_label} ({self.font_sizes[size_key]}px)")
            except Exception as e:
                logger.error(f"❌ 字体调整失败: {e}")

    def change_transparency(self, selected_level):
        """改变界面组件透明度以显示背景图片"""
        self.current_component_transparency = selected_level

        # 特殊处理不同模式
        if selected_level == '隐藏界面':
            self.toggle_hide_interface()
        elif selected_level == '纯文字模式':
            self.apply_text_only_mode()
        elif selected_level == '完全透明背景':
            self.apply_full_transparent_mode()
        else:
            # 如果之前是特殊模式，先恢复界面
            if self.hidden_components:
                self.restore_interface()

            colors = self.component_transparency_levels[selected_level]

            # 特殊处理极高透明度
            if selected_level in ['极透明', '几乎隐形']:
                self.apply_extreme_transparency()
            else:
                # 更新所有主要组件的颜色
                self.update_component_colors(colors)

        logger.info(f"🌟 组件透明度已调整为: {selected_level}")

    def change_font_color(self, selected_color):
        """改变助手输出字体颜色"""
        self.current_font_color = selected_color
        color_value = self.font_colors[selected_color]

        # 更新助手输出文本框字体颜色
        if hasattr(self, 'helper_output_text'):
            try:
                self.helper_output_text.configure(text_color=color_value)
                logger.info(f"🎨 助手字体颜色已调整为: {selected_color} ({color_value})")
            except Exception as e:
                logger.error(f"❌ 字体颜色调整失败: {e}")

    def apply_extreme_transparency(self):
        """应用极高透明度效果"""
        try:
            # 对于极高透明度，我们尝试多种方法来实现真正的透明

            # 方法1: 尝试使用透明色
            transparent_color = "transparent"

            # 方法2: 使用极浅的颜色
            ultra_light_color = ("gray10", "gray90")

            # 对于极透明和几乎隐形，使用特殊的透明处理

            # 更新所有框架
            frames_to_update = [
                'title_frame', 'control_frame', 'status_frame', 'config_frame',
                'helper_status_frame', 'helper_output_frame',
                'config_management_frame', 'config_status_frame'
            ]

            for frame_name in frames_to_update:
                if hasattr(self, frame_name):
                    frame = getattr(self, frame_name)
                    try:
                        # 首先尝试透明
                        frame.configure(
                            fg_color=transparent_color,
                            border_width=0,
                            corner_radius=0
                        )
                    except:
                        try:
                            # 如果透明失败，使用极浅色
                            frame.configure(
                                fg_color=ultra_light_color,
                                border_width=0,
                                corner_radius=0
                            )
                        except:
                            pass

            # 特殊处理选项卡视图
            if hasattr(self, 'tabview'):
                try:
                    self.tabview.configure(
                        fg_color=transparent_color,
                        border_width=0,
                        segmented_button_fg_color=ultra_light_color
                    )
                except:
                    pass

            # 更新文本框
            text_boxes = ['config_text', 'helper_output_text', 'config_status_text']
            for text_box_name in text_boxes:
                if hasattr(self, text_box_name):
                    text_box = getattr(self, text_box_name)
                    try:
                        text_box.configure(
                            fg_color=("gray5", "gray95"),  # 极透明文本框
                            border_width=0
                        )
                    except:
                        pass

        except Exception as e:
            logger.error(f"❌ 应用极高透明度失败: {e}")

    def hide_components_for_transparency(self):
        """隐藏组件以实现完全透明效果"""
        try:
            # 这是一个实验性功能：临时隐藏组件来显示背景
            components_to_hide = [
                'title_frame', 'control_frame', 'tabview'
            ]

            for comp_name in components_to_hide:
                if hasattr(self, comp_name):
                    comp = getattr(self, comp_name)
                    try:
                        comp.pack_forget()  # 临时隐藏
                    except:
                        pass

            # 显示一个提示，告诉用户如何恢复界面
            if hasattr(self, 'root'):
                self.root.title("雀魂MAX - 完全透明模式 (按Ctrl+T恢复界面)")

        except Exception as e:
            logger.error(f"❌ 隐藏组件失败: {e}")

    def toggle_hide_interface(self):
        """切换隐藏界面模式以完全显示背景图片"""
        try:
            if not self.hidden_components:
                # 隐藏界面组件
                components_to_hide = [
                    'title_frame',
                    'control_frame',
                    'tabview'
                ]

                for comp_name in components_to_hide:
                    if hasattr(self, comp_name):
                        comp = getattr(self, comp_name)
                        # 保存组件的pack信息
                        pack_info = comp.pack_info()
                        self.hidden_components.append((comp_name, pack_info))
                        # 隐藏组件
                        comp.pack_forget()

                # 更新窗口标题提示用户
                if hasattr(self, 'root'):
                    self.root.title("雀魂MAX - 背景显示模式 (重新选择透明度恢复界面)")

                logger.info("🖼️ 界面已隐藏，显示背景图片")

            else:
                # 恢复界面
                self.restore_interface()

        except Exception as e:
            logger.error(f"❌ 切换隐藏界面失败: {e}")

    def restore_interface(self):
        """恢复被隐藏的界面组件"""
        try:
            for comp_name, pack_info in self.hidden_components:
                if hasattr(self, comp_name):
                    comp = getattr(self, comp_name)
                    # 恢复组件显示
                    comp.pack(**pack_info)

            # 清空隐藏组件列表
            self.hidden_components = []

            # 恢复窗口标题
            if hasattr(self, 'root'):
                self.root.title("雀魂MAX独立版 - 集成助手版")

            logger.info("🔄 界面已恢复显示")

        except Exception as e:
            logger.error(f"❌ 恢复界面失败: {e}")

    def apply_text_only_mode(self):
        """应用纯文字模式：去除所有框架背景，只保留文字和按钮"""
        try:
            # 如果之前是隐藏模式，先恢复界面
            if self.hidden_components:
                self.restore_interface()

            # 设置所有框架为完全透明
            transparent_color = "transparent"

            # 更新所有框架为透明
            frames_to_make_transparent = [
                'title_frame', 'control_frame', 'status_frame', 'config_frame',
                'helper_status_frame', 'helper_output_frame',
                'config_management_frame', 'config_status_frame'
            ]

            for frame_name in frames_to_make_transparent:
                if hasattr(self, frame_name):
                    frame = getattr(self, frame_name)
                    try:
                        frame.configure(
                            fg_color=transparent_color,
                            border_width=0,
                            corner_radius=0
                        )
                    except Exception as e:
                        logger.debug(f"设置框架透明失败 {frame_name}: {e}")

            # 特殊处理选项卡视图
            if hasattr(self, 'tabview'):
                try:
                    self.tabview.configure(
                        fg_color=transparent_color,
                        border_width=0,
                        corner_radius=0,
                        segmented_button_fg_color=transparent_color,
                        segmented_button_selected_color=("gray70", "gray30"),
                        segmented_button_selected_hover_color=("gray60", "gray40")
                    )
                except Exception as e:
                    logger.debug(f"设置选项卡透明失败: {e}")

            # 设置文本框也为透明背景
            text_boxes = ['config_text', 'helper_output_text', 'config_status_text']
            for text_box_name in text_boxes:
                if hasattr(self, text_box_name):
                    text_box = getattr(self, text_box_name)
                    try:
                        text_box.configure(
                            fg_color=transparent_color,  # 文本框也透明
                            border_width=0,
                            corner_radius=0
                        )
                    except Exception as e:
                        logger.debug(f"设置文本框透明失败 {text_box_name}: {e}")

            # 设置主窗口背景也为透明
            try:
                self.root.configure(fg_color=transparent_color)
            except Exception as e:
                logger.debug(f"设置主窗口透明失败: {e}")

            # 设置所有可能的组件为透明
            additional_components = [
                'main_frame', 'content_frame', 'left_frame', 'right_frame'
            ]

            for comp_name in additional_components:
                if hasattr(self, comp_name):
                    comp = getattr(self, comp_name)
                    try:
                        comp.configure(
                            fg_color=transparent_color,
                            border_width=0,
                            corner_radius=0
                        )
                    except Exception as e:
                        logger.debug(f"设置组件透明失败 {comp_name}: {e}")

            # 更新窗口标题
            if hasattr(self, 'root'):
                self.root.title("雀魂MAX - 纯文字模式 (重新选择透明度恢复框架)")

            logger.info("📝 已切换到纯文字模式，框架背景已移除")

        except Exception as e:
            logger.error(f"❌ 应用纯文字模式失败: {e}")

    def apply_full_transparent_mode(self):
        """应用完全透明背景模式：让所有背景完全透明，只显示文字和按钮"""
        try:
            # 如果之前是隐藏模式，先恢复界面
            if self.hidden_components:
                self.restore_interface()

            # 完全透明
            transparent_color = "transparent"

            # 获取所有可能的组件并设为透明
            all_components = []

            # 遍历所有属性，找到所有CustomTkinter组件
            for attr_name in dir(self):
                if not attr_name.startswith('_'):
                    attr = getattr(self, attr_name)
                    # 检查是否是CustomTkinter组件
                    if hasattr(attr, 'configure') and hasattr(attr, 'cget'):
                        try:
                            # 尝试获取fg_color属性，如果有则是可设置背景的组件
                            attr.cget('fg_color')
                            all_components.append((attr_name, attr))
                        except:
                            pass

            # 设置所有找到的组件为透明
            for comp_name, comp in all_components:
                try:
                    comp.configure(
                        fg_color=transparent_color,
                        border_width=0,
                        corner_radius=0
                    )
                    logger.debug(f"✅ {comp_name} 已设为透明")
                except Exception as e:
                    logger.debug(f"⚠️ {comp_name} 透明设置失败: {e}")

            # 特殊处理一些组件
            special_components = {
                'tabview': {
                    'segmented_button_fg_color': transparent_color,
                    'segmented_button_selected_color': ("gray80", "gray30"),
                    'segmented_button_selected_hover_color': ("gray70", "gray40")
                }
            }

            for comp_name, special_config in special_components.items():
                if hasattr(self, comp_name):
                    comp = getattr(self, comp_name)
                    try:
                        comp.configure(**special_config)
                        logger.debug(f"✅ {comp_name} 特殊配置已应用")
                    except Exception as e:
                        logger.debug(f"⚠️ {comp_name} 特殊配置失败: {e}")

            # 更新窗口标题
            if hasattr(self, 'root'):
                self.root.title("雀魂MAX - 完全透明背景模式")

            logger.info("🌟 已切换到完全透明背景模式，所有背景已移除")

        except Exception as e:
            logger.error(f"❌ 应用完全透明背景模式失败: {e}")

    def update_component_colors(self, colors):
        """更新所有组件的颜色以实现透明度效果"""
        try:
            # 检查是否为极透明模式
            is_extreme_transparent = self.current_component_transparency in ['极透明', '几乎隐形']

            # 如果是极透明模式，使用特殊处理
            if is_extreme_transparent:
                # 对于极透明，我们需要使用非常浅的颜色或者隐藏边框
                frame_color = ("gray95", "gray20")  # 使用很浅的颜色
                border_width = 0  # 移除边框
            else:
                frame_color = colors
                border_width = 1

            # 更新标题区域
            if hasattr(self, 'title_frame'):
                self.title_frame.configure(fg_color=frame_color)
                if hasattr(self.title_frame, 'configure'):
                    try:
                        self.title_frame.configure(border_width=border_width)
                    except:
                        pass

            # 更新控制区域
            if hasattr(self, 'control_frame'):
                self.control_frame.configure(fg_color=frame_color)

            # 更新选项卡视图
            if hasattr(self, 'tabview'):
                if is_extreme_transparent:
                    # 极透明时选项卡使用更透明的设置
                    self.tabview.configure(
                        fg_color=("gray90", "gray25"),
                        border_width=0
                    )
                else:
                    self.tabview.configure(fg_color=colors)

            # 更新状态框架
            if hasattr(self, 'status_frame'):
                self.status_frame.configure(fg_color=frame_color)
                if is_extreme_transparent:
                    try:
                        self.status_frame.configure(border_width=0)
                    except:
                        pass

            # 更新配置框架
            if hasattr(self, 'config_frame'):
                self.config_frame.configure(fg_color=frame_color)
                if is_extreme_transparent:
                    try:
                        self.config_frame.configure(border_width=0)
                    except:
                        pass

            # 更新助手状态框架
            if hasattr(self, 'helper_status_frame'):
                self.helper_status_frame.configure(fg_color=frame_color)
                if is_extreme_transparent:
                    try:
                        self.helper_status_frame.configure(border_width=0)
                    except:
                        pass

            # 更新助手输出框架
            if hasattr(self, 'helper_output_frame'):
                self.helper_output_frame.configure(fg_color=frame_color)
                if is_extreme_transparent:
                    try:
                        self.helper_output_frame.configure(border_width=0)
                    except:
                        pass

            # 更新设置相关框架
            if hasattr(self, 'config_management_frame'):
                self.config_management_frame.configure(fg_color=frame_color)
                if is_extreme_transparent:
                    try:
                        self.config_management_frame.configure(border_width=0)
                    except:
                        pass

            if hasattr(self, 'config_status_frame'):
                self.config_status_frame.configure(fg_color=frame_color)
                if is_extreme_transparent:
                    try:
                        self.config_status_frame.configure(border_width=0)
                    except:
                        pass

            # 更新文本框背景颜色
            text_bg_color = self.get_text_bg_color()
            if hasattr(self, 'config_text'):
                self.config_text.configure(fg_color=text_bg_color)
                if is_extreme_transparent:
                    try:
                        self.config_text.configure(border_width=0)
                    except:
                        pass

            if hasattr(self, 'helper_output_text'):
                self.helper_output_text.configure(fg_color=text_bg_color)
                if is_extreme_transparent:
                    try:
                        self.helper_output_text.configure(border_width=0)
                    except:
                        pass

            if hasattr(self, 'config_status_text'):
                self.config_status_text.configure(fg_color=text_bg_color)
                if is_extreme_transparent:
                    try:
                        self.config_status_text.configure(border_width=0)
                    except:
                        pass

        except Exception as e:
            logger.error(f"❌ 更新组件颜色失败: {e}")

    def get_text_bg_color(self):
        """根据当前透明度级别获取文本框背景颜色"""
        transparency_map = {
            '完全不透明': ("gray98", "gray12"),
            '轻微透明': ("gray90", "gray20"),
            '半透明': ("gray80", "gray30"),
            '高透明': ("gray70", "gray40"),
            '很透明': ("gray55", "gray55"),
            '极透明': ("gray35", "gray75"),  # 极透明
            '几乎隐形': ("gray20", "gray85"),  # 几乎隐形
            '隐藏界面': ("gray10", "gray90"),  # 隐藏界面模式
            '纯文字模式': ("gray95", "gray20"),  # 纯文字模式，保持文本框可读性
            '完全透明背景': "transparent"  # 完全透明背景模式
        }
        return transparency_map.get(self.current_component_transparency, ("gray90", "gray20"))

    def create_widgets(self):
        """创建GUI组件"""
        # 设置背景图片
        self.setup_background()

        # 创建主容器，完全透明以显示背景；将其挂到背景标签上，这样透明才能透出图片
        parent_container = self.root  # 避免覆盖背景图的全屏容器
        main_container = ctk.CTkFrame(
            parent_container,
            fg_color="transparent",
            bg_color="transparent",
            corner_radius=0
        )
        # 不再铺满全屏，避免形成一层遮罩
        main_container.pack(padx=10, pady=10)

        # 标题区域 - 可调透明度
        self.title_frame = ctk.CTkFrame(
            main_container,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            corner_radius=15,
            border_width=1,
            border_color=("gray70", "gray40")
        )
        self.title_frame.pack(fill="x", pady=(0, 10))

        # 主标题 - 增强视觉效果
        title_label = ctk.CTkLabel(
            self.title_frame,
            text="雀魂MAX独立版 - 集成助手",
            font=ctk.CTkFont(size=26, weight="bold"),
            text_color=("#1D1D1F", "#FFFFFF")
        )
        title_label.pack(pady=(12, 3))

        # 副标题
        subtitle_label = ctk.CTkLabel(
            self.title_frame,
            text="Desgin:Nifilmjon for GUOCHA(GC)",
            font=ctk.CTkFont(size=13),
            text_color=("#666666", "#CCCCCC")
        )
        subtitle_label.pack(pady=(0, 12))

        # 创建透明度和颜色控制区域
        self.control_frame = ctk.CTkFrame(
            main_container,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            corner_radius=12,
            height=50
        )
        self.control_frame.pack(fill="x", pady=(0, 10))
        self.control_frame.pack_propagate(False)

        # 透明度控制
        transparency_label = ctk.CTkLabel(
            self.control_frame,
            text="组件透明度:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=("#333333", "#CCCCCC")
        )
        transparency_label.pack(side="left", padx=(15, 5), pady=12)

        self.transparency_var = tk.StringVar(value=self.current_component_transparency)
        self.transparency_menu = ctk.CTkOptionMenu(
            self.control_frame,
            values=list(self.component_transparency_levels.keys()),
            width=100,
            height=28,
            corner_radius=14,
            fg_color=("gray75", "gray35"),
            button_color=("gray70", "gray45"),
            text_color=("#333333", "#FFFFFF"),
            command=self.change_transparency
        )
        self.transparency_menu.pack(side="left", padx=(0, 20), pady=12)
        self.transparency_menu.set(self.current_component_transparency)

        # 字体颜色控制
        color_label = ctk.CTkLabel(
            self.control_frame,
            text="助手字体颜色:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=("#333333", "#CCCCCC")
        )
        color_label.pack(side="left", padx=(0, 5), pady=12)

        self.font_color_var = tk.StringVar(value=self.current_font_color)
        self.font_color_menu = ctk.CTkOptionMenu(
            self.control_frame,
            values=list(self.font_colors.keys()),
            width=80,
            height=28,
            corner_radius=14,
            fg_color=("gray75", "gray35"),
            button_color=("gray70", "gray45"),
            text_color=("#333333", "#FFFFFF"),
            command=self.change_font_color
        )
        # 背景增强控制区（模糊/亮度/换背景/主题）
        enhance_frame = ctk.CTkFrame(
            self.control_frame,
            fg_color=("gray80", "gray30"),
            corner_radius=10
        )
        enhance_frame.pack(side="right", padx=12, pady=8)

        # 模糊
        blur_label = ctk.CTkLabel(enhance_frame, text="模糊", font=ctk.CTkFont(size=11))
        blur_label.pack(side="left", padx=(8, 4))
        self.blur_slider = ctk.CTkSlider(
            enhance_frame, from_=0, to=10, number_of_steps=10, width=100,
            command=lambda v: self._on_blur_change(v)
        )
        self.blur_slider.set(self.bg_blur_level)
        self.blur_slider.pack(side="left", padx=(0, 8))

        # 亮度
        bright_label = ctk.CTkLabel(enhance_frame, text="亮度", font=ctk.CTkFont(size=11))
        bright_label.pack(side="left", padx=(4, 4))
        self.brightness_slider = ctk.CTkSlider(
            enhance_frame, from_=0.3, to=1.5, width=120,
            command=lambda v: self._on_brightness_change(v)
        )
        self.brightness_slider.set(self.bg_brightness)
        self.brightness_slider.pack(side="left", padx=(0, 8))

        # 更换背景
        change_bg_btn = ctk.CTkButton(
            enhance_frame, text="更换背景", width=90, height=28,
            fg_color=Colors.PRIMARY_BLUE,
            hover_color=Colors.PRIMARY_BLUE_HOVER,
            command=self.change_background_image
        )
        change_bg_btn.pack(side="left", padx=(4, 8))

        # 主题切换
        theme_btn = ctk.CTkSegmentedButton(
            enhance_frame,
            values=["亮","暗"],
            command=lambda v: self.toggle_theme(v)
        )
        theme_btn.set("亮" if self.appearance_mode == "light" else "暗")
        theme_btn.pack(side="left", padx=(0, 8))

        self.font_color_menu.pack(side="left", padx=(0, 15), pady=12)
        self.font_color_menu.set(self.current_font_color)

        # 创建选项卡视图 - 半透明
        self.tabview = ctk.CTkTabview(
            main_container,
            corner_radius=15,
            border_width=1,
            border_color=("gray70", "gray40"),
            fg_color=("gray95", "gray15"),
            segmented_button_fg_color=("gray85", "gray30"),
            segmented_button_selected_color=Colors.PRIMARY_BLUE,
            segmented_button_selected_hover_color=Colors.PRIMARY_BLUE_HOVER,
            text_color=("#1D1D1F", "#FFFFFF")
        )
        self.tabview.pack(fill="both", expand=True)

        # 创建选项卡
        self.tabview.add("🚀 雀魂MAX")
        self.tabview.add("🎯 麻将助手")
        self.tabview.add("⚙️ 设置")

        # 设置默认选项卡
        self.tabview.set("🚀 雀魂MAX")

        # 创建各个选项卡的内容
        self.create_majsoul_tab()
        self.create_helper_tab()
        self.create_settings_tab()

    def create_majsoul_tab(self):
        """创建雀魂MAX选项卡内容"""
        majsoul_frame = self.tabview.tab("🚀 雀魂MAX")

        # 状态显示区域 - 可调透明度
        self.status_frame = ctk.CTkFrame(
            majsoul_frame,
            corner_radius=15,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.status_frame.pack(pady=(15, 15), padx=15, fill="x")

        # 状态标题
        status_title = ctk.CTkLabel(
            self.status_frame,
            text="📊 系统状态",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_SECONDARY
        )
        status_title.pack(pady=(15, 10))

        # 主服务状态
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="雀魂MAX：❌ 未启动",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        self.status_label.pack(pady=(0, 15))

        # 控制按钮区域
        button_frame = ctk.CTkFrame(majsoul_frame, fg_color="transparent")
        button_frame.pack(pady=(0, 20), padx=15, fill="x")

        # 雀魂MAX控制按钮
        self.start_button = ctk.CTkButton(
            button_frame,
            text="🚀 启动雀魂MAX",
            font=ctk.CTkFont(size=18, weight="bold"),
            height=55,
            corner_radius=28,
            fg_color=Colors.PRIMARY_BLUE,
            hover_color=Colors.PRIMARY_BLUE_HOVER,
            command=self.toggle_service
        )
        self.start_button.pack(pady=(0, 12), fill="x")

        # 快捷操作按钮区域
        quick_button_frame = ctk.CTkFrame(button_frame, fg_color="transparent")
        quick_button_frame.pack(fill="x", pady=(8, 0))

        # 打开日志按钮
        log_button = ctk.CTkButton(
            quick_button_frame,
            text="📋 查看日志",
            font=ctk.CTkFont(size=15),
            height=45,
            corner_radius=22,
            fg_color=Colors.SECONDARY_GRAY,
            text_color=Colors.TEXT_PRIMARY,
            hover_color=Colors.SECONDARY_GRAY_HOVER,
            command=self.open_log_folder
        )
        log_button.pack(side="left", fill="x", expand=True, padx=(0, 6))

        # 重启服务按钮
        restart_button = ctk.CTkButton(
            quick_button_frame,
            text="� 重启服务",
            font=ctk.CTkFont(size=15),
            height=45,
            corner_radius=22,
            fg_color=Colors.WARNING_ORANGE,
            text_color=Colors.TEXT_PRIMARY,
            hover_color=Colors.WARNING_ORANGE_HOVER,
            command=self.restart_service
        )
        restart_button.pack(side="right", fill="x", expand=True, padx=(6, 0))

        # 配置信息显示区域 - 可调透明度
        self.config_frame = ctk.CTkFrame(
            majsoul_frame,
            corner_radius=15,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.config_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        config_title = ctk.CTkLabel(
            self.config_frame,
            text="📊 当前配置信息",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        config_title.pack(pady=(15, 10))

        self.config_text = ctk.CTkTextbox(
            self.config_frame,
            height=120,
            corner_radius=12,
            font=ctk.CTkFont(size=12, family="Microsoft YaHei UI"),
            fg_color=self.get_text_bg_color(),
            text_color=("#1D1D1F", "#FFFFFF"),
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.config_text.pack(pady=(0, 15), padx=15, fill="both", expand=True)

    def create_helper_tab(self):
        """创建麻将助手选项卡内容"""
        helper_frame = self.tabview.tab("🎯 麻将助手")

        # 助手状态区域 - 可调透明度
        self.helper_status_frame = ctk.CTkFrame(
            helper_frame,
            corner_radius=15,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.helper_status_frame.pack(pady=(15, 15), padx=15, fill="x")

        # 状态标题
        helper_status_title = ctk.CTkLabel(
            self.helper_status_frame,
            text="🎯 麻将助手状态",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_SECONDARY
        )
        helper_status_title.pack(pady=(15, 10))

        # 助手状态标签
        self.helper_status_label = ctk.CTkLabel(
            self.helper_status_frame,
            text="麻将助手：❌ 未启动",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        self.helper_status_label.pack(pady=(0, 15))

        # 助手控制按钮区域
        helper_control_frame = ctk.CTkFrame(helper_frame, fg_color="transparent")
        helper_control_frame.pack(pady=(0, 20), padx=15, fill="x")

        # 启动模式选择
        mode_frame = ctk.CTkFrame(helper_control_frame, fg_color="transparent")
        mode_frame.pack(fill="x", pady=(0, 15))

        mode_label = ctk.CTkLabel(
            mode_frame,
            text="启动模式：",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        mode_label.pack(side="left", padx=(0, 10))

        # 模式选择开关
        self.embedded_mode_switch = ctk.CTkSwitch(
            mode_frame,
            text="嵌入模式",
            font=ctk.CTkFont(size=14),
            text_color=Colors.TEXT_PRIMARY,
            progress_color=Colors.PRIMARY_BLUE,
            button_color=Colors.SECONDARY_GRAY,
            button_hover_color=Colors.SECONDARY_GRAY_HOVER,
            command=self.toggle_embedded_mode
        )
        self.embedded_mode_switch.pack(side="left")
        self.embedded_mode_switch.select()  # 默认选择嵌入模式

        # 助手控制按钮
        self.helper_button = ctk.CTkButton(
            helper_control_frame,
            text="🎯 启动麻将助手",
            font=ctk.CTkFont(size=18, weight="bold"),
            height=55,
            corner_radius=28,
            fg_color=Colors.SUCCESS_GREEN,
            hover_color=Colors.SUCCESS_GREEN_HOVER,
            command=self.toggle_helper
        )
        self.helper_button.pack(pady=(0, 15), fill="x")

        # 助手输出显示区域 - 可调透明度
        self.helper_output_frame = ctk.CTkFrame(
            helper_frame,
            corner_radius=15,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.helper_output_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 输出区域标题
        output_title_frame = ctk.CTkFrame(self.helper_output_frame, fg_color="transparent")
        output_title_frame.pack(fill="x", pady=(15, 10), padx=15)

        output_title = ctk.CTkLabel(
            output_title_frame,
            text="📋 助手分析输出",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        output_title.pack(side="left")

        # 清空输出按钮
        clear_button = ctk.CTkButton(
            output_title_frame,
            text="🗑️ 清空",
            font=ctk.CTkFont(size=12),
            width=60,
            height=30,
            corner_radius=15,
            fg_color=Colors.ERROR_RED,
            hover_color=Colors.ERROR_RED_HOVER,
            command=self.clear_helper_output
        )
        clear_button.pack(side="right")

        # 字体控制区域
        font_control_frame = ctk.CTkFrame(output_title_frame, fg_color="transparent")
        font_control_frame.pack(side="right", padx=(10, 0))

        # 字体大小标签
        font_label = ctk.CTkLabel(
            font_control_frame,
            text="字体:",
            font=ctk.CTkFont(size=12),
            text_color=Colors.TEXT_SECONDARY
        )
        font_label.pack(side="left", padx=(0, 5))

        # 字体大小选择
        self.font_size_var = tk.StringVar(value=self.current_font_size)
        font_sizes = ["small", "medium", "large", "xlarge"]
        font_labels = {"small": "小", "medium": "中", "large": "大", "xlarge": "特大"}

        self.font_option_menu = ctk.CTkOptionMenu(
            font_control_frame,
            values=[font_labels[size] for size in font_sizes],
            width=60,
            height=28,
            corner_radius=14,
            fg_color=Colors.SECONDARY_GRAY,
            button_color=Colors.SECONDARY_GRAY_HOVER,
            button_hover_color=Colors.SECONDARY_GRAY_PRESSED,
            text_color=Colors.TEXT_PRIMARY,
            command=self.change_font_size
        )
        self.font_option_menu.pack(side="left")
        self.font_option_menu.set(font_labels[self.current_font_size])

        # 助手输出文本框 - 支持动态颜色和透明度
        self.helper_output_text = ctk.CTkTextbox(
            self.helper_output_frame,
            corner_radius=12,
            font=ctk.CTkFont(size=self.font_sizes[self.current_font_size], family="Microsoft YaHei UI"),
            fg_color=self.get_text_bg_color(),
            text_color=self.font_colors[self.current_font_color],
            border_width=1,
            border_color=("gray75", "gray45"),
            wrap="word"
        )
        self.helper_output_text.pack(pady=(0, 15), padx=15, fill="both", expand=True)

        # 初始化输出文本
        welcome_text = """🎯 麻将助手 - 嵌入式版本

✨ 功能特点：
• 实时分析雀魂对局
• 智能推荐最佳打法
• 显示危险度分析
• 计算听牌概率

🚀 使用说明：
1. 确保雀魂MAX服务已启动
2. 点击"启动麻将助手"按钮
3. 进入雀魂对局即可看到分析结果

💡 提示：
• 嵌入模式：在此界面查看输出
• 独立模式：打开独立窗口运行

等待启动中..."""

        self.helper_output_text.insert("1.0", welcome_text)
        self.helper_output_text.configure(state="disabled")

    def create_settings_tab(self):
        """创建设置选项卡内容"""
        settings_frame = self.tabview.tab("⚙️ 设置")

        # 创建设置容器
        settings_container = ctk.CTkFrame(settings_frame, fg_color="transparent")
        settings_container.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题区域
        title_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        title_frame.pack(fill="x", pady=(0, 20))

        title_label = ctk.CTkLabel(
            title_frame,
            text="🎮 MAX插件设置管理",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        title_label.pack(pady=(10, 5))

        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="管理您的雀魂MAX插件配置文件",
            font=ctk.CTkFont(size=14),
            text_color=Colors.TEXT_SECONDARY
        )
        subtitle_label.pack()

        # 配置管理区域 - 可调透明度
        self.config_management_frame = ctk.CTkFrame(
            settings_container,
            corner_radius=15,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.config_management_frame.pack(fill="x", pady=(0, 15))

        config_title = ctk.CTkLabel(
            self.config_management_frame,
            text="📁 配置文件管理",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        config_title.pack(pady=(15, 10))

        # 按钮区域
        button_frame = ctk.CTkFrame(self.config_management_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=15, pady=(0, 15))

        # 保存配置按钮
        save_config_btn = ctk.CTkButton(
            button_frame,
            text="💾 保存当前配置",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            corner_radius=25,
            fg_color=Colors.PRIMARY_BLUE,
            hover_color=Colors.PRIMARY_BLUE_HOVER,
            command=self.save_max_config
        )
        save_config_btn.pack(fill="x", pady=(0, 10))

        # 读取配置按钮
        load_config_btn = ctk.CTkButton(
            button_frame,
            text="📂 读取配置文件",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            corner_radius=25,
            fg_color=Colors.SUCCESS_GREEN,
            hover_color=Colors.SUCCESS_GREEN_HOVER,
            command=self.load_max_config
        )
        load_config_btn.pack(fill="x", pady=(0, 10))

        # 重置配置按钮
        reset_config_btn = ctk.CTkButton(
            button_frame,
            text="🔄 重置为默认配置",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            corner_radius=25,
            fg_color=Colors.WARNING_ORANGE,
            hover_color=Colors.WARNING_ORANGE_HOVER,
            command=self.reset_max_config
        )
        reset_config_btn.pack(fill="x")

        # 配置状态显示区域 - 可调透明度
        self.config_status_frame = ctk.CTkFrame(
            settings_container,
            corner_radius=15,
            fg_color=self.component_transparency_levels[self.current_component_transparency],
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.config_status_frame.pack(fill="both", expand=True)

        status_title = ctk.CTkLabel(
            self.config_status_frame,
            text="📊 配置状态",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=Colors.TEXT_PRIMARY
        )
        status_title.pack(pady=(15, 10))

        # 配置状态文本框 - 支持动态透明度
        self.config_status_text = ctk.CTkTextbox(
            self.config_status_frame,
            height=150,
            corner_radius=12,
            font=ctk.CTkFont(size=12, family="Microsoft YaHei UI"),
            fg_color=self.get_text_bg_color(),
            text_color=("#1D1D1F", "#FFFFFF"),
            border_width=1,
            border_color=("gray75", "gray45")
        )
        self.config_status_text.pack(pady=(0, 15), padx=15, fill="both", expand=True)

        # 初始化配置状态显示
        self.update_config_status()

    def load_settings(self):
        """加载设置"""
        try:
            settings_path = get_resource_path('config/settings.yaml')
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = self.yaml.load(f)

            # 美化配置显示
            mod_status = "✅ 启用" if settings.get('plugin_enable', {}).get('mod', False) else "❌ 禁用"
            helper_status = "✅ 启用" if settings.get('plugin_enable', {}).get('helper', False) else "❌ 禁用"
            auto_update = "✅ 启用" if settings.get('liqi', {}).get('auto_update', False) else "❌ 禁用"
            liqi_version = settings.get('liqi', {}).get('liqi_version', '未知')

            config_text = f"""🎨 MOD功能: {mod_status}
🤖 Helper功能: {helper_status}
🔄 自动更新: {auto_update}
📦 Liqi版本: {liqi_version}
🌐 代理端口: 23410
📁 配置目录: config/

💡 点击"启动雀魂MAX"查看详细启动信息"""

            self.config_text.delete("1.0", tk.END)
            self.config_text.insert("1.0", config_text)

        except Exception as e:
            self.config_text.delete("1.0", tk.END)
            self.config_text.insert("1.0", f"❌ 配置加载失败: {str(e)}")

    def update_config_info_with_startup(self):
        """更新配置信息显示，包含启动日志"""
        try:
            settings_path = get_resource_path('config/settings.yaml')
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = self.yaml.load(f)

            # 美化配置显示
            mod_status = "✅ 启用" if settings.get('plugin_enable', {}).get('mod', False) else "❌ 禁用"
            helper_status = "✅ 启用" if settings.get('plugin_enable', {}).get('helper', False) else "❌ 禁用"
            auto_update = "✅ 启用" if settings.get('liqi', {}).get('auto_update', False) else "❌ 禁用"
            liqi_version = settings.get('liqi', {}).get('liqi_version', '未知')



            config_text = f"""🎨 MOD功能: {mod_status}
🤖 Helper功能: {helper_status}
🔄 自动更新: {auto_update}
📦 Liqi版本: {liqi_version}
🌐 代理端口: 23410
📁 配置目录: config/

📊 服务状态: {'🟢 运行中' if self.is_running else '🔴 未启动'}

⚠️  注意: 点击"启动雀魂MAX"按钮后，真实的启动日志将显示在右侧的"助手输出"区域。"""

            self.config_text.delete("1.0", tk.END)
            self.config_text.insert("1.0", config_text)

        except Exception as e:
            logger.error(f"❌ 更新配置信息失败: {e}")

    def update_final_config_info(self):
        """更新最终的配置信息显示"""
        try:
            settings_path = get_resource_path('config/settings.yaml')
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = self.yaml.load(f)

            # 美化配置显示
            mod_status = "✅ 启用" if settings.get('plugin_enable', {}).get('mod', False) else "❌ 禁用"
            helper_status = "✅ 启用" if settings.get('plugin_enable', {}).get('helper', False) else "❌ 禁用"
            auto_update = "✅ 启用" if settings.get('liqi', {}).get('auto_update', False) else "❌ 禁用"
            liqi_version = settings.get('liqi', {}).get('liqi_version', '未知')



            config_text = f"""🎨 MOD功能: {mod_status}
🤖 Helper功能: {helper_status}
🔄 自动更新: {auto_update}
📦 Liqi版本: {liqi_version}
🌐 代理端口: 23410
📁 配置目录: config/

📊 当前服务状态: {'🟢 运行中' if self.is_running else '🔴 未启动'}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
雀魂果茶大助手        设计者：Nfilmjon
基于原作者Avenshy的思路进行独立设计和优化

本工具完全免费、开源，仅供学习交流使用
请在下载后24小时内删除，不得用于商业用途

独立版特性：
- 优化了代码结构和性能
- 简化了配置流程
- 增强了稳定性和兼容性
- 保持与原版功能的完全兼容
- 现代化GUI界面设计

感谢原作者Avenshy的开源贡献！
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

⚠️  真实的启动日志将在点击"启动雀魂MAX"后显示在右侧的"助手输出"区域。
这里只显示配置信息，不是实际的服务运行状态。"""

            self.config_text.delete("1.0", tk.END)
            self.config_text.insert("1.0", config_text)

        except Exception as e:
            logger.error(f"❌ 更新最终配置信息失败: {e}")

    def open_log_folder(self):
        """打开日志文件夹"""
        try:
            log_path = os.path.abspath(get_resource_path("log"))
            if os.path.exists(log_path):
                os.startfile(log_path)
            else:
                messagebox.showinfo("提示", "日志文件夹不存在")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志文件夹:\n{str(e)}")

    def toggle_service(self):
        """切换雀魂MAX服务状态"""
        if not self.is_running:
            self.start_service()
        else:
            self.stop_service()

    def toggle_helper(self):
        """切换麻将助手状态"""
        if not self.helper_running:
            self.start_helper()
        else:
            self.stop_helper()

    def start_service(self):
        """启动雀魂MAX服务"""
        try:
            logger.info("🚀 开始启动雀魂MAX服务...")

            self.is_running = True
            self.start_button.configure(
                text="🛑 停止雀魂MAX",
                fg_color=Colors.ERROR_RED,
                hover_color=Colors.ERROR_RED_HOVER
            )
            self.status_label.configure(
                text="雀魂MAX：正在启动...",
                text_color=Colors.WARNING_ORANGE
            )

            # 在新线程中启动异步服务
            def run_mitm():
                try:
                    # 动态导入addons模块
                    import addons
                    logger.info("📦 addons模块导入成功")

                    # 真正启动MITM服务
                    logger.info("🔄 开始启动MITM代理服务...")

                    # 将真实的启动输出发送到助手输出区域
                    self.helper_output_queue.put(f"[{time.strftime('%H:%M:%S')}] 🚀 开始启动雀魂MAX服务...\n")
                    self.helper_output_queue.put(f"[{time.strftime('%H:%M:%S')}] 📦 正在加载addons模块...\n")

                    # 重定向标准输出到我们的队列
                    import io
                    import contextlib

                    @contextlib.contextmanager
                    def capture_output():
                        old_stdout = sys.stdout
                        old_stderr = sys.stderr
                        stdout_capture = io.StringIO()
                        stderr_capture = io.StringIO()
                        try:
                            sys.stdout = stdout_capture
                            sys.stderr = stderr_capture
                            yield stdout_capture, stderr_capture
                        finally:
                            sys.stdout = old_stdout
                            sys.stderr = old_stderr

                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 捕获addons的输出并显示到GUI
                    with capture_output() as (stdout_capture, stderr_capture):
                        loop.run_until_complete(addons.start_mitm())

                    # 将捕获的输出发送到GUI
                    captured_output = stdout_capture.getvalue()
                    if captured_output:
                        for line in captured_output.split('\n'):
                            if line.strip():
                                self.helper_output_queue.put(f"[{time.strftime('%H:%M:%S')}] {line}\n")

                    captured_errors = stderr_capture.getvalue()
                    if captured_errors:
                        for line in captured_errors.split('\n'):
                            if line.strip():
                                self.helper_output_queue.put(f"[{time.strftime('%H:%M:%S')}] ❌ {line}\n")

                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"❌ 雀魂MAX服务启动失败: {error_msg}")
                    self.root.after(0, lambda msg=error_msg: self.handle_service_error(msg))

            self.mitm_thread = threading.Thread(target=run_mitm, daemon=True)
            self.mitm_thread.start()

            # 更新状态 - 使用动画效果
            self.root.after(2000, lambda: self.pulse_status_update(
                self.status_label,
                "雀魂MAX：✅ 运行中 (端口: 23410)",
                Colors.SUCCESS_GREEN
            ))

            # 真实的启动状态将通过addons模块的输出显示

            logger.info("✅ 雀魂MAX服务启动请求已发送")

        except Exception as e:
            logger.error(f"❌ 启动雀魂MAX服务时出错: {e}")
            self.handle_service_error(str(e))

    def stop_service(self):
        """停止雀魂MAX服务"""
        self.is_running = False
        self.start_button.configure(
            text="🚀 启动雀魂MAX",
            fg_color=Colors.PRIMARY_BLUE,
            hover_color=Colors.PRIMARY_BLUE_HOVER
        )
        self.status_label.configure(
            text="雀魂MAX：❌ 已停止",
            text_color=Colors.ERROR_RED
        )

        messagebox.showinfo("提示", "雀魂MAX服务已停止\n注意：可能需要重启程序才能完全停止所有进程")

    def handle_service_error(self, error_msg):
        """处理雀魂MAX服务错误"""
        self.is_running = False
        self.start_button.configure(
            text="🚀 启动雀魂MAX",
            fg_color=Colors.PRIMARY_BLUE,
            hover_color=Colors.PRIMARY_BLUE_HOVER
        )
        self.status_label.configure(
            text="雀魂MAX：❌ 启动失败",
            text_color=Colors.ERROR_RED
        )
        messagebox.showerror("错误", f"雀魂MAX服务启动失败:\n{error_msg}")

    def start_helper(self):
        """启动麻将助手"""
        try:
            helper_path = "./mahjong-helper.exe"
            if not os.path.exists(helper_path):
                messagebox.showerror("错误", f"找不到麻将助手程序:\n{helper_path}\n\n请确保文件存在或运行 build_helper.bat 编译程序")
                return

            self.helper_running = True
            self.helper_button.configure(
                text="🛑 停止助手",
                fg_color=Colors.ERROR_RED,
                hover_color=Colors.ERROR_RED_HOVER
            )
            self.helper_status_label.configure(
                text="麻将助手：正在启动...",
                text_color=Colors.WARNING_ORANGE
            )

            if self.helper_embedded:
                # 嵌入模式：捕获输出到GUI
                self.helper_process = subprocess.Popen(
                    [helper_path, "-majsoul", "-interactive"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    stdin=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    text=False,
                    bufsize=1
                )

                # 启动输出读取线程
                self.helper_reader_thread = threading.Thread(
                    target=self.read_helper_output,
                    args=(self.helper_process,),
                    daemon=True
                )
                self.helper_reader_thread.start()

                # 在输出区域显示启动信息
                self.helper_output_text.configure(state="normal")
                self.helper_output_text.delete("1.0", tk.END)
                self.helper_output_text.insert("1.0", f"[{time.strftime('%H:%M:%S')}] 🎯 麻将助手启动中...\n")
                self.helper_output_text.insert(tk.END, f"[{time.strftime('%H:%M:%S')}] 模式：嵌入式雀魂助手\n")
                self.helper_output_text.insert(tk.END, f"[{time.strftime('%H:%M:%S')}] 等待雀魂对局数据...\n\n")
                self.helper_output_text.configure(state="disabled")

            else:
                # 独立窗口模式
                self.helper_process = subprocess.Popen(
                    [helper_path, "-majsoul"],
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )

            # 更新状态 - 使用动画效果
            self.root.after(1000, lambda: self.pulse_status_update(
                self.helper_status_label,
                "麻将助手：✅ 运行中",
                Colors.SUCCESS_GREEN
            ))

        except Exception as e:
            self.handle_helper_error(str(e))

    def stop_helper(self):
        """停止麻将助手"""
        try:
            if self.helper_process:
                self.helper_process.terminate()
                self.helper_process = None

            # 停止输出读取线程
            if self.helper_reader_thread and self.helper_reader_thread.is_alive():
                self.helper_reader_thread = None

            self.helper_running = False
            self.helper_button.configure(
                text="🎯 启动麻将助手",
                fg_color=Colors.SUCCESS_GREEN,
                hover_color=Colors.SUCCESS_GREEN_HOVER
            )
            self.helper_status_label.configure(
                text="麻将助手：❌ 已停止",
                text_color=Colors.ERROR_RED
            )

            # 在嵌入模式下显示停止信息
            if self.helper_embedded:
                self.helper_output_text.configure(state="normal")
                self.helper_output_text.insert(tk.END, f"\n[{time.strftime('%H:%M:%S')}] 🛑 麻将助手已停止\n")
                self.helper_output_text.see(tk.END)
                self.helper_output_text.configure(state="disabled")
            else:
                messagebox.showinfo("提示", "麻将助手已停止")

        except Exception as e:
            messagebox.showerror("错误", f"停止麻将助手失败:\n{str(e)}")

    def handle_helper_error(self, error_msg):
        """处理麻将助手错误"""
        self.helper_running = False
        self.helper_button.configure(
            text="🎯 启动麻将助手",
            fg_color=Colors.SUCCESS_GREEN,
            hover_color=Colors.SUCCESS_GREEN_HOVER
        )
        self.helper_status_label.configure(
            text="麻将助手：❌ 启动失败",
            text_color=Colors.ERROR_RED
        )
        messagebox.showerror("错误", f"麻将助手启动失败:\n{error_msg}")

    def toggle_embedded_mode(self):
        """切换嵌入模式"""
        self.helper_embedded = self.embedded_mode_switch.get()
        if self.helper_embedded:
            self.helper_output_text.configure(state="normal")
            self.helper_output_text.delete("1.0", tk.END)
            self.helper_output_text.insert("1.0", "已切换到嵌入模式\n助手输出将显示在此区域\n")
            self.helper_output_text.configure(state="disabled")
        else:
            self.helper_output_text.configure(state="normal")
            self.helper_output_text.delete("1.0", tk.END)
            self.helper_output_text.insert("1.0", "已切换到独立窗口模式\n助手将在独立窗口中运行\n")
            self.helper_output_text.configure(state="disabled")

    def clear_helper_output(self):
        """清空助手输出"""
        self.helper_output_text.configure(state="normal")
        self.helper_output_text.delete("1.0", tk.END)
        self.helper_output_text.insert("1.0", "输出已清空\n")
        self.helper_output_text.configure(state="disabled")

    def update_helper_output(self):
        """更新助手输出显示"""
        try:
            while not self.helper_output_queue.empty():
                output_line = self.helper_output_queue.get_nowait()
                self.helper_output_text.configure(state="normal")
                self.helper_output_text.insert(tk.END, output_line)
                self.helper_output_text.see(tk.END)  # 自动滚动到底部
                self.helper_output_text.configure(state="disabled")
        except queue.Empty:
            pass

        # 每100ms检查一次新输出
        self.root.after(100, self.update_helper_output)

    def read_helper_output(self, process):
        """在后台线程中读取助手输出"""
        try:
            startup_logged = False
            while process.poll() is None:
                line = process.stdout.readline()
                if line:
                    # 使用更好的编码处理，支持中文和特殊字符
                    try:
                        decoded_line = line.decode('utf-8', errors='replace').strip()
                    except:
                        try:
                            decoded_line = line.decode('gbk', errors='replace').strip()
                        except:
                            decoded_line = str(line, errors='replace').strip()

                    if decoded_line:  # 只处理非空行
                        timestamp = time.strftime("[%H:%M:%S] ")

                        # 特殊处理启动信息
                        if not startup_logged and ("日本麻将助手" in decoded_line or "mahjong helper" in decoded_line.lower()):
                            self.helper_output_queue.put(f"{timestamp}🎯 {decoded_line}\n")
                            self.helper_output_queue.put(f"{timestamp}✅ 助手启动成功，等待对局数据...\n\n")
                            startup_logged = True
                            logger.info("🎯 麻将助手已成功启动")
                        else:
                            # 智能格式化输出
                            formatted_line = self.format_helper_output(decoded_line)
                            self.helper_output_queue.put(f"{timestamp}{formatted_line}\n")
                else:
                    time.sleep(0.1)

            # 进程结束时的处理
            if process.poll() is not None:
                timestamp = time.strftime("[%H:%M:%S] ")
                self.helper_output_queue.put(f"\n{timestamp}� 助手进程已结束\n")
                logger.info("🛑 麻将助手进程已结束")

        except Exception as e:
            timestamp = time.strftime("[%H:%M:%S] ")
            error_msg = f"{timestamp}❌ 读取助手输出时出错: {str(e)}\n"
            self.helper_output_queue.put(error_msg)
            logger.error(f"❌ 读取助手输出时出错: {e}")

    def format_helper_output(self, text):
        """格式化助手输出文本"""
        try:
            # 移除可能的控制字符
            text = ''.join(char for char in text if ord(char) >= 32 or char in '\t\n\r')

            # 智能添加图标和格式化
            if "危险度" in text or "危险" in text:
                return f"⚠️  {text}"
            elif "推荐" in text or "建议" in text or "最佳" in text:
                return f"💡 {text}"
            elif "听牌" in text or "和牌" in text:
                return f"🎯 {text}"
            elif "得点" in text or "分数" in text or "点数" in text:
                return f"📊 {text}"
            elif "立直" in text or "リーチ" in text:
                return f"🔥 {text}"
            elif "役" in text and ("满" in text or "倍" in text):
                return f"🏆 {text}"
            elif "切" in text or "打" in text:
                return f"🎴 {text}"
            elif "摸" in text or "抽" in text:
                return f"🎲 {text}"
            elif "错误" in text or "失败" in text or "error" in text.lower():
                return f"❌ {text}"
            elif "成功" in text or "完成" in text or "success" in text.lower():
                return f"✅ {text}"
            elif "警告" in text or "注意" in text or "warning" in text.lower():
                return f"⚠️  {text}"
            elif "信息" in text or "info" in text.lower():
                return f"ℹ️  {text}"
            else:
                return f"📝 {text}"

        except Exception as e:
            logger.error(f"❌ 格式化助手输出失败: {e}")
            return text

    def restart_service(self):
        """重启雀魂MAX服务"""
        if self.is_running:
            self.stop_service()
            # 延迟2秒后重新启动
            self.root.after(2000, self.start_service)
        else:
            self.start_service()

    def save_max_config(self):
        """保存MAX插件配置"""
        try:
            config_dir = get_resource_path("config")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 获取当前时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(config_dir, f"settings_backup_{timestamp}.yaml")

            # 备份当前配置
            settings_file = get_resource_path("config/settings.yaml")
            if os.path.exists(settings_file):
                import shutil
                shutil.copy2(settings_file, backup_file)
                logger.info(f"📁 配置已备份到: {backup_file}")

            # 弹出文件保存对话框
            file_path = filedialog.asksaveasfilename(
                title="保存MAX插件配置",
                defaultextension=".yaml",
                filetypes=[("YAML文件", "*.yaml"), ("所有文件", "*.*")],
                initialdir=config_dir,
                initialname=f"majsoul_max_config_{timestamp}.yaml"
            )

            if file_path:
                if os.path.exists(settings_file):
                    import shutil
                    shutil.copy2(settings_file, file_path)
                    logger.info(f"💾 配置已保存到: {file_path}")
                    messagebox.showinfo("成功", f"配置已成功保存到:\n{file_path}")
                    self.update_config_status()
                else:
                    messagebox.showerror("错误", "找不到当前配置文件")

        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败:\n{str(e)}")

    def load_max_config(self):
        """读取MAX插件配置"""
        try:
            # 弹出文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择MAX插件配置文件",
                filetypes=[("YAML文件", "*.yaml"), ("所有文件", "*.*")],
                initialdir=get_resource_path("config")
            )

            if file_path:
                # 备份当前配置
                settings_file = get_resource_path("config/settings.yaml")
                if os.path.exists(settings_file):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    config_dir = get_resource_path("config")
                    backup_file = os.path.join(config_dir, f"settings_backup_{timestamp}.yaml")
                    import shutil
                    shutil.copy2(settings_file, backup_file)
                    logger.info(f"📁 当前配置已备份到: {backup_file}")

                # 复制选择的配置文件
                import shutil
                shutil.copy2(file_path, settings_file)
                logger.info(f"📂 配置已从 {file_path} 加载")

                # 重新加载设置
                self.load_settings()
                self.update_config_status()

                messagebox.showinfo("成功", f"配置已成功加载:\n{file_path}\n\n请重启程序以应用新配置")

        except Exception as e:
            logger.error(f"❌ 读取配置失败: {e}")
            messagebox.showerror("错误", f"读取配置失败:\n{str(e)}")

    def reset_max_config(self):
        """重置MAX插件配置为默认"""
        try:
            result = messagebox.askyesno(
                "确认重置",
                "确定要重置为默认配置吗？\n\n当前配置将被备份，但所有自定义设置将丢失。"
            )

            if result:
                # 备份当前配置
                settings_file = get_resource_path("config/settings.yaml")
                if os.path.exists(settings_file):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    config_dir = get_resource_path("config")
                    backup_file = os.path.join(config_dir, f"settings_backup_{timestamp}.yaml")
                    import shutil
                    shutil.copy2(settings_file, backup_file)
                    logger.info(f"📁 当前配置已备份到: {backup_file}")

                # 创建默认配置
                default_config = {
                    'plugin_enable': {
                        'mod': True,
                        'helper': True
                    },
                    'liqi': {
                        'auto_update': True,
                        'liqi_version': 'latest'
                    }
                }

                # 保存默认配置
                with open(settings_file, 'w', encoding='utf-8') as f:
                    self.yaml.dump(default_config, f)

                logger.info("🔄 配置已重置为默认值")

                # 重新加载设置
                self.load_settings()
                self.update_config_status()

                messagebox.showinfo("成功", "配置已重置为默认值\n\n请重启程序以应用新配置")

        except Exception as e:
            logger.error(f"❌ 重置配置失败: {e}")
            messagebox.showerror("错误", f"重置配置失败:\n{str(e)}")

    # 控制逻辑：背景增强/主题/背景切换/全屏
    def _on_blur_change(self, value):
        try:
            self.bg_blur_level = float(value)
            self.refresh_background()
        except Exception as e:
            logger.debug(f"模糊调整失败: {e}")

    def _on_brightness_change(self, value):
        try:
            self.bg_brightness = float(value)
            self.refresh_background()
        except Exception as e:
            logger.debug(f"亮度调整失败: {e}")

    def toggle_theme(self, val=None):
        # 允许按钮传入“亮/暗”，未传入则切换
        try:
            if val in ("亮", "暗"):
                self.appearance_mode = "light" if val == "亮" else "dark"
            else:
                self.appearance_mode = "dark" if self.appearance_mode == "light" else "light"
            ctk.set_appearance_mode(self.appearance_mode)
            # 主题影响对比度，刷新背景自适应文字
            self.refresh_background()
        except Exception as e:
            logger.debug(f"主题切换失败: {e}")

    def change_background_image(self):
        try:
            file_path = filedialog.askopenfilename(
                title="选择背景图片",
                filetypes=[("图片文件", "*.png;*.jpg;*.jpeg;*.bmp;*.webp"), ("所有文件", "*.*")],
                initialdir=os.path.dirname(self.bg_image_path) if self.bg_image_path else os.getcwd()
            )
            if file_path:
                self.bg_image_path = file_path
                self._bg_original_image = Image.open(self.bg_image_path)
                self._render_background()
        except Exception as e:
            logger.error(f"更换背景失败: {e}")

    def toggle_fullscreen(self):
        try:
            current = bool(self.root.attributes('-fullscreen'))
            self.root.attributes('-fullscreen', not current)
        except Exception:
            # Windows下fallback
            try:
                if not hasattr(self, '_fs_old_geom'):
                    self._fs_old_geom = self.root.geometry()
                    self.root.attributes('-topmost', True)
                    self.root.state('zoomed')
                else:
                    self.root.attributes('-topmost', False)
                    self.root.geometry(self._fs_old_geom)
                    self._fs_old_geom = None
            except Exception as e:
                logger.debug(f"全屏切换失败: {e}")


    def update_config_status(self):
        """更新配置状态显示"""
        try:
            if hasattr(self, 'config_status_text'):
                status_text = f"""📊 MAX插件配置状态

📁 配置文件路径: config/settings.yaml
📅 最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔧 当前配置:"""

                # 读取当前配置
                settings_file = get_resource_path('config/settings.yaml')
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = self.yaml.load(f)

                    mod_status = "✅ 启用" if settings.get('plugin_enable', {}).get('mod', False) else "❌ 禁用"
                    helper_status = "✅ 启用" if settings.get('plugin_enable', {}).get('helper', False) else "❌ 禁用"
                    auto_update = "✅ 启用" if settings.get('liqi', {}).get('auto_update', False) else "❌ 禁用"
                    liqi_version = settings.get('liqi', {}).get('liqi_version', '未知')

                    status_text += f"""
  🎨 MOD功能: {mod_status}
  🤖 Helper功能: {helper_status}
  🔄 自动更新: {auto_update}
  📦 Liqi版本: {liqi_version}

💡 提示:
  • 修改配置后需要重启程序生效
  • 建议在修改前先保存当前配置
  • 配置文件使用YAML格式"""
                else:
                    status_text += "\n❌ 配置文件不存在"

                self.config_status_text.delete("1.0", tk.END)
                self.config_status_text.insert("1.0", status_text)

        except Exception as e:
            logger.error(f"❌ 更新配置状态失败: {e}")

    def open_settings(self):
        """打开设置窗口"""
        # 切换到设置选项卡
        self.tabview.set("⚙️ 设置")
        self.update_config_status()

    def welcome_animation(self):
        """启动时的欢迎动画"""
        # 初始时隐藏主要元素
        self.root.attributes('-alpha', 0.0)

        # 渐显动画
        def fade_in(alpha=0.0):
            if alpha < 1.0:
                alpha += 0.05
                self.root.attributes('-alpha', alpha)
                self.root.after(30, lambda: fade_in(alpha))
            else:
                self.root.attributes('-alpha', 1.0)

        # 延迟启动动画
        self.root.after(100, fade_in)

    def animate_button_click(self, button, original_text, temp_text, duration=1000):
        """按钮点击动画效果"""
        if self.animation_running:
            return

        self.animation_running = True
        button.configure(text=temp_text)

        # 恢复原始文本
        def restore_text():
            button.configure(text=original_text)
            self.animation_running = False

        self.root.after(duration, restore_text)

    def pulse_status_update(self, label, text, color, pulses=3):
        """状态更新时的脉冲动画"""
        original_font = label.cget("font")

        def pulse(count=0):
            if count < pulses:
                # 放大
                label.configure(
                    text=text,
                    text_color=color,
                    font=ctk.CTkFont(size=17, weight="bold")
                )
                self.root.after(150, lambda: shrink(count))
            else:
                label.configure(font=original_font)

        def shrink(count):
            # 缩小
            label.configure(font=ctk.CTkFont(size=16, weight="bold"))
            self.root.after(150, lambda: pulse(count + 1))

        pulse()

    def run(self):
        """运行GUI"""
        self.root.mainloop()



def main():
    """主函数"""
    try:
        # 确保必要目录存在
        os.makedirs(get_resource_path('config'), exist_ok=True)
        os.makedirs(get_resource_path('log'), exist_ok=True)

        logger.info("=" * 60)
        logger.info("🎮 雀魂MAX独立版 - 集成助手版")
        logger.info("设计者：Nfilmjon (小约)")
        logger.info("版本：v2.0 - 嵌入式助手版")
        logger.info("=" * 60)
        logger.info("🚀 正在启动GUI界面...")

        # 启动GUI
        app = MajsoulMaxGUI()
        logger.info("🎉 GUI界面启动完成，等待用户操作...")
        app.run()

    except Exception as e:
        logger.error(f"❌ 程序启动失败: {e}")
        messagebox.showerror("启动失败", f"程序启动时发生错误:\n{str(e)}")
    finally:
        logger.info("👋 程序已退出")

if __name__ == "__main__":
    main()

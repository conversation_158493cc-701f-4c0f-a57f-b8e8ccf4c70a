#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试启动器，用于诊断问题
"""

import http.server
import socketserver
import webbrowser
import threading
import time
from pathlib import Path

# 导入嵌入的静态文件
try:
    from embedded_static import create_temp_static_dir, cleanup_temp_dir
    USE_EMBEDDED = True
    print("✅ 使用嵌入的静态文件")
except ImportError:
    USE_EMBEDDED = False
    print("❌ 未找到嵌入的静态文件，使用本地文件")

def start_server():
    port = 8000
    
    try:
        # 获取静态文件目录
        if USE_EMBEDDED:
            static_dir = create_temp_static_dir()
            print(f"✅ 创建临时目录: {static_dir}")
        else:
            static_dir = Path(__file__).parent / "static"
            print(f"✅ 使用本地目录: {static_dir}")
        
        # 检查目录和文件
        if not static_dir.exists():
            print(f"❌ 目录不存在: {static_dir}")
            return
        
        index_file = static_dir / "index.html"
        if not index_file.exists():
            print(f"❌ index.html不存在: {index_file}")
            return
        
        print(f"✅ 找到index.html: {index_file}")
        
        # 列出目录内容
        files = list(static_dir.glob("*"))
        print(f"✅ 目录内容: {[f.name for f in files]}")
        
        # 创建服务器
        handler_cls = lambda *args, **kwargs: http.server.SimpleHTTPRequestHandler(
            *args, directory=str(static_dir), **kwargs
        )
        
        socketserver.ThreadingTCPServer.allow_reuse_address = True
        server = socketserver.ThreadingTCPServer(("127.0.0.1", port), handler_cls)
        
        print(f"✅ 服务器启动: http://127.0.0.1:{port}")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f"http://127.0.0.1:{port}")
        
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 启动服务器
        server.serve_forever()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🀄 日本麻将教学网站 - 测试启动器")
    print("=" * 50)
    start_server()

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: liqi.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nliqi.proto\x12\x02lq\"c\n\x13NotifyRoomGameStart\x12\x10\n\x08game_url\x18\x01 \x01(\t\x12\x15\n\rconnect_token\x18\x02 \x01(\t\x12\x11\n\tgame_uuid\x18\x03 \x01(\t\x12\x10\n\x08location\x18\x04 \x01(\t\"{\n\x14NotifyMatchGameStart\x12\x10\n\x08game_url\x18\x01 \x01(\t\x12\x15\n\rconnect_token\x18\x02 \x01(\t\x12\x11\n\tgame_uuid\x18\x03 \x01(\t\x12\x15\n\rmatch_mode_id\x18\x04 \x01(\r\x12\x10\n\x08location\x18\x05 \x01(\t\"\xc2\x01\n\x15NotifyRoomPlayerReady\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05ready\x18\x02 \x01(\x08\x12\x41\n\x0c\x61\x63\x63ount_list\x18\x03 \x01(\x0b\x32+.lq.NotifyRoomPlayerReady.AccountReadyState\x12\x0b\n\x03seq\x18\x04 \x01(\r\x1a\x36\n\x11\x41\x63\x63ountReadyState\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05ready\x18\x02 \x01(\x08\"\xd4\x01\n\x18NotifyRoomPlayerDressing\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08\x64ressing\x18\x02 \x01(\x08\x12G\n\x0c\x61\x63\x63ount_list\x18\x03 \x01(\x0b\x32\x31.lq.NotifyRoomPlayerDressing.AccountDressingState\x12\x0b\n\x03seq\x18\x04 \x01(\r\x1a<\n\x14\x41\x63\x63ountDressingState\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08\x64ressing\x18\x02 \x01(\x08\"\xac\x01\n\x16NotifyRoomPlayerUpdate\x12\x10\n\x08owner_id\x18\x03 \x01(\r\x12\x13\n\x0brobot_count\x18\x04 \x01(\r\x12\'\n\x0bplayer_list\x18\x05 \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x0b\n\x03seq\x18\x06 \x01(\r\x12\"\n\x06robots\x18\x07 \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x11\n\tpositions\x18\x08 \x03(\r\"\x13\n\x11NotifyRoomKickOut\"Z\n\x17NotifyFriendStateChange\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12,\n\x0c\x61\x63tive_state\x18\x02 \x01(\x0b\x32\x16.lq.AccountActiveState\"M\n\x16NotifyFriendViewChange\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12 \n\x04\x62\x61se\x18\x02 \x01(\x0b\x32\x12.lq.PlayerBaseView\"R\n\x12NotifyFriendChange\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x1a\n\x06\x66riend\x18\x03 \x01(\x0b\x32\n.lq.Friend\"R\n\x14NotifyNewFriendApply\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x12\n\napply_time\x18\x02 \x01(\r\x12\x12\n\nremoved_id\x18\x03 \x01(\r\"X\n\x13NotifyClientMessage\x12\"\n\x06sender\x18\x01 \x01(\x0b\x32\x12.lq.PlayerBaseView\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\"8\n\x13NotifyAccountUpdate\x12!\n\x06update\x18\x01 \x01(\x0b\x32\x11.lq.AccountUpdate\"\x14\n\x12NotifyAnotherLogin\"\x15\n\x13NotifyAccountLogout\"\x96\x01\n\x18NotifyAnnouncementUpdate\x12\x44\n\x0bupdate_list\x18\x01 \x03(\x0b\x32/.lq.NotifyAnnouncementUpdate.AnnouncementUpdate\x1a\x34\n\x12\x41nnouncementUpdate\x12\x0c\n\x04lang\x18\x01 \x01(\t\x12\x10\n\x08platform\x18\x02 \x01(\t\"\'\n\rNotifyNewMail\x12\x16\n\x04mail\x18\x01 \x01(\x0b\x32\x08.lq.Mail\"(\n\x10NotifyDeleteMail\x12\x14\n\x0cmail_id_list\x18\x01 \x03(\r\",\n\x16NotifyReviveCoinUpdate\x12\x12\n\nhas_gained\x18\x01 \x01(\x08\"r\n\x15NotifyDailyTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x1c\n\x14max_daily_task_count\x18\x02 \x01(\r\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\"@\n\x18NotifyActivityTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\"F\n\x1eNotifyActivityPeriodTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\"E\n\x1dNotifyAccountRandomTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\"N\n\x1fNotifyActivitySegmentTaskUpdate\x12+\n\nprogresses\x18\x01 \x03(\x0b\x32\x17.lq.SegmentTaskProgress\"\xbb\x04\n\x14NotifyActivityUpdate\x12\x37\n\x04list\x18\x01 \x03(\x0b\x32).lq.NotifyActivityUpdate.FeedActivityData\x1a\xe9\x03\n\x10\x46\x65\x65\x64\x41\x63tivityData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\nfeed_count\x18\x02 \x01(\r\x12X\n\x13\x66riend_receive_data\x18\x03 \x01(\x0b\x32;.lq.NotifyActivityUpdate.FeedActivityData.CountWithTimeData\x12U\n\x10\x66riend_send_data\x18\x04 \x01(\x0b\x32;.lq.NotifyActivityUpdate.FeedActivityData.CountWithTimeData\x12I\n\ngift_inbox\x18\x05 \x03(\x0b\x32\x35.lq.NotifyActivityUpdate.FeedActivityData.GiftBoxData\x1a<\n\x11\x43ountWithTimeData\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x18\n\x10last_update_time\x18\x02 \x01(\r\x1ar\n\x0bGiftBoxData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\x17\n\x0f\x66rom_account_id\x18\x04 \x01(\r\x12\x0c\n\x04time\x18\x05 \x01(\r\x12\x10\n\x08received\x18\x06 \x01(\r\"\xaf\x01\n NotifyAccountChallengeTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\x12\r\n\x05level\x18\x02 \x01(\r\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\x12\x13\n\x0bmatch_count\x18\x04 \x01(\r\x12\x11\n\tticket_id\x18\x05 \x01(\r\x12\x17\n\x0frewarded_season\x18\x06 \x03(\r\"\x12\n\x10NotifyNewComment\"\x15\n\x13NotifyRollingNotice\"\x16\n\x14NotifyMaintainNotice\"\x17\n\x15NotifyGiftSendRefresh\"3\n\x10NotifyShopUpdate\x12\x1f\n\tshop_info\x18\x01 \x01(\x0b\x32\x0c.lq.ShopInfo\"\x16\n\x14NotifyIntervalUpdate\"\xb7\x01\n\x14NotifyVipLevelChange\x12\x12\n\ngift_limit\x18\x01 \x01(\r\x12\x18\n\x10\x66riend_max_count\x18\x02 \x01(\r\x12\x1e\n\x16zhp_free_refresh_limit\x18\x03 \x01(\r\x12\x1e\n\x16zhp_cost_refresh_limit\x18\x04 \x01(\r\x12\x13\n\x0b\x62uddy_bonus\x18\x05 \x01(\x02\x12\x1c\n\x14record_collect_limit\x18\x06 \x01(\r\";\n\x13NotifyServerSetting\x12$\n\x08settings\x18\x01 \x01(\x0b\x32\x12.lq.ServerSettings\"\xdc\x01\n\x0fNotifyPayResult\x12\x12\n\npay_result\x18\x01 \x01(\r\x12\x10\n\x08order_id\x18\x02 \x01(\t\x12\x10\n\x08goods_id\x18\x03 \x01(\r\x12\x18\n\x10new_month_ticket\x18\x04 \x01(\r\x12;\n\x0fresource_modify\x18\x05 \x03(\x0b\x32\".lq.NotifyPayResult.ResourceModify\x1a:\n\x0eResourceModify\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\r\n\x05\x66inal\x18\x03 \x01(\r\"y\n\x1dNotifyCustomContestAccountMsg\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x0e\n\x06sender\x18\x03 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\x12\x10\n\x08verified\x18\x05 \x01(\r\"\xb1\x01\n\x1cNotifyCustomContestSystemMsg\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0c\n\x04uuid\x18\x03 \x01(\t\x12\x32\n\ngame_start\x18\x04 \x01(\x0b\x32\x1e.lq.CustomizedContestGameStart\x12.\n\x08game_end\x18\x05 \x01(\x0b\x32\x1c.lq.CustomizedContestGameEnd\"!\n\x12NotifyMatchTimeout\x12\x0b\n\x03sid\x18\x01 \x01(\t\" \n\x11NotifyMatchFailed\x12\x0b\n\x03sid\x18\x01 \x01(\t\"<\n\x18NotifyCustomContestState\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\r\n\x05state\x18\x02 \x01(\r\"T\n\x14NotifyActivityChange\x12$\n\x0enew_activities\x18\x01 \x03(\x0b\x32\x0c.lq.Activity\x12\x16\n\x0e\x65nd_activities\x18\x02 \x03(\r\"H\n\x0fNotifyAFKResult\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x14\n\x0c\x62\x61n_end_time\x18\x02 \x01(\r\x12\x11\n\tgame_uuid\x18\x03 \x01(\t\"\x1a\n\x18NotifyLoginQueueFinished\"\xb8\x05\n\x18NotifyGameFinishRewardV2\x12\x0f\n\x07mode_id\x18\x01 \x01(\r\x12>\n\x0clevel_change\x18\x02 \x01(\x0b\x32(.lq.NotifyGameFinishRewardV2.LevelChange\x12<\n\x0bmatch_chest\x18\x03 \x01(\x0b\x32\'.lq.NotifyGameFinishRewardV2.MatchChest\x12\x42\n\x0emain_character\x18\x04 \x01(\x0b\x32*.lq.NotifyGameFinishRewardV2.MainCharacter\x12\x42\n\x0e\x63haracter_gift\x18\x05 \x01(\x0b\x32*.lq.NotifyGameFinishRewardV2.CharacterGift\x12(\n\x06\x62\x61\x64ges\x18\x06 \x03(\x0b\x32\x18.lq.BadgeAchieveProgress\x1a^\n\x0bLevelChange\x12 \n\x06origin\x18\x01 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x1f\n\x05\x66inal\x18\x02 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x0c\n\x04type\x18\x03 \x01(\r\x1aq\n\nMatchChest\x12\x10\n\x08\x63hest_id\x18\x01 \x01(\r\x12\x0e\n\x06origin\x18\x02 \x01(\r\x12\r\n\x05\x66inal\x18\x03 \x01(\r\x12\x11\n\tis_graded\x18\x04 \x01(\x08\x12\x1f\n\x07rewards\x18\x05 \x03(\x0b\x32\x0e.lq.RewardSlot\x1a\x38\n\rMainCharacter\x12\r\n\x05level\x18\x01 \x01(\r\x12\x0b\n\x03\x65xp\x18\x02 \x01(\r\x12\x0b\n\x03\x61\x64\x64\x18\x03 \x01(\r\x1aN\n\rCharacterGift\x12\x0e\n\x06origin\x18\x01 \x01(\r\x12\r\n\x05\x66inal\x18\x02 \x01(\r\x12\x0b\n\x03\x61\x64\x64\x18\x03 \x01(\r\x12\x11\n\tis_graded\x18\x04 \x01(\x08\"\xa4\x01\n\x16NotifyActivityRewardV2\x12\x42\n\x0f\x61\x63tivity_reward\x18\x01 \x03(\x0b\x32).lq.NotifyActivityRewardV2.ActivityReward\x1a\x46\n\x0e\x41\x63tivityReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x1f\n\x07rewards\x18\x02 \x03(\x0b\x32\x0e.lq.RewardSlot\"\x8e\x01\n\x15NotifyActivityPointV2\x12@\n\x0f\x61\x63tivity_points\x18\x01 \x03(\x0b\x32\'.lq.NotifyActivityPointV2.ActivityPoint\x1a\x33\n\rActivityPoint\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\"\xa0\x01\n\x18NotifyLeaderboardPointV2\x12I\n\x12leaderboard_points\x18\x01 \x03(\x0b\x32-.lq.NotifyLeaderboardPointV2.LeaderboardPoint\x1a\x39\n\x10LeaderboardPoint\x12\x16\n\x0eleaderboard_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\"1\n\x10NotifySeerReport\x12\x1d\n\x06report\x18\x01 \x01(\x0b\x32\r.lq.SeerBrief\"<\n\x18NotifyConnectionShutdown\x12\x0e\n\x06reason\x18\x01 \x01(\r\x12\x10\n\x08\x63lose_at\x18\x02 \x01(\r\"Q\n\x05\x45rror\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x12\n\nu32_params\x18\x02 \x03(\r\x12\x12\n\nstr_params\x18\x03 \x03(\t\x12\x12\n\njson_param\x18\x04 \x01(\t\"%\n\x07Wrapper\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\"@\n\x0fNetworkEndpoint\x12\x0e\n\x06\x66\x61mily\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\x12\x0c\n\x04port\x18\x03 \x01(\r\"\x0b\n\tReqCommon\"%\n\tResCommon\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"O\n\x10ResAccountUpdate\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12!\n\x06update\x18\x02 \x01(\x0b\x32\x11.lq.AccountUpdate\"(\n\rAntiAddiction\x12\x17\n\x0fonline_duration\x18\x01 \x01(\r\"\x7f\n\x0fHighestHuRecord\x12\x0e\n\x06\x66\x61nshu\x18\x01 \x01(\r\x12\x0f\n\x07\x64oranum\x18\x02 \x01(\r\x12\r\n\x05title\x18\x03 \x01(\t\x12\r\n\x05hands\x18\x04 \x03(\t\x12\x0c\n\x04ming\x18\x05 \x03(\t\x12\r\n\x05hupai\x18\x06 \x01(\t\x12\x10\n\x08title_id\x18\x07 \x01(\r\"\xb6\x06\n\x17\x41\x63\x63ountMahjongStatistic\x12\x1d\n\x15\x66inal_position_counts\x18\x01 \x03(\r\x12>\n\x0crecent_round\x18\x02 \x01(\x0b\x32(.lq.AccountMahjongStatistic.RoundSummary\x12\x38\n\trecent_hu\x18\x03 \x01(\x0b\x32%.lq.AccountMahjongStatistic.HuSummary\x12\'\n\nhighest_hu\x18\x04 \x01(\x0b\x32\x13.lq.HighestHuRecord\x12G\n\x14recent_20_hu_summary\x18\x06 \x01(\x0b\x32).lq.AccountMahjongStatistic.Liqi20Summary\x12G\n\x14recent_10_hu_summary\x18\x07 \x01(\x0b\x32).lq.AccountMahjongStatistic.LiQi10Summary\x12\x45\n\x15recent_10_game_result\x18\x08 \x03(\x0b\x32&.lq.AccountMahjongStatistic.GameResult\x1a\x64\n\x0cRoundSummary\x12\x13\n\x0btotal_count\x18\x01 \x01(\r\x12\x12\n\nrong_count\x18\x02 \x01(\r\x12\x12\n\nzimo_count\x18\x03 \x01(\r\x12\x17\n\x0f\x66\x61ngchong_count\x18\x04 \x01(\r\x1aM\n\tHuSummary\x12\x13\n\x0btotal_count\x18\x01 \x01(\r\x12\x18\n\x10\x64ora_round_count\x18\x02 \x01(\r\x12\x11\n\ttotal_fan\x18\x03 \x01(\r\x1aZ\n\rLiqi20Summary\x12\x13\n\x0btotal_count\x18\x01 \x01(\r\x12\x1a\n\x12total_lidora_count\x18\x02 \x01(\r\x12\x18\n\x10\x61verage_hu_point\x18\x03 \x01(\r\x1a>\n\rLiQi10Summary\x12\x17\n\x0ftotal_xuanshang\x18\x01 \x01(\r\x12\x14\n\x0ctotal_fanshu\x18\x02 \x01(\r\x1a/\n\nGameResult\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x13\n\x0b\x66inal_point\x18\x02 \x01(\x05\"\x8a\x01\n\x14\x41\x63\x63ountStatisticData\x12\x18\n\x10mahjong_category\x18\x01 \x01(\r\x12\x15\n\rgame_category\x18\x02 \x01(\r\x12.\n\tstatistic\x18\x03 \x01(\x0b\x32\x1b.lq.AccountMahjongStatistic\x12\x11\n\tgame_type\x18\x04 \x01(\r\")\n\x0c\x41\x63\x63ountLevel\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\r\"M\n\x08ViewSlot\x12\x0c\n\x04slot\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\x12\x14\n\x0citem_id_list\x18\x04 \x03(\r\"[\n\nFavoriteHu\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x1f\n\x02hu\x18\x03 \x01(\x0b\x32\x13.lq.HighestHuRecord\x12\x0c\n\x04mode\x18\x04 \x01(\r\"\xbf\x08\n\x07\x41\x63\x63ount\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x12\n\nlogin_time\x18\x03 \x01(\r\x12\x13\n\x0blogout_time\x18\x04 \x01(\r\x12\x0f\n\x07room_id\x18\x05 \x01(\r\x12)\n\x0e\x61nti_addiction\x18\x06 \x01(\x0b\x32\x11.lq.AntiAddiction\x12\r\n\x05title\x18\x07 \x01(\r\x12\x11\n\tsignature\x18\x08 \x01(\t\x12\r\n\x05\x65mail\x18\t \x01(\t\x12\x14\n\x0c\x65mail_verify\x18\n \x01(\r\x12\x0c\n\x04gold\x18\x0b \x01(\r\x12\x0f\n\x07\x64iamond\x18\x0c \x01(\r\x12\x11\n\tavatar_id\x18\r \x01(\r\x12\x0b\n\x03vip\x18\x0e \x01(\r\x12\x10\n\x08\x62irthday\x18\x0f \x01(\x05\x12\r\n\x05phone\x18\x10 \x01(\t\x12\x14\n\x0cphone_verify\x18\x11 \x01(\r\x12\x35\n\x10platform_diamond\x18\x12 \x03(\x0b\x32\x1b.lq.Account.PlatformDiamond\x12\x1f\n\x05level\x18\x15 \x01(\x0b\x32\x10.lq.AccountLevel\x12 \n\x06level3\x18\x16 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x14\n\x0c\x61vatar_frame\x18\x17 \x01(\r\x12\x13\n\x0bskin_ticket\x18\x18 \x01(\r\x12<\n\x14platform_skin_ticket\x18\x19 \x03(\x0b\x32\x1e.lq.Account.PlatformSkinTicket\x12\x10\n\x08verified\x18\x1a \x01(\r\x12\x34\n\x10\x63hallenge_levels\x18\x1b \x03(\x0b\x32\x1a.lq.Account.ChallengeLevel\x12\x14\n\x0c\x66rozen_state\x18\x1d \x01(\r\x12\x37\n\x11\x61\x63hievement_count\x18\x1c \x03(\x0b\x32\x1c.lq.Account.AchievementCount\x12\x15\n\rloading_image\x18\x1e \x03(\r\x12#\n\x0b\x66\x61vorite_hu\x18\" \x03(\x0b\x32\x0e.lq.FavoriteHu\x12!\n\x06\x62\x61\x64ges\x18# \x03(\x0b\x32\x11.lq.Account.Badge\x1a,\n\x0fPlatformDiamond\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x1a/\n\x12PlatformSkinTicket\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x1a=\n\x0e\x43hallengeLevel\x12\x0e\n\x06season\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0c\n\x04rank\x18\x03 \x01(\r\x1a/\n\x10\x41\x63hievementCount\x12\x0c\n\x04rare\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x1a\x44\n\x05\x42\x61\x64ge\x12\n\n\x02id\x18\x01 \x01(\r\x12\x15\n\rachieved_time\x18\x02 \x01(\r\x12\x18\n\x10\x61\x63hieved_counter\x18\x03 \x01(\r\"-\n\x10\x41\x63\x63ountOwnerData\x12\x19\n\x11unlock_characters\x18\x01 \x03(\r\"\x85\x11\n\rAccountUpdate\x12\x34\n\tnumerical\x18\x01 \x03(\x0b\x32!.lq.AccountUpdate.NumericalUpdate\x12\x34\n\tcharacter\x18\x02 \x01(\x0b\x32!.lq.AccountUpdate.CharacterUpdate\x12\x1a\n\x03\x62\x61g\x18\x03 \x01(\x0b\x32\r.lq.BagUpdate\x12\x38\n\x0b\x61\x63hievement\x18\x04 \x01(\x0b\x32#.lq.AccountUpdate.AchievementUpdate\x12#\n\x07shilian\x18\x05 \x01(\x0b\x32\x12.lq.AccountShiLian\x12\x35\n\ndaily_task\x18\x06 \x01(\x0b\x32!.lq.AccountUpdate.DailyTaskUpdate\x12,\n\x05title\x18\x07 \x01(\x0b\x32\x1d.lq.AccountUpdate.TitleUpdate\x12\x1a\n\x12new_recharged_list\x18\x08 \x03(\r\x12\x33\n\ractivity_task\x18\t \x01(\x0b\x32\x1c.lq.AccountUpdate.TaskUpdate\x12\x38\n\x12\x61\x63tivity_flip_task\x18\n \x01(\x0b\x32\x1c.lq.AccountUpdate.TaskUpdate\x12:\n\x14\x61\x63tivity_period_task\x18\x0b \x01(\x0b\x32\x1c.lq.AccountUpdate.TaskUpdate\x12:\n\x14\x61\x63tivity_random_task\x18\x0c \x01(\x0b\x32\x1c.lq.AccountUpdate.TaskUpdate\x12;\n\tchallenge\x18\r \x01(\x0b\x32(.lq.AccountUpdate.AccountChallengeUpdate\x12\x38\n\x08\x61\x62_match\x18\x0e \x01(\x0b\x32&.lq.AccountUpdate.AccountABMatchUpdate\x12+\n\x08\x61\x63tivity\x18\x0f \x01(\x0b\x32\x19.lq.AccountActivityUpdate\x12\x42\n\x15\x61\x63tivity_segment_task\x18\x10 \x01(\x0b\x32#.lq.AccountUpdate.SegmentTaskUpdate\x12\x39\n\x0cmonth_ticket\x18\x11 \x01(\x0b\x32#.lq.AccountUpdate.MonthTicketUpdate\x12=\n\x0emain_character\x18\x12 \x01(\x0b\x32%.lq.AccountUpdate.MainCharacterUpdate\x12,\n\x05\x62\x61\x64ge\x18\x13 \x01(\x0b\x32\x1d.lq.AccountUpdate.BadgeUpdate\x1a,\n\x0fNumericalUpdate\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x66inal\x18\x03 \x01(\r\x1aw\n\x0f\x43haracterUpdate\x12!\n\ncharacters\x18\x02 \x03(\x0b\x32\r.lq.Character\x12\r\n\x05skins\x18\x03 \x03(\r\x12\x18\n\x10\x66inished_endings\x18\x04 \x03(\r\x12\x18\n\x10rewarded_endings\x18\x05 \x03(\r\x1aX\n\x11\x41\x63hievementUpdate\x12+\n\nprogresses\x18\x01 \x03(\x0b\x32\x17.lq.AchievementProgress\x12\x16\n\x0erewarded_group\x18\x02 \x03(\r\x1aJ\n\x0f\x44\x61ilyTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x11\n\ttask_list\x18\x02 \x03(\r\x1a\x38\n\x0bTitleUpdate\x12\x12\n\nnew_titles\x18\x01 \x03(\r\x12\x15\n\rremove_titles\x18\x02 \x03(\r\x1a\x45\n\nTaskUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x11\n\ttask_list\x18\x02 \x03(\r\x1a\xb8\x01\n\x16\x41\x63\x63ountChallengeUpdate\x12$\n\nprogresses\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\x12\r\n\x05level\x18\x02 \x01(\r\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\x12\x13\n\x0bmatch_count\x18\x04 \x01(\r\x12\x11\n\tticket_id\x18\x05 \x01(\r\x12\x11\n\ttask_list\x18\x06 \x03(\r\x12\x17\n\x0frewarded_season\x18\x07 \x03(\r\x1a\xfd\x01\n\x14\x41\x63\x63ountABMatchUpdate\x12\x10\n\x08match_id\x18\x01 \x01(\r\x12\x13\n\x0bmatch_count\x18\x02 \x01(\r\x12\x14\n\x0c\x62uy_in_count\x18\x03 \x01(\r\x12\r\n\x05point\x18\x04 \x01(\r\x12\x10\n\x08rewarded\x18\x05 \x01(\x08\x12J\n\x0fmatch_max_point\x18\x06 \x03(\x0b\x32\x31.lq.AccountUpdate.AccountABMatchUpdate.MatchPoint\x12\x0c\n\x04quit\x18\x07 \x01(\x08\x1a-\n\nMatchPoint\x12\x10\n\x08match_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\x1aS\n\x11SegmentTaskUpdate\x12+\n\nprogresses\x18\x01 \x03(\x0b\x32\x17.lq.SegmentTaskProgress\x12\x11\n\ttask_list\x18\x02 \x03(\r\x1a<\n\x11MonthTicketUpdate\x12\x10\n\x08\x65nd_time\x18\x01 \x01(\r\x12\x15\n\rlast_pay_time\x18\x02 \x01(\r\x1a<\n\x13MainCharacterUpdate\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12\x0f\n\x07skin_id\x18\x02 \x01(\r\x1a;\n\x0b\x42\x61\x64geUpdate\x12,\n\nprogresses\x18\x01 \x03(\x0b\x32\x18.lq.BadgeAchieveProgress\"E\n\x0cGameMetaData\x12\x0f\n\x07room_id\x18\x01 \x01(\r\x12\x0f\n\x07mode_id\x18\x02 \x01(\r\x12\x13\n\x0b\x63ontest_uid\x18\x03 \x01(\r\"Y\n\x12\x41\x63\x63ountPlayingGame\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\r\x12\x1e\n\x04meta\x18\x03 \x01(\x0b\x32\x10.lq.GameMetaData\"8\n\x0fRandomCharacter\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12\x0f\n\x07skin_id\x18\x02 \x01(\r\"\xa2\x03\n\x10\x41\x63\x63ountCacheView\x12\x15\n\rcache_version\x18\x01 \x01(\r\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x10\n\x08nickname\x18\x03 \x01(\t\x12\x12\n\nlogin_time\x18\x04 \x01(\r\x12\x13\n\x0blogout_time\x18\x05 \x01(\r\x12\x11\n\tis_online\x18\x06 \x01(\x08\x12\x0f\n\x07room_id\x18\x07 \x01(\r\x12\r\n\x05title\x18\x08 \x01(\r\x12\x11\n\tavatar_id\x18\t \x01(\r\x12\x0b\n\x03vip\x18\n \x01(\r\x12\x1f\n\x05level\x18\x0b \x01(\x0b\x32\x10.lq.AccountLevel\x12,\n\x0cplaying_game\x18\x0c \x01(\x0b\x32\x16.lq.AccountPlayingGame\x12 \n\x06level3\x18\r \x01(\x0b\x32\x10.lq.AccountLevel\x12\x14\n\x0c\x61vatar_frame\x18\x0e \x01(\r\x12\x10\n\x08verified\x18\x0f \x01(\r\x12\x14\n\x0c\x62\x61n_deadline\x18\x10 \x01(\r\x12\x13\n\x0b\x63omment_ban\x18\x11 \x01(\r\x12\x11\n\tban_state\x18\x12 \x01(\r\"\xd6\x01\n\x0ePlayerBaseView\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x11\n\tavatar_id\x18\x02 \x01(\r\x12\r\n\x05title\x18\x03 \x01(\r\x12\x10\n\x08nickname\x18\x04 \x01(\t\x12\x1f\n\x05level\x18\x05 \x01(\x0b\x32\x10.lq.AccountLevel\x12 \n\x06level3\x18\x06 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x14\n\x0c\x61vatar_frame\x18\x07 \x01(\r\x12\x10\n\x08verified\x18\x08 \x01(\r\x12\x11\n\tis_banned\x18\t \x01(\r\"\x82\x02\n\x0ePlayerGameView\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x11\n\tavatar_id\x18\x02 \x01(\r\x12\r\n\x05title\x18\x03 \x01(\r\x12\x10\n\x08nickname\x18\x04 \x01(\t\x12\x1f\n\x05level\x18\x05 \x01(\x0b\x32\x10.lq.AccountLevel\x12 \n\tcharacter\x18\x06 \x01(\x0b\x32\r.lq.Character\x12 \n\x06level3\x18\x07 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x14\n\x0c\x61vatar_frame\x18\x08 \x01(\r\x12\x10\n\x08verified\x18\t \x01(\r\x12\x1b\n\x05views\x18\n \x03(\x0b\x32\x0c.lq.ViewSlot\"#\n\x0bGameSetting\x12\x14\n\x0c\x65moji_switch\x18\x01 \x01(\r\"\xc4\x01\n\x08GameMode\x12\x0c\n\x04mode\x18\x01 \x01(\r\x12\n\n\x02\x61i\x18\x04 \x01(\x08\x12\x12\n\nextendinfo\x18\x05 \x01(\t\x12\'\n\x0b\x64\x65tail_rule\x18\x06 \x01(\x0b\x32\x12.lq.GameDetailRule\x12:\n\x13testing_environment\x18\x07 \x01(\x0b\x32\x1d.lq.GameTestingEnvironmentSet\x12%\n\x0cgame_setting\x18\x08 \x01(\x0b\x32\x0f.lq.GameSetting\"Y\n\x19GameTestingEnvironmentSet\x12\x0f\n\x07paixing\x18\x01 \x01(\r\x12\x12\n\nleft_count\x18\x02 \x01(\r\x12\x17\n\x0f\x66ield_spell_var\x18\x03 \x01(\r\"\xf5\x0c\n\x0eGameDetailRule\x12\x12\n\ntime_fixed\x18\x01 \x01(\r\x12\x10\n\x08time_add\x18\x02 \x01(\r\x12\x12\n\ndora_count\x18\x03 \x01(\r\x12\x0f\n\x07shiduan\x18\x04 \x01(\r\x12\x12\n\ninit_point\x18\x05 \x01(\r\x12\x0f\n\x07\x66\x61ndian\x18\x06 \x01(\r\x12\x11\n\tcan_jifei\x18\x07 \x01(\x08\x12\x16\n\x0etianbian_value\x18\x08 \x01(\r\x12\x16\n\x0eliqibang_value\x18\t \x01(\r\x12\x17\n\x0f\x63hangbang_value\x18\n \x01(\r\x12\x15\n\rnoting_fafu_1\x18\x0b \x01(\r\x12\x15\n\rnoting_fafu_2\x18\x0c \x01(\r\x12\x15\n\rnoting_fafu_3\x18\r \x01(\r\x12\x19\n\x11have_liujumanguan\x18\x0e \x01(\x08\x12\x1c\n\x14have_qieshangmanguan\x18\x0f \x01(\x08\x12\x16\n\x0ehave_biao_dora\x18\x10 \x01(\x08\x12\x1b\n\x13have_gang_biao_dora\x18\x11 \x01(\x08\x12\"\n\x1aming_dora_immediately_open\x18\x12 \x01(\x08\x12\x14\n\x0chave_li_dora\x18\x13 \x01(\x08\x12\x19\n\x11have_gang_li_dora\x18\x14 \x01(\x08\x12\x19\n\x11have_sifenglianda\x18\x15 \x01(\x08\x12\x18\n\x10have_sigangsanle\x18\x16 \x01(\x08\x12\x17\n\x0fhave_sijializhi\x18\x17 \x01(\x08\x12\x1b\n\x13have_jiuzhongjiupai\x18\x18 \x01(\x08\x12\x17\n\x0fhave_sanjiahele\x18\x19 \x01(\x08\x12\x14\n\x0chave_toutiao\x18\x1a \x01(\x08\x12\x1b\n\x13have_helelianzhuang\x18\x1b \x01(\x08\x12\x18\n\x10have_helezhongju\x18\x1c \x01(\x08\x12\x1e\n\x16have_tingpailianzhuang\x18\x1d \x01(\x08\x12\x1b\n\x13have_tingpaizhongju\x18\x1e \x01(\x08\x12\x11\n\thave_yifa\x18\x1f \x01(\x08\x12\x16\n\x0ehave_nanruxiru\x18  \x01(\x08\x12\x18\n\x10jingsuanyuandian\x18! \x01(\r\x12\x13\n\x0bshunweima_2\x18\" \x01(\x05\x12\x13\n\x0bshunweima_3\x18# \x01(\x05\x12\x13\n\x0bshunweima_4\x18$ \x01(\x05\x12\x14\n\x0c\x62ianjietishi\x18% \x01(\x08\x12\x10\n\x08\x61i_level\x18& \x01(\r\x12\x14\n\x0chave_zimosun\x18\' \x01(\x08\x12\x1d\n\x15\x64isable_multi_yukaman\x18( \x01(\x08\x12\r\n\x05\x66\x61nfu\x18) \x01(\r\x12\x11\n\tguyi_mode\x18* \x01(\r\x12\x12\n\ndora3_mode\x18+ \x01(\r\x12\x17\n\x0f\x62\x65gin_open_mode\x18, \x01(\r\x12\x14\n\x0cjiuchao_mode\x18- \x01(\r\x12\x11\n\tmuyu_mode\x18. \x01(\r\x12\x11\n\topen_hand\x18/ \x01(\r\x12\x14\n\x0cxuezhandaodi\x18\x30 \x01(\r\x12\x14\n\x0chuansanzhang\x18\x31 \x01(\r\x12\x0f\n\x07\x63huanma\x18\x32 \x01(\r\x12\x16\n\x0ereveal_discard\x18\x33 \x01(\r\x12\x18\n\x10\x66ield_spell_mode\x18\x34 \x01(\r\x12\x10\n\x08zhanxing\x18\x35 \x01(\r\x12\x15\n\rtianming_mode\x18\x36 \x01(\r\x12\x1a\n\x12\x64isable_leijiyiman\x18< \x01(\x08\x12\x1e\n\x16\x64isable_double_yakuman\x18> \x01(\r\x12!\n\x19\x64isable_composite_yakuman\x18? \x01(\r\x12\x14\n\x0c\x65nable_shiti\x18@ \x01(\r\x12\x1c\n\x14\x65nable_nontsumo_liqi\x18\x41 \x01(\r\x12#\n\x1b\x64isable_double_wind_four_fu\x18\x42 \x01(\r\x12\x1d\n\x15\x64isable_angang_guoshi\x18\x43 \x01(\r\x12\x14\n\x0c\x65nable_renhe\x18\x44 \x01(\r\x12%\n\x1d\x65nable_baopai_extend_settings\x18\x45 \x01(\r\x12\x16\n\x0eyongchang_mode\x18\x46 \x01(\r\x12\x17\n\x0fhunzhiyiji_mode\x18G \x01(\r\x12\x1b\n\x13wanxiangxiuluo_mode\x18H \x01(\r\x12\x1b\n\x13\x62\x65ishuizhizhan_mode\x18I \x01(\r\"\xc3\x02\n\x04Room\x12\x0f\n\x07room_id\x18\x01 \x01(\r\x12\x10\n\x08owner_id\x18\x02 \x01(\r\x12\x1a\n\x04mode\x18\x03 \x01(\x0b\x32\x0c.lq.GameMode\x12\x18\n\x10max_player_count\x18\x04 \x01(\r\x12#\n\x07persons\x18\x05 \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x12\n\nready_list\x18\x06 \x03(\r\x12\x12\n\nis_playing\x18\x07 \x01(\x08\x12\x13\n\x0bpublic_live\x18\x08 \x01(\x08\x12\x13\n\x0brobot_count\x18\t \x01(\r\x12\x15\n\rtournament_id\x18\n \x01(\r\x12\x0b\n\x03seq\x18\x0b \x01(\r\x12\x10\n\x08pre_rule\x18\x0c \x01(\t\x12\"\n\x06robots\x18\r \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x11\n\tpositions\x18\x0e \x03(\r\"\xc1\x01\n\rGameEndResult\x12-\n\x07players\x18\x01 \x03(\x0b\x32\x1c.lq.GameEndResult.PlayerItem\x1a\x80\x01\n\nPlayerItem\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x13\n\x0btotal_point\x18\x02 \x01(\x05\x12\x14\n\x0cpart_point_1\x18\x03 \x01(\x05\x12\x14\n\x0cpart_point_2\x18\x04 \x01(\x05\x12\x15\n\rgrading_score\x18\x05 \x01(\x05\x12\x0c\n\x04gold\x18\x06 \x01(\x05\"M\n\x0fGameConnectInfo\x12\x15\n\rconnect_token\x18\x02 \x01(\t\x12\x11\n\tgame_uuid\x18\x03 \x01(\t\x12\x10\n\x08location\x18\x04 \x01(\t\"0\n\x0eItemGainRecord\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"d\n\x0fItemGainRecords\x12\x13\n\x0brecord_time\x18\x01 \x01(\r\x12\x17\n\x0flimit_source_id\x18\x02 \x01(\r\x12#\n\x07records\x18\x03 \x03(\x0b\x32\x12.lq.ItemGainRecord\"g\n\x11\x46\x61keRandomRecords\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\x17\n\x0fspecial_item_id\x18\x02 \x01(\r\x12\x12\n\ngain_count\x18\x03 \x01(\r\x12\x14\n\x0cgain_history\x18\x04 \x03(\r\"&\n\x04Item\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\r\n\x05stack\x18\x02 \x01(\r\"N\n\x03\x42\x61g\x12\x17\n\x05items\x18\x01 \x03(\x0b\x32\x08.lq.Item\x12.\n\x11\x64\x61ily_gain_record\x18\x02 \x03(\x0b\x32\x13.lq.ItemGainRecords\"b\n\tBagUpdate\x12\x1e\n\x0cupdate_items\x18\x01 \x03(\x0b\x32\x08.lq.Item\x12\x35\n\x18update_daily_gain_record\x18\x02 \x03(\x0b\x32\x13.lq.ItemGainRecords\"\'\n\nRewardSlot\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"M\n\nOpenResult\x12\x1e\n\x06reward\x18\x01 \x01(\x0b\x32\x0e.lq.RewardSlot\x12\x1f\n\x07replace\x18\x02 \x01(\x0b\x32\x0e.lq.RewardSlot\"\x97\x01\n\x10RewardPlusResult\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12/\n\x08\x65xchange\x18\x03 \x01(\x0b\x32\x1d.lq.RewardPlusResult.Exchange\x1a\x37\n\x08\x45xchange\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x10\n\x08\x65xchange\x18\x03 \x01(\r\"g\n\rExecuteReward\x12\x1e\n\x06reward\x18\x01 \x01(\x0b\x32\x0e.lq.RewardSlot\x12\x1f\n\x07replace\x18\x02 \x01(\x0b\x32\x0e.lq.RewardSlot\x12\x15\n\rreplace_count\x18\x03 \x01(\r\"*\n\rExecuteResult\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\",\n\x0bI18nContext\x12\x0c\n\x04lang\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontext\x18\x02 \x01(\t\"\xa5\x02\n\x04Mail\x12\x0f\n\x07mail_id\x18\x01 \x01(\r\x12\r\n\x05state\x18\x02 \x01(\r\x12\x17\n\x0ftake_attachment\x18\x03 \x01(\x08\x12\r\n\x05title\x18\x04 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x05 \x01(\t\x12#\n\x0b\x61ttachments\x18\x06 \x03(\x0b\x32\x0e.lq.RewardSlot\x12\x13\n\x0b\x63reate_time\x18\x07 \x01(\r\x12\x13\n\x0b\x65xpire_time\x18\x08 \x01(\r\x12\x14\n\x0creference_id\x18\t \x01(\r\x12#\n\ntitle_i18n\x18\n \x03(\x0b\x32\x0f.lq.I18nContext\x12%\n\x0c\x63ontent_i18n\x18\x0b \x03(\x0b\x32\x0f.lq.I18nContext\x12\x13\n\x0btemplate_id\x18\x0c \x01(\r\"m\n\x13\x41\x63hievementProgress\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07\x63ounter\x18\x02 \x01(\r\x12\x10\n\x08\x61\x63hieved\x18\x03 \x01(\x08\x12\x10\n\x08rewarded\x18\x04 \x01(\x08\x12\x15\n\rachieved_time\x18\x05 \x01(\r\"d\n\x14\x42\x61\x64geAchieveProgress\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07\x63ounter\x18\x02 \x01(\r\x12\x18\n\x10\x61\x63hieved_counter\x18\x03 \x01(\r\x12\x15\n\rachieved_time\x18\x04 \x01(\r\"\x97\x04\n\x1a\x41\x63\x63ountStatisticByGameMode\x12\x0c\n\x04mode\x18\x01 \x01(\r\x12\x16\n\x0egame_count_sum\x18\x02 \x01(\r\x12\x1b\n\x13game_final_position\x18\x03 \x03(\r\x12\x11\n\tfly_count\x18\x04 \x01(\r\x12\x15\n\rgold_earn_sum\x18\x05 \x01(\x02\x12\x17\n\x0fround_count_sum\x18\x06 \x01(\r\x12\x12\n\ndadian_sum\x18\x07 \x01(\x02\x12>\n\tround_end\x18\x08 \x03(\x0b\x32+.lq.AccountStatisticByGameMode.RoundEndData\x12\x16\n\x0eming_count_sum\x18\t \x01(\r\x12\x16\n\x0eliqi_count_sum\x18\n \x01(\r\x12\x15\n\rxun_count_sum\x18\x0b \x01(\r\x12\x1a\n\x12highest_lianzhuang\x18\x0c \x01(\r\x12\x16\n\x0escore_earn_sum\x18\r \x01(\r\x12<\n\nrank_score\x18\x0e \x03(\x0b\x32(.lq.AccountStatisticByGameMode.RankScore\x1a)\n\x0cRoundEndData\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0b\n\x03sum\x18\x02 \x01(\r\x1a;\n\tRankScore\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x11\n\tscore_sum\x18\x02 \x01(\x05\x12\r\n\x05\x63ount\x18\x03 \x01(\r\"4\n\x15\x41\x63\x63ountStatisticByFan\x12\x0e\n\x06\x66\x61n_id\x18\x01 \x01(\r\x12\x0b\n\x03sum\x18\x02 \x01(\r\"l\n\x12\x41\x63\x63ountFanAchieved\x12\x18\n\x10mahjong_category\x18\x01 \x01(\r\x12&\n\x03\x66\x61n\x18\x02 \x03(\x0b\x32\x19.lq.AccountStatisticByFan\x12\x14\n\x0cliujumanguan\x18\x03 \x01(\r\"\xb7\x01\n\x16\x41\x63\x63ountDetailStatistic\x12\x31\n\tgame_mode\x18\x01 \x03(\x0b\x32\x1e.lq.AccountStatisticByGameMode\x12&\n\x03\x66\x61n\x18\x02 \x03(\x0b\x32\x19.lq.AccountStatisticByFan\x12\x14\n\x0cliujumanguan\x18\x03 \x01(\r\x12,\n\x0c\x66\x61n_achieved\x18\x04 \x03(\x0b\x32\x16.lq.AccountFanAchieved\"j\n AccountDetailStatisticByCategory\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\r\x12\x34\n\x10\x64\x65tail_statistic\x18\x02 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\"\xd1\n\n\x18\x41\x63\x63ountDetailStatisticV2\x12\x39\n\x15\x66riend_room_statistic\x18\x01 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12\x42\n\x0erank_statistic\x18\x02 \x01(\x0b\x32*.lq.AccountDetailStatisticV2.RankStatistic\x12]\n\x1c\x63ustomized_contest_statistic\x18\x03 \x01(\x0b\x32\x37.lq.AccountDetailStatisticV2.CustomizedContestStatistic\x12;\n\x17leisure_match_statistic\x18\x04 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12R\n\x19\x63hallenge_match_statistic\x18\x05 \x01(\x0b\x32/.lq.AccountDetailStatisticV2.ChallengeStatistic\x12<\n\x18\x61\x63tivity_match_statistic\x18\x06 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12\x36\n\x12\x61\x62_match_statistic\x18\x07 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x1a\xbd\x03\n\rRankStatistic\x12L\n\x0ftotal_statistic\x18\x01 \x01(\x0b\x32\x33.lq.AccountDetailStatisticV2.RankStatistic.RankData\x12L\n\x0fmonth_statistic\x18\x02 \x01(\x0b\x32\x33.lq.AccountDetailStatisticV2.RankStatistic.RankData\x12\x1a\n\x12month_refresh_time\x18\x03 \x01(\r\x1a\xf3\x01\n\x08RankData\x12\x37\n\x13\x61ll_level_statistic\x18\x01 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12Z\n\x0flevel_data_list\x18\x02 \x03(\x0b\x32\x41.lq.AccountDetailStatisticV2.RankStatistic.RankData.RankLevelData\x1aR\n\rRankLevelData\x12\x12\n\nrank_level\x18\x01 \x01(\r\x12-\n\tstatistic\x18\x02 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x1a\xa2\x01\n\x1a\x43ustomizedContestStatistic\x12\x33\n\x0ftotal_statistic\x18\x01 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12\x33\n\x0fmonth_statistic\x18\x02 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12\x1a\n\x12month_refresh_time\x18\x03 \x01(\r\x1a\xea\x01\n\x12\x43hallengeStatistic\x12.\n\nall_season\x18\x01 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\x12T\n\x10season_data_list\x18\x02 \x03(\x0b\x32:.lq.AccountDetailStatisticV2.ChallengeStatistic.SeasonData\x1aN\n\nSeasonData\x12\x11\n\tseason_id\x18\x01 \x01(\r\x12-\n\tstatistic\x18\x02 \x01(\x0b\x32\x1a.lq.AccountDetailStatistic\"-\n\x0e\x41\x63\x63ountShiLian\x12\x0c\n\x04step\x18\x01 \x01(\r\x12\r\n\x05state\x18\x02 \x01(\r\"\x98\x02\n\x10\x43lientDeviceInfo\x12\x10\n\x08platform\x18\x01 \x01(\t\x12\x10\n\x08hardware\x18\x02 \x01(\t\x12\n\n\x02os\x18\x03 \x01(\t\x12\x12\n\nos_version\x18\x04 \x01(\t\x12\x12\n\nis_browser\x18\x05 \x01(\x08\x12\x10\n\x08software\x18\x06 \x01(\t\x12\x15\n\rsale_platform\x18\x07 \x01(\t\x12\x17\n\x0fhardware_vendor\x18\x08 \x01(\t\x12\x14\n\x0cmodel_number\x18\t \x01(\t\x12\x14\n\x0cscreen_width\x18\n \x01(\r\x12\x15\n\rscreen_height\x18\x0b \x01(\r\x12\x12\n\nuser_agent\x18\x0c \x01(\t\x12\x13\n\x0bscreen_type\x18\r \x01(\r\"6\n\x11\x43lientVersionInfo\x12\x10\n\x08resource\x18\x01 \x01(\t\x12\x0f\n\x07package\x18\x02 \x01(\t\"P\n\x0c\x41nnouncement\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x14\n\x0cheader_image\x18\x04 \x01(\t\"v\n\x0cTaskProgress\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07\x63ounter\x18\x02 \x01(\r\x12\x10\n\x08\x61\x63hieved\x18\x03 \x01(\x08\x12\x10\n\x08rewarded\x18\x04 \x01(\x08\x12\x0e\n\x06\x66\x61iled\x18\x05 \x01(\x08\x12\x15\n\rrewarded_time\x18\x06 \x01(\r\"Z\n\nGameConfig\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\r\x12\x1a\n\x04mode\x18\x02 \x01(\x0b\x32\x0c.lq.GameMode\x12\x1e\n\x04meta\x18\x03 \x01(\x0b\x32\x10.lq.GameMetaData\"P\n\x08RPGState\x12\x16\n\x0eplayer_damaged\x18\x01 \x01(\r\x12\x17\n\x0fmonster_damaged\x18\x02 \x01(\r\x12\x13\n\x0bmonster_seq\x18\x03 \x01(\r\"\xd0\x01\n\x0bRPGActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x16\n\x0elast_show_uuid\x18\x05 \x01(\t\x12\x18\n\x10last_played_uuid\x18\x06 \x01(\t\x12#\n\rcurrent_state\x18\x07 \x01(\x0b\x32\x0c.lq.RPGState\x12%\n\x0flast_show_state\x18\x08 \x01(\x0b\x32\x0c.lq.RPGState\x12\x18\n\x10received_rewards\x18\t \x03(\r\x12\x14\n\x0clast_show_id\x18\n \x01(\r\"\xc8\x01\n\x11\x41\x63tivityArenaData\x12\x11\n\twin_count\x18\x01 \x01(\r\x12\x12\n\nlose_count\x18\x02 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x03 \x01(\r\x12\x12\n\nenter_time\x18\x04 \x01(\r\x12\x19\n\x11\x64\x61ily_enter_count\x18\x05 \x01(\r\x12\x18\n\x10\x64\x61ily_enter_time\x18\x06 \x01(\r\x12\x15\n\rmax_win_count\x18\x07 \x01(\r\x12\x17\n\x0ftotal_win_count\x18\x08 \x01(\r\"\xaa\x03\n\x10\x46\x65\x65\x64\x41\x63tivityData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\nfeed_count\x18\x02 \x01(\r\x12\x43\n\x13\x66riend_receive_data\x18\x03 \x01(\x0b\x32&.lq.FeedActivityData.CountWithTimeData\x12@\n\x10\x66riend_send_data\x18\x04 \x01(\x0b\x32&.lq.FeedActivityData.CountWithTimeData\x12\x34\n\ngift_inbox\x18\x05 \x03(\x0b\x32 .lq.FeedActivityData.GiftBoxData\x1a<\n\x11\x43ountWithTimeData\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x18\n\x10last_update_time\x18\x02 \x01(\r\x1ar\n\x0bGiftBoxData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\x17\n\x0f\x66rom_account_id\x18\x04 \x01(\r\x12\x0c\n\x04time\x18\x05 \x01(\r\x12\x10\n\x08received\x18\x06 \x01(\r\"\x94\x01\n\x13SegmentTaskProgress\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07\x63ounter\x18\x02 \x01(\r\x12\x10\n\x08\x61\x63hieved\x18\x03 \x01(\x08\x12\x10\n\x08rewarded\x18\x04 \x01(\x08\x12\x0e\n\x06\x66\x61iled\x18\x05 \x01(\x08\x12\x14\n\x0creward_count\x18\x06 \x01(\r\x12\x16\n\x0e\x61\x63hieved_count\x18\x07 \x01(\r\"Y\n\x10MineActivityData\x12\x1c\n\tdig_point\x18\x01 \x03(\x0b\x32\t.lq.Point\x12\x1b\n\x03map\x18\x02 \x03(\x0b\x32\x0e.lq.MineReward\x12\n\n\x02id\x18\x03 \x01(\r\"\xd1\x05\n\x15\x41\x63\x63ountActivityUpdate\x12\'\n\tmine_data\x18\x01 \x03(\x0b\x32\x14.lq.MineActivityData\x12!\n\x08rpg_data\x18\x02 \x03(\x0b\x32\x0f.lq.RPGActivity\x12\'\n\tfeed_data\x18\x03 \x03(\x0b\x32\x14.lq.ActivityFeedData\x12\'\n\tspot_data\x18\x04 \x03(\x0b\x32\x14.lq.ActivitySpotData\x12\x34\n\x10\x66riend_gift_data\x18\x05 \x03(\x0b\x32\x1a.lq.ActivityFriendGiftData\x12-\n\x0cupgrade_data\x18\x06 \x03(\x0b\x32\x17.lq.ActivityUpgradeData\x12/\n\ngacha_data\x18\x07 \x03(\x0b\x32\x1b.lq.ActivityGachaUpdateData\x12\x33\n\x0fsimulation_data\x18\x08 \x03(\x0b\x32\x1a.lq.ActivitySimulationData\x12\x33\n\x0e\x63ombining_data\x18\t \x03(\x0b\x32\x1b.lq.ActivityCombiningLQData\x12-\n\x0cvillage_data\x18\n \x03(\x0b\x32\x17.lq.ActivityVillageData\x12/\n\rfestival_data\x18\x0b \x03(\x0b\x32\x18.lq.ActivityFestivalData\x12+\n\x0bisland_data\x18\x0c \x03(\x0b\x32\x16.lq.ActivityIslandData\x12)\n\nstory_data\x18\x0e \x03(\x0b\x32\x15.lq.ActivityStoryData\x12\x30\n\x0e\x63hoose_up_data\x18\x0f \x03(\x0b\x32\x18.lq.ActivityChooseUpData\x12\x30\n\x12simulation_v2_data\x18\x10 \x03(\x0b\x32\x14.lq.SimulationV2Data\";\n\x1a\x41\x63tivityCombiningWorkbench\x12\x10\n\x08\x63raft_id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x01(\r\"\xde\x01\n\x19\x41\x63tivityCombiningMenuData\x12\x12\n\nmenu_group\x18\x01 \x01(\r\x12<\n\tgenerated\x18\x02 \x03(\x0b\x32).lq.ActivityCombiningMenuData.MenuRequire\x12\x42\n\x0fmulti_generated\x18\x03 \x03(\x0b\x32).lq.ActivityCombiningMenuData.MenuRequire\x1a+\n\x0bMenuRequire\x12\r\n\x05level\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\x87\x01\n\x1a\x41\x63tivityCombiningOrderData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x01(\r\x12\x12\n\nunlock_day\x18\x04 \x01(\r\x12\x0f\n\x07\x63har_id\x18\x05 \x01(\r\x12\x19\n\x11\x66inished_craft_id\x18\x06 \x03(\r\x12\x10\n\x08\x63raft_id\x18\x07 \x03(\r\"\xf9\x01\n\x17\x41\x63tivityCombiningLQData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x31\n\tworkbench\x18\x02 \x03(\x0b\x32\x1e.lq.ActivityCombiningWorkbench\x12.\n\x06orders\x18\x03 \x03(\x0b\x32\x1e.lq.ActivityCombiningOrderData\x12\x33\n\x0brecycle_bin\x18\x04 \x01(\x0b\x32\x1e.lq.ActivityCombiningWorkbench\x12\x16\n\x0eunlocked_craft\x18\x05 \x03(\r\x12\x19\n\x11\x64\x61ily_bonus_count\x18\x06 \x01(\r\"9\n\x19\x41\x63tivityCombiningPoolData\x12\r\n\x05group\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\xee\x03\n\x15\x41\x63tivityCombiningData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x31\n\tworkbench\x18\x02 \x03(\x0b\x32\x1e.lq.ActivityCombiningWorkbench\x12.\n\x06orders\x18\x03 \x03(\x0b\x32\x1e.lq.ActivityCombiningOrderData\x12\x33\n\x0brecycle_bin\x18\x04 \x01(\x0b\x32\x1e.lq.ActivityCombiningWorkbench\x12+\n\x04menu\x18\x05 \x01(\x0b\x32\x1d.lq.ActivityCombiningMenuData\x12\x18\n\x10\x63urrent_order_id\x18\x06 \x01(\r\x12\x32\n\x05\x62onus\x18\x07 \x01(\x0b\x32#.lq.ActivityCombiningData.BonusData\x12\x16\n\x0eunlocked_craft\x18\x08 \x03(\r\x12\x31\n\ncraft_pool\x18\t \x03(\x0b\x32\x1d.lq.ActivityCombiningPoolData\x12\x31\n\norder_pool\x18\n \x03(\x0b\x32\x1d.lq.ActivityCombiningPoolData\x1a/\n\tBonusData\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x13\n\x0bupdate_time\x18\x02 \x01(\r\"*\n\rVillageReward\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"U\n\x13VillageBuildingData\x12\n\n\x02id\x18\x01 \x01(\r\x12!\n\x06reward\x18\x03 \x03(\x0b\x32\x11.lq.VillageReward\x12\x0f\n\x07workers\x18\x04 \x03(\r\"\x8e\x01\n\x0fVillageTripData\x12\x13\n\x0bstart_round\x18\x01 \x01(\r\x12\x0f\n\x07\x64\x65st_id\x18\x02 \x01(\r\x12!\n\x06reward\x18\x03 \x03(\x0b\x32\x11.lq.VillageReward\x12\r\n\x05level\x18\x04 \x01(\r\x12#\n\x04info\x18\x05 \x01(\x0b\x32\x15.lq.VillageTargetInfo\"6\n\x0fVillageTaskData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x17\n\x0f\x63ompleted_count\x18\x02 \x01(\r\"l\n\x11VillageTargetInfo\x12\x10\n\x08nickname\x18\x01 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x02 \x01(\r\x12\x14\n\x0c\x61vatar_frame\x18\x03 \x01(\r\x12\r\n\x05title\x18\x04 \x01(\r\x12\x10\n\x08verified\x18\x05 \x01(\r\"\xac\x01\n\x13\x41\x63tivityVillageData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12*\n\tbuildings\x18\x02 \x03(\x0b\x32\x17.lq.VillageBuildingData\x12!\n\x04trip\x18\x03 \x03(\x0b\x32\x13.lq.VillageTripData\x12\"\n\x05tasks\x18\x06 \x03(\x0b\x32\x13.lq.VillageTaskData\x12\r\n\x05round\x18\x07 \x01(\r\"5\n\x0fTimeCounterData\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x13\n\x0bupdate_time\x18\x02 \x01(\r\";\n\x15SignedTimeCounterData\x12\r\n\x05\x63ount\x18\x01 \x01(\x05\x12\x13\n\x0bupdate_time\x18\x02 \x01(\r\"D\n\x14\x46\x65stivalProposalData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0bproposal_id\x18\x02 \x01(\r\x12\x0b\n\x03pos\x18\x03 \x01(\r\"\xae\x01\n\x14\x41\x63tivityFestivalData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12/\n\rproposal_list\x18\x03 \x03(\x0b\x32\x18.lq.FestivalProposalData\x12\x12\n\nevent_list\x18\x04 \x03(\r\x12-\n\nbuy_record\x18\x05 \x01(\x0b\x32\x19.lq.SignedTimeCounterData\"\xbe\x01\n\x10SimulationV2Data\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12*\n\x06season\x18\x02 \x01(\x0b\x32\x1a.lq.SimulationV2SeasonData\x12\x15\n\rhighest_score\x18\x03 \x01(\x05\x12(\n\x07upgrade\x18\x04 \x01(\x0b\x32\x17.lq.SimulationV2Ability\x12\x12\n\nevent_pool\x18\x05 \x03(\r\x12\x14\n\x0cseason_count\x18\x06 \x01(\r\"]\n\x11IslandBagItemData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x03(\r\x12\x0e\n\x06rotate\x18\x03 \x01(\r\x12\x10\n\x08goods_id\x18\x04 \x01(\r\x12\r\n\x05price\x18\x05 \x01(\r\"Q\n\rIslandBagData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06matrix\x18\x02 \x01(\t\x12$\n\x05items\x18\x03 \x03(\x0b\x32\x15.lq.IslandBagItemData\"G\n\x0fIslandGoodsData\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x12\x13\n\x0bupdate_time\x18\x03 \x01(\r\"z\n\x0eIslandZoneData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x30\n\rcurrency_used\x18\x02 \x01(\x0b\x32\x19.lq.SignedTimeCounterData\x12*\n\rgoods_records\x18\x03 \x03(\x0b\x32\x13.lq.IslandGoodsData\"{\n\x12\x41\x63tivityIslandData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04zone\x18\x02 \x01(\r\x12\x1f\n\x04\x62\x61gs\x18\x03 \x03(\x0b\x32\x11.lq.IslandBagData\x12!\n\x05zones\x18\x04 \x03(\x0b\x32\x12.lq.IslandZoneData\":\n\x10\x41muletEffectData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03uid\x18\x02 \x01(\r\x12\r\n\x05store\x18\x03 \x03(\x03\"+\n\x0e\x41muletBuffData\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05store\x18\x03 \x03(\x03\"P\n\x13\x41muletGameShopGoods\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04sold\x18\x02 \x01(\x08\x12\x10\n\x08goods_id\x18\x03 \x01(\r\x12\r\n\x05price\x18\x04 \x01(\r\"F\n\x16\x41muletActivityTingInfo\x12\x0c\n\x04tile\x18\x01 \x01(\t\x12\x0b\n\x03\x66\x61n\x18\x02 \x01(\x04\x12\x11\n\tting_tile\x18\x03 \x01(\t\"4\n\x19\x41muletShowDesktopTileData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x01(\r\"\x83\x01\n\x13\x41muletGameOperation\x12\x0c\n\x04type\x18\x01 \x01(\r\x12/\n\x04gang\x18\x02 \x03(\x0b\x32!.lq.AmuletGameOperation.GangTiles\x12\x11\n\teffect_id\x18\x03 \x01(\r\x1a\x1a\n\tGangTiles\x12\r\n\x05tiles\x18\x01 \x03(\r\"\x9b\x01\n\x12\x41muletGameShopData\x12&\n\x05goods\x18\x01 \x03(\x0b\x32\x17.lq.AmuletGameShopGoods\x12\x13\n\x0b\x65\x66\x66\x65\x63t_list\x18\x02 \x03(\r\x12\x1a\n\x12shop_refresh_count\x18\x03 \x01(\r\x12\x15\n\rrefresh_price\x18\x04 \x01(\r\x12\x15\n\rnext_goods_id\x18\x05 \x01(\r\"\xa7\x05\n\x14\x41muletGameUpdateData\x12$\n\x0ctile_replace\x18\x01 \x03(\x0b\x32\x0e.lq.AmuletTile\x12\x11\n\ttian_dora\x18\x02 \x03(\t\x12\x0c\n\x04\x64ora\x18\x04 \x03(\r\x12\r\n\x05hands\x18\x07 \x03(\r\x12 \n\x04ming\x18\x08 \x03(\x0b\x32\x12.lq.AmuletMingInfo\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\t \x03(\x0b\x32\x14.lq.AmuletEffectData\x12\'\n\tbuff_list\x18\n \x03(\x0b\x32\x14.lq.AmuletEffectData\x12\r\n\x05point\x18\r \x01(\t\x12\x0c\n\x04\x63oin\x18\x0e \x01(\r\x12\r\n\x05stage\x18\x16 \x01(\r\x12\x16\n\x0e\x64\x65sktop_remain\x18\x1a \x01(\r\x12\x39\n\x12show_desktop_tiles\x18\x1c \x03(\x0b\x32\x1d.lq.AmuletShowDesktopTileData\x12-\n\tting_list\x18\x1e \x03(\x0b\x32\x1a.lq.AmuletActivityTingInfo\x12/\n\x0enext_operation\x18\x1f \x03(\x0b\x32\x17.lq.AmuletGameOperation\x12\x14\n\x0cused_desktop\x18\" \x03(\r\x12.\n\nhighest_hu\x18# \x01(\x0b\x32\x1a.lq.ActivityAmuletHuRecord\x12)\n\x07records\x18$ \x01(\x0b\x32\x18.lq.ActivityAmuletRecord\x12\x13\n\x0breward_pack\x18% \x03(\r\x12\x15\n\rreward_effect\x18& \x03(\r\x12/\n\ntile_score\x18+ \x03(\x0b\x32\x1b.lq.AmuletGameTileScoreData\x12\x16\n\x0ereward_pack_id\x18/ \x01(\r\"`\n\x14\x41muletGameRecordData\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x11\n\tint_value\x18\x02 \x01(\x05\x12\x11\n\tstr_value\x18\x03 \x01(\t\x12\x15\n\rint_arr_value\x18\x04 \x03(\x05\"6\n\x17\x41muletGameTileScoreData\x12\x0c\n\x04tile\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\t\"\xb2\t\n\x0e\x41muletGameData\x12\x1c\n\x04pool\x18\x01 \x03(\x0b\x32\x0e.lq.AmuletTile\x12$\n\x0ctile_replace\x18\x02 \x03(\x0b\x32\x0e.lq.AmuletTile\x12\x11\n\ttian_dora\x18\x03 \x03(\t\x12\x10\n\x08mountain\x18\x04 \x03(\r\x12\x0c\n\x04\x64ora\x18\x05 \x03(\r\x12\r\n\x05hands\x18\x07 \x03(\r\x12 \n\x04ming\x18\x08 \x03(\x0b\x32\x12.lq.AmuletMingInfo\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\t \x03(\x0b\x32\x14.lq.AmuletEffectData\x12%\n\tbuff_list\x18\n \x03(\x0b\x32\x12.lq.AmuletBuffData\x12\r\n\x05level\x18\x0b \x01(\r\x12\r\n\x05point\x18\r \x01(\t\x12\x0c\n\x04\x63oin\x18\x0e \x01(\r\x12$\n\x04shop\x18\x10 \x01(\x0b\x32\x16.lq.AmuletGameShopData\x12\x0c\n\x04used\x18\x14 \x03(\r\x12\x11\n\tboss_buff\x18\x15 \x03(\r\x12\r\n\x05stage\x18\x16 \x01(\r\x12\x0f\n\x07\x64\x65sktop\x18\x18 \x03(\r\x12\x14\n\x0cshow_desktop\x18\x19 \x03(\r\x12\x16\n\x0e\x64\x65sktop_remain\x18\x1a \x01(\r\x12\x18\n\x10\x66ree_effect_list\x18\x1b \x03(\r\x12\x39\n\x12show_desktop_tiles\x18\x1c \x03(\x0b\x32\x1d.lq.AmuletShowDesktopTileData\x12\x19\n\x11\x63hange_tile_count\x18\x1d \x01(\r\x12-\n\tting_list\x18\x1e \x03(\x0b\x32\x1a.lq.AmuletActivityTingInfo\x12/\n\x0enext_operation\x18\x1f \x03(\x0b\x32\x17.lq.AmuletGameOperation\x12*\n\x0eshop_buff_list\x18  \x03(\x0b\x32\x12.lq.AmuletBuffData\x12 \n\x18remain_change_tile_count\x18! \x01(\x05\x12\x14\n\x0cused_desktop\x18\" \x03(\r\x12\x12\n\nafter_gang\x18# \x01(\r\x12-\n\x0brecord_data\x18$ \x03(\x0b\x32\x18.lq.AmuletGameRecordData\x12+\n\x0fskill_buff_list\x18% \x03(\x0b\x32\x12.lq.AmuletBuffData\x12\x18\n\x10max_effect_count\x18& \x01(\r\x12.\n\nhighest_hu\x18\' \x01(\x0b\x32\x1a.lq.ActivityAmuletHuRecord\x12\x1b\n\x13total_consumed_coin\x18( \x01(\r\x12\x14\n\x0c\x62oss_buff_id\x18) \x03(\r\x12\x13\n\x0blocked_tile\x18* \x03(\r\x12/\n\ntile_score\x18+ \x03(\x0b\x32\x1b.lq.AmuletGameTileScoreData\x12\x19\n\x11locked_tile_count\x18, \x01(\r\x12\x13\n\x0breward_pack\x18- \x03(\r\x12\x15\n\rreward_effect\x18. \x03(\r\x12\x16\n\x0ereward_pack_id\x18/ \x01(\r\x12\x1f\n\x17total_change_tile_count\x18\x30 \x01(\r\"r\n\x18\x41\x63tivityAmuletUpdateData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12-\n\x0bgame_update\x18\x02 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\x12\x12\n\ngame_empty\x18\x03 \x01(\x08\",\n\x0f\x41muletSkillData\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\"?\n\x19\x41\x63tivityAmuletUpgradeData\x12\"\n\x05skill\x18\x02 \x03(\x0b\x32\x13.lq.AmuletSkillData\"C\n\x14\x41\x63tivityAmuletRecord\x12\x19\n\x11\x65\x66\x66\x65\x63t_gain_count\x18\x01 \x01(\r\x12\x10\n\x08hu_count\x18\x02 \x01(\r\"O\n\x16\x41\x63tivityAmuletHuRecord\x12\r\n\x05point\x18\x01 \x01(\t\x12\x0b\n\x03pai\x18\x02 \x01(\t\x12\x0b\n\x03\x66\x61n\x18\x03 \x01(\t\x12\x0c\n\x04\x62\x61se\x18\x04 \x01(\t\"\x85\x01\n!ActivityAmuletIllustratedBookData\x12\x19\n\x11\x65\x66\x66\x65\x63t_collection\x18\x01 \x03(\r\x12.\n\nhighest_hu\x18\x02 \x01(\x0b\x32\x1a.lq.ActivityAmuletHuRecord\x12\x15\n\rhighest_level\x18\x03 \x01(\r\"<\n\x16\x41\x63tivityAmuletTaskData\x12\"\n\x08progress\x18\x01 \x03(\x0b\x32\x10.lq.TaskProgress\"\xf7\x01\n\x12\x41\x63tivityAmuletData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12 \n\x04game\x18\x02 \x01(\x0b\x32\x12.lq.AmuletGameData\x12\x0f\n\x07version\x18\x03 \x01(\r\x12.\n\x07upgrade\x18\x04 \x01(\x0b\x32\x1d.lq.ActivityAmuletUpgradeData\x12?\n\x10illustrated_book\x18\x05 \x01(\x0b\x32%.lq.ActivityAmuletIllustratedBookData\x12(\n\x04task\x18\x06 \x01(\x0b\x32\x1a.lq.ActivityAmuletTaskData\"\xc0\x03\n\x10\x41\x63tivityFeedData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\nfeed_count\x18\x02 \x01(\r\x12\x43\n\x13\x66riend_receive_data\x18\x03 \x01(\x0b\x32&.lq.ActivityFeedData.CountWithTimeData\x12@\n\x10\x66riend_send_data\x18\x04 \x01(\x0b\x32&.lq.ActivityFeedData.CountWithTimeData\x12\x34\n\ngift_inbox\x18\x05 \x03(\x0b\x32 .lq.ActivityFeedData.GiftBoxData\x12\x14\n\x0cmax_inbox_id\x18\x06 \x01(\r\x1a<\n\x11\x43ountWithTimeData\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x18\n\x10last_update_time\x18\x02 \x01(\r\x1ar\n\x0bGiftBoxData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\x17\n\x0f\x66rom_account_id\x18\x04 \x01(\r\x12\x0c\n\x04time\x18\x05 \x01(\r\x12\x10\n\x08received\x18\x06 \x01(\r\"\x8d\x01\n\x11UnlockedStoryData\x12\x10\n\x08story_id\x18\x01 \x01(\r\x12\x17\n\x0f\x66inished_ending\x18\x02 \x03(\r\x12\x17\n\x0frewarded_ending\x18\x03 \x03(\r\x12\x17\n\x0f\x66inish_rewarded\x18\x04 \x01(\r\x12\x1b\n\x13\x61ll_finish_rewarded\x18\x05 \x01(\r\"W\n\x11\x41\x63tivityStoryData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12-\n\x0eunlocked_story\x18\x02 \x03(\x0b\x32\x15.lq.UnlockedStoryData\"`\n\x14\x41\x63tivityChooseUpData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08\x63hest_id\x18\x02 \x01(\r\x12\x11\n\tselection\x18\x03 \x01(\r\x12\x0e\n\x06is_end\x18\x04 \x01(\r\"\xce\x03\n\x16\x41\x63tivityFriendGiftData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x14\n\x0cmax_inbox_id\x18\x02 \x01(\r\x12\x42\n\x0creceive_data\x18\x03 \x01(\x0b\x32,.lq.ActivityFriendGiftData.CountWithTimeData\x12?\n\tsend_data\x18\x04 \x01(\x0b\x32,.lq.ActivityFriendGiftData.CountWithTimeData\x12:\n\ngift_inbox\x18\x05 \x03(\x0b\x32&.lq.ActivityFriendGiftData.GiftBoxData\x1aT\n\x11\x43ountWithTimeData\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x18\n\x10last_update_time\x18\x02 \x01(\r\x12\x16\n\x0esend_friend_id\x18\x03 \x03(\r\x1ar\n\x0bGiftBoxData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\x17\n\x0f\x66rom_account_id\x18\x04 \x01(\r\x12\x0c\n\x04time\x18\x05 \x01(\r\x12\x10\n\x08received\x18\x06 \x01(\r\"\xa5\x01\n\x13\x41\x63tivityUpgradeData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x32\n\x06groups\x18\x02 \x03(\x0b\x32\".lq.ActivityUpgradeData.LevelGroup\x12\x16\n\x0ereceived_level\x18\x03 \x01(\r\x1a-\n\nLevelGroup\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\"(\n\x0bGachaRecord\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"I\n\x11\x41\x63tivityGachaData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x1f\n\x06gained\x18\x02 \x03(\x0b\x32\x0f.lq.GachaRecord\"e\n\x17\x41\x63tivityGachaUpdateData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x1f\n\x06gained\x18\x02 \x03(\x0b\x32\x0f.lq.GachaRecord\x12\x14\n\x0cremain_count\x18\x03 \x01(\r\"N\n#ActivitySimulationGameRecordMessage\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0c\n\x04\x61rgs\x18\x02 \x03(\r\x12\x0b\n\x03xun\x18\x03 \x01(\r\"\xa9\x01\n\x1c\x41\x63tivitySimulationGameRecord\x12\r\n\x05round\x18\x01 \x01(\r\x12\r\n\x05seats\x18\x02 \x03(\r\x12\x0c\n\x04uuid\x18\x03 \x01(\t\x12\x12\n\nstart_time\x18\x04 \x01(\r\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\x39\n\x08messages\x18\x06 \x03(\x0b\x32\'.lq.ActivitySimulationGameRecordMessage\"\x83\x01\n\x1e\x41\x63tivitySimulationDailyContest\x12\x0b\n\x03\x64\x61y\x18\x01 \x01(\r\x12\x12\n\ncharacters\x18\x02 \x03(\r\x12\x31\n\x07records\x18\x03 \x03(\x0b\x32 .lq.ActivitySimulationGameRecord\x12\r\n\x05round\x18\x04 \x01(\r\"f\n\x1d\x41\x63tivitySimulationTrainRecord\x12\x0c\n\x04time\x18\x01 \x01(\r\x12\x14\n\x0cmodify_stats\x18\x02 \x03(\x05\x12\x13\n\x0b\x66inal_stats\x18\x03 \x03(\r\x12\x0c\n\x04type\x18\x04 \x01(\r\"\xce\x01\n\x16\x41\x63tivitySimulationData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05stats\x18\x02 \x03(\r\x12\x1b\n\x13stamina_update_time\x18\x03 \x01(\r\x12\x39\n\rdaily_contest\x18\x04 \x03(\x0b\x32\".lq.ActivitySimulationDailyContest\x12\x38\n\rtrain_records\x18\x05 \x03(\x0b\x32!.lq.ActivitySimulationTrainRecord\"\xb1\x01\n\x10\x41\x63tivitySpotData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12,\n\x05spots\x18\x03 \x03(\x0b\x32\x1d.lq.ActivitySpotData.SpotData\x1aZ\n\x08SpotData\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x10\n\x08rewarded\x18\x02 \x01(\r\x12\x17\n\x0funlocked_ending\x18\x03 \x03(\r\x12\x10\n\x08unlocked\x18\x04 \x01(\r\"\x8d\x01\n\x12\x41\x63\x63ountActiveState\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x12\n\nlogin_time\x18\x02 \x01(\r\x12\x13\n\x0blogout_time\x18\x03 \x01(\r\x12\x11\n\tis_online\x18\x04 \x01(\x08\x12\'\n\x07playing\x18\x05 \x01(\x0b\x32\x16.lq.AccountPlayingGame\"a\n\x06\x46riend\x12 \n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x12.lq.PlayerBaseView\x12%\n\x05state\x18\x02 \x01(\x0b\x32\x16.lq.AccountActiveState\x12\x0e\n\x06remark\x18\x03 \x01(\t\"\x1d\n\x05Point\x12\t\n\x01x\x18\x01 \x01(\r\x12\t\n\x01y\x18\x02 \x01(\r\"K\n\nMineReward\x12\x18\n\x05point\x18\x01 \x01(\x0b\x32\t.lq.Point\x12\x11\n\treward_id\x18\x02 \x01(\r\x12\x10\n\x08received\x18\x03 \x01(\x08\"O\n\x0cGameLiveUnit\x12\x11\n\ttimestamp\x18\x01 \x01(\r\x12\x17\n\x0f\x61\x63tion_category\x18\x02 \x01(\r\x12\x13\n\x0b\x61\x63tion_data\x18\x03 \x01(\x0c\"4\n\x0fGameLiveSegment\x12!\n\x07\x61\x63tions\x18\x01 \x03(\x0b\x32\x10.lq.GameLiveUnit\"=\n\x12GameLiveSegmentUri\x12\x12\n\nsegment_id\x18\x01 \x01(\r\x12\x13\n\x0bsegment_uri\x18\x02 \x01(\t\"\x8d\x01\n\x0cGameLiveHead\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12#\n\x0bgame_config\x18\x03 \x01(\x0b\x32\x0e.lq.GameConfig\x12#\n\x07players\x18\x04 \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x11\n\tseat_list\x18\x05 \x03(\r\"(\n\x11GameNewRoundState\x12\x13\n\x0bseat_states\x18\x01 \x03(\r\"\x1e\n\rGameEndAction\x12\r\n\x05state\x18\x01 \x01(\r\"\x10\n\x0eGameNoopAction\"\x7f\n\x0b\x43ommentItem\x12\x12\n\ncomment_id\x18\x01 \x01(\r\x12\x11\n\ttimestamp\x18\x02 \x01(\r\x12%\n\tcommenter\x18\x03 \x01(\x0b\x32\x12.lq.PlayerBaseView\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\x12\x11\n\tis_banned\x18\x05 \x01(\r\"\x89\x01\n\rRollingNotice\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x04 \x01(\r\x12\x17\n\x0frepeat_interval\x18\x05 \x01(\r\x12\x13\n\x0brepeat_time\x18\x07 \x03(\r\x12\x13\n\x0brepeat_type\x18\x08 \x01(\r\"\'\n\x0eMaintainNotice\x12\x15\n\rmaintain_time\x18\x01 \x01(\r\"q\n\x0c\x42illingGoods\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x65sc\x18\x03 \x01(\t\x12\x0c\n\x04icon\x18\x04 \x01(\t\x12\x13\n\x0bresource_id\x18\x05 \x01(\r\x12\x16\n\x0eresource_count\x18\x06 \x01(\r\"=\n\x0c\x42illShortcut\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x12\n\ndeal_price\x18\x03 \x01(\r\"u\n\x0e\x42illingProduct\x12\x1f\n\x05goods\x18\x01 \x01(\x0b\x32\x10.lq.BillingGoods\x12\x15\n\rcurrency_code\x18\x02 \x01(\t\x12\x16\n\x0e\x63urrency_price\x18\x03 \x01(\r\x12\x13\n\x0bsort_weight\x18\x04 \x01(\r\"\xa4\x01\n\tCharacter\x12\x0e\n\x06\x63harid\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0b\n\x03\x65xp\x18\x03 \x01(\r\x12\x1b\n\x05views\x18\x04 \x03(\x0b\x32\x0c.lq.ViewSlot\x12\x0c\n\x04skin\x18\x05 \x01(\r\x12\x13\n\x0bis_upgraded\x18\x06 \x01(\x08\x12\x13\n\x0b\x65xtra_emoji\x18\x07 \x03(\r\x12\x16\n\x0erewarded_level\x18\x08 \x03(\r\"&\n\tBuyRecord\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\xca\x01\n\x07ZHPShop\x12\r\n\x05goods\x18\x01 \x03(\r\x12\"\n\x0b\x62uy_records\x18\x02 \x03(\x0b\x32\r.lq.BuyRecord\x12.\n\x0c\x66ree_refresh\x18\x03 \x01(\x0b\x32\x18.lq.ZHPShop.RefreshCount\x12.\n\x0c\x63ost_refresh\x18\x04 \x01(\x0b\x32\x18.lq.ZHPShop.RefreshCount\x1a,\n\x0cRefreshCount\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\r\n\x05limit\x18\x02 \x01(\r\"F\n\x0fMonthTicketInfo\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\r\x12\x15\n\rlast_pay_time\x18\x03 \x01(\r\"c\n\x08ShopInfo\x12\x18\n\x03zhp\x18\x01 \x01(\x0b\x32\x0b.lq.ZHPShop\x12\"\n\x0b\x62uy_records\x18\x02 \x03(\x0b\x32\r.lq.BuyRecord\x12\x19\n\x11last_refresh_time\x18\x03 \x01(\r\">\n\x14\x43hangeNicknameRecord\x12\x0c\n\x04\x66rom\x18\x01 \x01(\t\x12\n\n\x02to\x18\x02 \x01(\t\x12\x0c\n\x04time\x18\x03 \x01(\r\"\x9e\x01\n\x0eServerSettings\x12+\n\x0fpayment_setting\x18\x03 \x01(\x0b\x32\x12.lq.PaymentSetting\x12\x30\n\x12payment_setting_v2\x18\x04 \x01(\x0b\x32\x14.lq.PaymentSettingV2\x12-\n\x10nickname_setting\x18\x05 \x01(\x0b\x32\x13.lq.NicknameSetting\"4\n\x0fNicknameSetting\x12\x0e\n\x06\x65nable\x18\x01 \x01(\r\x12\x11\n\tnicknames\x18\x02 \x03(\t\"\xed\x03\n\x10PaymentSettingV2\x12\x14\n\x0copen_payment\x18\x01 \x01(\r\x12\x42\n\x11payment_platforms\x18\x02 \x03(\x0b\x32\'.lq.PaymentSettingV2.PaymentSettingUnit\x1a\x86\x01\n\x0fPaymentMaintain\x12\x12\n\nstart_time\x18\x01 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\r\x12\x1a\n\x12goods_click_action\x18\x03 \x01(\r\x12\x18\n\x10goods_click_text\x18\x04 \x01(\t\x12\x17\n\x0f\x65nabled_channel\x18\x05 \x03(\t\x1a\xf5\x01\n\x12PaymentSettingUnit\x12\x10\n\x08platform\x18\x01 \x01(\t\x12\x0f\n\x07is_show\x18\x02 \x01(\x08\x12\x1a\n\x12goods_click_action\x18\x03 \x01(\r\x12\x18\n\x10goods_click_text\x18\x04 \x01(\t\x12\x36\n\x08maintain\x18\x05 \x01(\x0b\x32$.lq.PaymentSettingV2.PaymentMaintain\x12!\n\x19\x65nable_for_frozen_account\x18\x06 \x01(\x08\x12\x12\n\nextra_data\x18\x07 \x01(\t\x12\x17\n\x0f\x65nabled_channel\x18\x08 \x03(\t\"\xdf\x02\n\x0ePaymentSetting\x12\x14\n\x0copen_payment\x18\x01 \x01(\r\x12\x1e\n\x16payment_info_show_type\x18\x02 \x01(\r\x12\x14\n\x0cpayment_info\x18\x03 \x01(\t\x12-\n\x06wechat\x18\x04 \x01(\x0b\x32\x1d.lq.PaymentSetting.WechatData\x12-\n\x06\x61lipay\x18\x05 \x01(\x0b\x32\x1d.lq.PaymentSetting.AlipayData\x1a\\\n\nWechatData\x12\x16\n\x0e\x64isable_create\x18\x01 \x01(\x08\x12\x1f\n\x17payment_source_platform\x18\x02 \x01(\r\x12\x15\n\renable_credit\x18\x03 \x01(\x08\x1a\x45\n\nAlipayData\x12\x16\n\x0e\x64isable_create\x18\x01 \x01(\x08\x12\x1f\n\x17payment_source_platform\x18\x02 \x01(\r\",\n\x0e\x41\x63\x63ountSetting\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"h\n\tChestData\x12\x10\n\x08\x63hest_id\x18\x01 \x01(\r\x12\x18\n\x10total_open_count\x18\x02 \x01(\r\x12\x15\n\rconsume_count\x18\x03 \x01(\r\x12\x18\n\x10\x66\x61\x63\x65_black_count\x18\x04 \x01(\r\"t\n\x0b\x43hestDataV2\x12\x10\n\x08\x63hest_id\x18\x01 \x01(\r\x12\x18\n\x10total_open_count\x18\x02 \x01(\r\x12\x18\n\x10\x66\x61\x63\x65_black_count\x18\x03 \x01(\r\x12\x1f\n\x17ticket_face_black_count\x18\x04 \x01(\r\"d\n\tFaithData\x12\x10\n\x08\x66\x61ith_id\x18\x01 \x01(\r\x12\x18\n\x10total_open_count\x18\x02 \x01(\r\x12\x15\n\rconsume_count\x18\x03 \x01(\r\x12\x14\n\x0cmodify_count\x18\x04 \x01(\x05\"\x9c\x02\n\x15\x43ustomizedContestBase\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x12\n\ncontest_id\x18\x02 \x01(\r\x12\x14\n\x0c\x63ontest_name\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\r\x12\x12\n\ncreator_id\x18\x05 \x01(\r\x12\x13\n\x0b\x63reate_time\x18\x06 \x01(\r\x12\x12\n\nstart_time\x18\x07 \x01(\r\x12\x13\n\x0b\x66inish_time\x18\x08 \x01(\r\x12\x0c\n\x04open\x18\t \x01(\x08\x12\x14\n\x0c\x63ontest_type\x18\n \x01(\r\x12\x15\n\rpublic_notice\x18\x0b \x01(\t\x12\x13\n\x0b\x63heck_state\x18\x0c \x01(\r\x12\x15\n\rchecking_name\x18\r \x01(\t\"C\n\x17\x43ustomizedContestExtend\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x15\n\rpublic_notice\x18\x02 \x01(\t\"\xf4\x01\n\x19\x43ustomizedContestAbstract\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x12\n\ncontest_id\x18\x02 \x01(\r\x12\x14\n\x0c\x63ontest_name\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\r\x12\x12\n\ncreator_id\x18\x05 \x01(\r\x12\x13\n\x0b\x63reate_time\x18\x06 \x01(\r\x12\x12\n\nstart_time\x18\x07 \x01(\r\x12\x13\n\x0b\x66inish_time\x18\x08 \x01(\r\x12\x0c\n\x04open\x18\t \x01(\x08\x12\x15\n\rpublic_notice\x18\n \x01(\t\x12\x14\n\x0c\x63ontest_type\x18\x0b \x01(\r\"\xce\x03\n\x17\x43ustomizedContestDetail\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x12\n\ncontest_id\x18\x02 \x01(\r\x12\x14\n\x0c\x63ontest_name\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\r\x12\x12\n\ncreator_id\x18\x05 \x01(\r\x12\x13\n\x0b\x63reate_time\x18\x06 \x01(\r\x12\x12\n\nstart_time\x18\x07 \x01(\r\x12\x13\n\x0b\x66inish_time\x18\x08 \x01(\r\x12\x0c\n\x04open\x18\t \x01(\x08\x12\x11\n\trank_rule\x18\n \x01(\r\x12\x1f\n\tgame_mode\x18\x0b \x01(\x0b\x32\x0c.lq.GameMode\x12\x16\n\x0eprivate_notice\x18\x0c \x01(\t\x12\x17\n\x0fobserver_switch\x18\r \x01(\r\x12\x14\n\x0c\x65moji_switch\x18\x0e \x01(\r\x12\x14\n\x0c\x63ontest_type\x18\x0f \x01(\r\x12\x19\n\x11\x64isable_broadcast\x18\x10 \x01(\r\x12\x19\n\x11signup_start_time\x18\x11 \x01(\r\x12\x17\n\x0fsignup_end_time\x18\x12 \x01(\r\x12\x13\n\x0bsignup_type\x18\x13 \x01(\r\x12\x12\n\nauto_match\x18\x14 \x01(\r\"}\n\x1d\x43ustomizedContestPlayerReport\x12\x11\n\trank_rule\x18\x01 \x01(\r\x12\x0c\n\x04rank\x18\x02 \x01(\r\x12\r\n\x05point\x18\x03 \x01(\x05\x12\x12\n\ngame_ranks\x18\x04 \x03(\r\x12\x18\n\x10total_game_count\x18\x05 \x01(\r\"\x84\x04\n\nRecordGame\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\r\x12\x1e\n\x06\x63onfig\x18\x05 \x01(\x0b\x32\x0e.lq.GameConfig\x12,\n\x08\x61\x63\x63ounts\x18\x0b \x03(\x0b\x32\x1a.lq.RecordGame.AccountInfo\x12!\n\x06result\x18\x0c \x01(\x0b\x32\x11.lq.GameEndResult\x12*\n\x06robots\x18\r \x03(\x0b\x32\x1a.lq.RecordGame.AccountInfo\x12\x15\n\rstandard_rule\x18\x0e \x01(\r\x1a\x8d\x02\n\x0b\x41\x63\x63ountInfo\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0c\n\x04seat\x18\x02 \x01(\r\x12\x10\n\x08nickname\x18\x03 \x01(\t\x12\x11\n\tavatar_id\x18\x04 \x01(\r\x12 \n\tcharacter\x18\x05 \x01(\x0b\x32\r.lq.Character\x12\r\n\x05title\x18\x06 \x01(\r\x12\x1f\n\x05level\x18\x07 \x01(\x0b\x32\x10.lq.AccountLevel\x12 \n\x06level3\x18\x08 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x14\n\x0c\x61vatar_frame\x18\t \x01(\r\x12\x10\n\x08verified\x18\n \x01(\r\x12\x1b\n\x05views\x18\x0b \x03(\x0b\x32\x0c.lq.ViewSlot\"\xb3\x01\n\x0fRecordListEntry\x12\x0f\n\x07version\x18\x01 \x01(\r\x12\x0c\n\x04uuid\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x04 \x01(\r\x12\x0b\n\x03tag\x18\x05 \x01(\r\x12\x0e\n\x06subtag\x18\x06 \x01(\r\x12\'\n\x07players\x18\x07 \x03(\x0b\x32\x16.lq.RecordPlayerResult\x12\x15\n\rstandard_rule\x18\x08 \x01(\r\"\xb0\x02\n\x12RecordPlayerResult\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x10\n\x08nickname\x18\x03 \x01(\t\x12\x1f\n\x05level\x18\x04 \x01(\x0b\x32\x10.lq.AccountLevel\x12 \n\x06level3\x18\x05 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x0c\n\x04seat\x18\x06 \x01(\r\x12\n\n\x02pt\x18\x07 \x01(\x05\x12\r\n\x05point\x18\x08 \x01(\x05\x12\x13\n\x0bmax_hu_type\x18\t \x01(\r\x12\x13\n\x0b\x61\x63tion_liqi\x18\n \x01(\r\x12\x13\n\x0b\x61\x63tion_rong\x18\x0b \x01(\r\x12\x13\n\x0b\x61\x63tion_zimo\x18\x0c \x01(\r\x12\x14\n\x0c\x61\x63tion_chong\x18\r \x01(\r\x12\x10\n\x08verified\x18\x0e \x01(\r\"\x80\x01\n\x1a\x43ustomizedContestGameStart\x12\x34\n\x07players\x18\x01 \x03(\x0b\x32#.lq.CustomizedContestGameStart.Item\x1a,\n\x04Item\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08nickname\x18\x02 \x01(\t\"\x91\x01\n\x18\x43ustomizedContestGameEnd\x12\x32\n\x07players\x18\x01 \x03(\x0b\x32!.lq.CustomizedContestGameEnd.Item\x1a\x41\n\x04Item\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x13\n\x0btotal_point\x18\x03 \x01(\x05\"S\n\x08\x41\x63tivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\r\x12\x0c\n\x04type\x18\x04 \x01(\t\"4\n\x0e\x45xchangeRecord\x12\x13\n\x0b\x65xchange_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"^\n\x1c\x41\x63tivityAccumulatedPointData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\x05\x12\x1a\n\x12gained_reward_list\x18\x03 \x03(\r\"l\n\x15\x41\x63tivityRankPointData\x12\x16\n\x0eleaderboard_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\x05\x12\x15\n\rgained_reward\x18\x03 \x01(\x08\x12\x15\n\rgainable_time\x18\x04 \x01(\r\"\xb9\x03\n\x0fGameRoundHuData\x12(\n\x05hupai\x18\x01 \x01(\x0b\x32\x19.lq.GameRoundHuData.HuPai\x12%\n\x04\x66\x61ns\x18\x02 \x03(\x0b\x32\x17.lq.GameRoundHuData.Fan\x12\r\n\x05score\x18\x03 \x01(\r\x12\x0b\n\x03xun\x18\x04 \x01(\r\x12\x10\n\x08title_id\x18\x05 \x01(\r\x12\x0f\n\x07\x66\x61n_sum\x18\x06 \x01(\r\x12\x0e\n\x06\x66u_sum\x18\x07 \x01(\r\x12\x15\n\ryakuman_count\x18\x08 \x01(\r\x12\x17\n\x0f\x62iao_dora_count\x18\t \x01(\r\x12\x16\n\x0ered_dora_count\x18\n \x01(\r\x12\x15\n\rli_dora_count\x18\x0b \x01(\r\x12\x13\n\x0b\x62\x61\x62\x65i_count\x18\x0c \x01(\r\x12\x18\n\x10xuan_shang_count\x18\r \x01(\r\x12\x16\n\x0epai_left_count\x18\x0e \x01(\r\x1a\x31\n\x05HuPai\x12\x0c\n\x04tile\x18\x01 \x01(\t\x12\x0c\n\x04seat\x18\x02 \x01(\r\x12\x0c\n\x04liqi\x18\x03 \x01(\r\x1a-\n\x03\x46\x61n\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0b\n\x03\x66\x61n\x18\x03 \x01(\r\"R\n\x1cGameRoundPlayerFangChongInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\x12\x16\n\x0epai_left_count\x18\x03 \x01(\r\"\x98\x02\n\x15GameRoundPlayerResult\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05hands\x18\x02 \x03(\t\x12\x0c\n\x04ming\x18\x03 \x03(\t\x12\x11\n\tliqi_type\x18\x04 \x01(\r\x12\x0f\n\x07is_fulu\x18\x05 \x01(\x08\x12\x17\n\x0fis_liujumanguan\x18\x06 \x01(\x08\x12\x13\n\x0blian_zhuang\x18\x07 \x01(\r\x12\x1f\n\x02hu\x18\x08 \x01(\x0b\x32\x13.lq.GameRoundHuData\x12\x34\n\nfangchongs\x18\t \x03(\x0b\x32 .lq.GameRoundPlayerFangChongInfo\x12\x16\n\x0eliqi_fangchong\x18\n \x01(\x08\x12\x13\n\x0bliqi_failed\x18\x0b \x01(\x08\"Y\n\x0fGameRoundPlayer\x12\r\n\x05score\x18\x01 \x01(\x05\x12\x0c\n\x04rank\x18\x02 \x01(\r\x12)\n\x06result\x18\x03 \x01(\x0b\x32\x19.lq.GameRoundPlayerResult\"R\n\x11GameRoundSnapshot\x12\n\n\x02ju\x18\x01 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x02 \x01(\r\x12$\n\x07players\x18\x03 \x03(\x0b\x32\x13.lq.GameRoundPlayer\"\xbb\x07\n\x11GameFinalSnapshot\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\r\n\x05state\x18\x02 \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x03 \x01(\r\x12\x1a\n\x04mode\x18\x04 \x01(\x0b\x32\x0c.lq.GameMode\x12\x1e\n\x04meta\x18\x05 \x01(\x0b\x32\x10.lq.GameMetaData\x12=\n\x0f\x63\x61lculate_param\x18\x06 \x01(\x0b\x32$.lq.GameFinalSnapshot.CalculateParam\x12\x13\n\x0b\x63reate_time\x18\x07 \x01(\r\x12\x12\n\nstart_time\x18\x08 \x01(\r\x12\x13\n\x0b\x66inish_time\x18\t \x01(\r\x12-\n\x05seats\x18\n \x03(\x0b\x32\x1e.lq.GameFinalSnapshot.GameSeat\x12%\n\x06rounds\x18\x0b \x03(\x0b\x32\x15.lq.GameRoundSnapshot\x12)\n\raccount_views\x18\x0c \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x38\n\rfinal_players\x18\r \x03(\x0b\x32!.lq.GameFinalSnapshot.FinalPlayer\x12/\n\x08\x61\x66k_info\x18\x0e \x03(\x0b\x32\x1d.lq.GameFinalSnapshot.AFKInfo\x12\'\n\x0brobot_views\x18\x0f \x03(\x0b\x32\x12.lq.PlayerGameView\x1aS\n\x0e\x43\x61lculateParam\x12\x12\n\ninit_point\x18\x01 \x01(\r\x12\x18\n\x10jingsuanyuandian\x18\x02 \x01(\r\x12\x13\n\x0brank_points\x18\x03 \x03(\x05\x1a\x88\x01\n\x08GameSeat\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12,\n\x0fnotify_endpoint\x18\x03 \x01(\x0b\x32\x13.lq.NetworkEndpoint\x12\x16\n\x0e\x63lient_address\x18\x04 \x01(\t\x12\x14\n\x0cis_connected\x18\x05 \x01(\x08\x1a\x81\x01\n\x0b\x46inalPlayer\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x13\n\x0btotal_point\x18\x02 \x01(\x05\x12\x14\n\x0cpart_point_1\x18\x03 \x01(\x05\x12\x14\n\x0cpart_point_2\x18\x04 \x01(\x05\x12\x15\n\rgrading_score\x18\x05 \x01(\x05\x12\x0c\n\x04gold\x18\x06 \x01(\x05\x1a\x45\n\x07\x41\x46KInfo\x12\x17\n\x0f\x64\x65\x61l_tile_count\x18\x01 \x01(\r\x12\x13\n\x0bmoqie_count\x18\x02 \x01(\r\x12\x0c\n\x04seat\x18\x03 \x01(\r\"Z\n\x13RecordCollectedData\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x0f\n\x07remarks\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x04 \x01(\r\"\x92\n\n\x11\x43ontestDetailRule\x12\x12\n\ninit_point\x18\x05 \x01(\r\x12\x0f\n\x07\x66\x61ndian\x18\x06 \x01(\r\x12\x11\n\tcan_jifei\x18\x07 \x01(\x08\x12\x16\n\x0etianbian_value\x18\x08 \x01(\r\x12\x16\n\x0eliqibang_value\x18\t \x01(\r\x12\x17\n\x0f\x63hangbang_value\x18\n \x01(\r\x12\x15\n\rnoting_fafu_1\x18\x0b \x01(\r\x12\x15\n\rnoting_fafu_2\x18\x0c \x01(\r\x12\x15\n\rnoting_fafu_3\x18\r \x01(\r\x12\x19\n\x11have_liujumanguan\x18\x0e \x01(\x08\x12\x1c\n\x14have_qieshangmanguan\x18\x0f \x01(\x08\x12\x16\n\x0ehave_biao_dora\x18\x10 \x01(\x08\x12\x1b\n\x13have_gang_biao_dora\x18\x11 \x01(\x08\x12\"\n\x1aming_dora_immediately_open\x18\x12 \x01(\x08\x12\x14\n\x0chave_li_dora\x18\x13 \x01(\x08\x12\x19\n\x11have_gang_li_dora\x18\x14 \x01(\x08\x12\x19\n\x11have_sifenglianda\x18\x15 \x01(\x08\x12\x18\n\x10have_sigangsanle\x18\x16 \x01(\x08\x12\x17\n\x0fhave_sijializhi\x18\x17 \x01(\x08\x12\x1b\n\x13have_jiuzhongjiupai\x18\x18 \x01(\x08\x12\x17\n\x0fhave_sanjiahele\x18\x19 \x01(\x08\x12\x14\n\x0chave_toutiao\x18\x1a \x01(\x08\x12\x1b\n\x13have_helelianzhuang\x18\x1b \x01(\x08\x12\x18\n\x10have_helezhongju\x18\x1c \x01(\x08\x12\x1e\n\x16have_tingpailianzhuang\x18\x1d \x01(\x08\x12\x1b\n\x13have_tingpaizhongju\x18\x1e \x01(\x08\x12\x11\n\thave_yifa\x18\x1f \x01(\x08\x12\x16\n\x0ehave_nanruxiru\x18  \x01(\x08\x12\x18\n\x10jingsuanyuandian\x18! \x01(\r\x12\x13\n\x0bshunweima_2\x18\" \x01(\x05\x12\x13\n\x0bshunweima_3\x18# \x01(\x05\x12\x13\n\x0bshunweima_4\x18$ \x01(\x05\x12\x14\n\x0c\x62ianjietishi\x18% \x01(\x08\x12\x10\n\x08\x61i_level\x18& \x01(\r\x12\x14\n\x0chave_zimosun\x18\' \x01(\x08\x12\x1d\n\x15\x64isable_multi_yukaman\x18( \x01(\x08\x12\x11\n\tguyi_mode\x18) \x01(\r\x12\x1a\n\x12\x64isable_leijiyiman\x18* \x01(\x08\x12\x12\n\ndora3_mode\x18+ \x01(\r\x12\x14\n\x0cxuezhandaodi\x18, \x01(\r\x12\x14\n\x0chuansanzhang\x18- \x01(\r\x12\x0f\n\x07\x63huanma\x18. \x01(\r\x12\x1e\n\x16\x64isable_double_yakuman\x18> \x01(\r\x12!\n\x19\x64isable_composite_yakuman\x18? \x01(\r\x12\x14\n\x0c\x65nable_shiti\x18@ \x01(\r\x12\x1c\n\x14\x65nable_nontsumo_liqi\x18\x41 \x01(\r\x12#\n\x1b\x64isable_double_wind_four_fu\x18\x42 \x01(\r\x12\x1d\n\x15\x64isable_angang_guoshi\x18\x43 \x01(\r\x12\x14\n\x0c\x65nable_renhe\x18\x44 \x01(\r\x12%\n\x1d\x65nable_baopai_extend_settings\x18\x45 \x01(\r\x12\r\n\x05\x66\x61nfu\x18\x46 \x01(\r\"\xb3\x01\n\x13\x43ontestDetailRuleV2\x12(\n\tgame_rule\x18\x01 \x01(\x0b\x32\x15.lq.ContestDetailRule\x12\x35\n\nextra_rule\x18\x02 \x01(\x0b\x32!.lq.ContestDetailRuleV2.ExtraRule\x1a;\n\tExtraRule\x12\x16\n\x0erequired_level\x18\x01 \x01(\r\x12\x16\n\x0emax_game_count\x18\x02 \x01(\r\"\xab\x01\n\x0fGameRuleSetting\x12\x12\n\nround_type\x18\x01 \x01(\r\x12\x0f\n\x07shiduan\x18\x02 \x01(\x08\x12\x12\n\ndora_count\x18\x03 \x01(\r\x12\x15\n\rthinking_type\x18\x04 \x01(\r\x12\x17\n\x0fuse_detail_rule\x18\x05 \x01(\x08\x12/\n\x0e\x64\x65tail_rule_v2\x18\x06 \x01(\x0b\x32\x17.lq.ContestDetailRuleV2\"\xad\x01\n\x11RecordTingPaiInfo\x12\x0c\n\x04tile\x18\x01 \x01(\t\x12\x0e\n\x06haveyi\x18\x02 \x01(\x08\x12\r\n\x05yiman\x18\x03 \x01(\x08\x12\r\n\x05\x63ount\x18\x04 \x01(\r\x12\n\n\x02\x66u\x18\x05 \x01(\r\x12\x17\n\x0f\x62iao_dora_count\x18\x06 \x01(\r\x12\x12\n\nyiman_zimo\x18\x07 \x01(\x08\x12\x12\n\ncount_zimo\x18\x08 \x01(\r\x12\x0f\n\x07\x66u_zimo\x18\t \x01(\r\"m\n\x16RecordNoTilePlayerInfo\x12\x0f\n\x07tingpai\x18\x03 \x01(\x08\x12\x0c\n\x04hand\x18\x04 \x03(\t\x12$\n\x05tings\x18\x05 \x03(\x0b\x32\x15.lq.RecordTingPaiInfo\x12\x0e\n\x06liuman\x18\x06 \x01(\x08\"\xcb\x03\n\x0eRecordHuleInfo\x12\x0c\n\x04hand\x18\x01 \x03(\t\x12\x0c\n\x04ming\x18\x02 \x03(\t\x12\x0f\n\x07hu_tile\x18\x03 \x01(\t\x12\x0c\n\x04seat\x18\x04 \x01(\r\x12\x0c\n\x04zimo\x18\x05 \x01(\x08\x12\x0e\n\x06qinjia\x18\x06 \x01(\x08\x12\x0c\n\x04liqi\x18\x07 \x01(\x08\x12\r\n\x05\x64oras\x18\x08 \x03(\t\x12\x10\n\x08li_doras\x18\t \x03(\t\x12\r\n\x05yiman\x18\n \x01(\x08\x12\r\n\x05\x63ount\x18\x0b \x01(\r\x12.\n\x04\x66\x61ns\x18\x0c \x03(\x0b\x32 .lq.RecordHuleInfo.RecordFanInfo\x12\n\n\x02\x66u\x18\r \x01(\r\x12\x16\n\x0epoint_zimo_qin\x18\x0e \x01(\r\x12\x17\n\x0fpoint_zimo_xian\x18\x0f \x01(\r\x12\x10\n\x08title_id\x18\x10 \x01(\r\x12\x11\n\tpoint_sum\x18\x11 \x01(\r\x12\x0e\n\x06\x64\x61\x64ian\x18\x12 \x01(\r\x12\x14\n\x0cis_jue_zhang\x18\x13 \x01(\x08\x12\x0b\n\x03xun\x18\x14 \x01(\r\x12\x11\n\tting_type\x18\x15 \x01(\r\x12\x11\n\tting_mian\x18\x16 \x01(\r\x1a(\n\rRecordFanInfo\x12\x0b\n\x03val\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\"B\n\x0fRecordHulesInfo\x12\x0c\n\x04seat\x18\x01 \x01(\x05\x12!\n\x05hules\x18\x02 \x03(\x0b\x32\x12.lq.RecordHuleInfo\"-\n\x0fRecordLiujuInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\"U\n\x10RecordNoTileInfo\x12\x14\n\x0cliujumanguan\x18\x01 \x01(\x08\x12+\n\x07players\x18\x02 \x03(\x0b\x32\x1a.lq.RecordNoTilePlayerInfo\"r\n\x0eRecordLiqiInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\r\x12\x0c\n\x04is_w\x18\x03 \x01(\x08\x12\x14\n\x0cis_zhen_ting\x18\x04 \x01(\x08\x12\x0b\n\x03xun\x18\x05 \x01(\r\x12\x12\n\nis_success\x18\x06 \x01(\x08\"W\n\x0eRecordGangInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0b\n\x03pai\x18\x03 \x01(\t\x12\x0f\n\x07is_dora\x18\x04 \x01(\x08\x12\x0b\n\x03xun\x18\x05 \x01(\r\"S\n\x0fRecordBaBeiInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x10\n\x08is_zi_mo\x18\x02 \x01(\x08\x12\x10\n\x08is_chong\x18\x03 \x01(\x08\x12\x0e\n\x06is_bei\x18\x04 \x01(\x08\"O\n\x10RecordPeiPaiInfo\x12\x12\n\ndora_count\x18\x01 \x01(\r\x12\x14\n\x0cr_dora_count\x18\x02 \x01(\r\x12\x11\n\tbei_count\x18\x03 \x01(\r\"\xfb\x02\n\x0fRecordRoundInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x63hang\x18\x02 \x01(\r\x12\n\n\x02ju\x18\x03 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x04 \x01(\r\x12\x0e\n\x06scores\x18\x05 \x03(\r\x12&\n\nliqi_infos\x18\x07 \x03(\x0b\x32\x12.lq.RecordLiqiInfo\x12&\n\ngang_infos\x18\x08 \x03(\x0b\x32\x12.lq.RecordGangInfo\x12*\n\x0cpeipai_infos\x18\t \x03(\x0b\x32\x14.lq.RecordPeiPaiInfo\x12(\n\x0b\x62\x61\x62\x61i_infos\x18\n \x03(\x0b\x32\x13.lq.RecordBaBeiInfo\x12\'\n\nhules_info\x18\x0b \x01(\x0b\x32\x13.lq.RecordHulesInfo\x12\'\n\nliuju_info\x18\x0c \x01(\x0b\x32\x13.lq.RecordLiujuInfo\x12*\n\x0cno_tile_info\x18\r \x01(\x0b\x32\x14.lq.RecordNoTileInfo\"@\n\x14RecordAnalysisedData\x12(\n\x0bround_infos\x18\x01 \x03(\x0b\x32\x13.lq.RecordRoundInfo\"<\n\x08VoteData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04vote\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\"V\n\x10\x41\x63tivityBuffData\x12\x0f\n\x07\x62uff_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x05 \x01(\r\x12\r\n\x05\x63ount\x18\x06 \x01(\r\x12\x13\n\x0bupdate_time\x18\x07 \x01(\r\"\x96\x04\n\x17\x41\x63\x63ountResourceSnapshot\x12=\n\x08\x62\x61g_item\x18\x01 \x03(\x0b\x32+.lq.AccountResourceSnapshot.BagItemSnapshot\x12>\n\x08\x63urrency\x18\x02 \x03(\x0b\x32,.lq.AccountResourceSnapshot.CurrencySnapshot\x12\x38\n\x05title\x18\x03 \x01(\x0b\x32).lq.AccountResourceSnapshot.TitleSnapshot\x12\x41\n\nused_title\x18\x04 \x01(\x0b\x32-.lq.AccountResourceSnapshot.UsedTitleSnapshot\x12\x18\n\x10\x63urrency_convert\x18\x05 \x01(\r\x1aX\n\x0f\x42\x61gItemSnapshot\x12\x13\n\x0bresource_id\x18\x01 \x01(\r\x12\x16\n\x0eresource_count\x18\x02 \x01(\r\x12\x18\n\x10resource_version\x18\x03 \x01(\r\x1a?\n\x10\x43urrencySnapshot\x12\x13\n\x0b\x63urrency_id\x18\x01 \x01(\r\x12\x16\n\x0e\x63urrency_count\x18\x02 \x01(\r\x1a#\n\rTitleSnapshot\x12\x12\n\ntitle_list\x18\x01 \x03(\r\x1a%\n\x11UsedTitleSnapshot\x12\x10\n\x08title_id\x18\x01 \x01(\r\"\xd8\x03\n\x18\x41\x63\x63ountCharacterSnapshot\x12\x1a\n\x12\x63reated_characters\x18\x01 \x03(\r\x12)\n\x12removed_characters\x18\x02 \x03(\x0b\x32\r.lq.Character\x12*\n\x13modified_characters\x18\x03 \x03(\x0b\x32\r.lq.Character\x12J\n\x0emain_character\x18\x04 \x01(\x0b\x32\x32.lq.AccountCharacterSnapshot.MainCharacterSnapshot\x12\x39\n\x05skins\x18\x05 \x01(\x0b\x32*.lq.AccountCharacterSnapshot.SkinsSnapshot\x12G\n\x11hidden_characters\x18\x06 \x01(\x0b\x32,.lq.AccountCharacterSnapshot.HiddenCharacter\x1a-\n\x15MainCharacterSnapshot\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x1a\"\n\rSkinsSnapshot\x12\x11\n\tskin_list\x18\x01 \x03(\r\x1a&\n\x0fHiddenCharacter\x12\x13\n\x0bhidden_list\x18\x01 \x03(\r\"\xc1\x02\n\x11\x41\x63\x63ountMailRecord\x12\x15\n\rcreated_mails\x18\x01 \x03(\r\x12\x39\n\rremoved_mails\x18\x02 \x03(\x0b\x32\".lq.AccountMailRecord.MailSnapshot\x12:\n\x0emodified_mails\x18\x03 \x03(\x0b\x32\".lq.AccountMailRecord.MailSnapshot\x1a\x9d\x01\n\x0cMailSnapshot\x12\x0f\n\x07mail_id\x18\x01 \x01(\r\x12\x14\n\x0creference_id\x18\x02 \x01(\r\x12\x13\n\x0b\x63reate_time\x18\x03 \x01(\r\x12\x13\n\x0b\x65xpire_time\x18\x04 \x01(\r\x12\x17\n\x0ftake_attachment\x18\x05 \x01(\r\x12#\n\x0b\x61ttachments\x18\x06 \x03(\x0b\x32\x0e.lq.RewardSlot\"\xb2\x02\n\x1a\x41\x63\x63ountAchievementSnapshot\x12-\n\x0c\x61\x63hievements\x18\x01 \x03(\x0b\x32\x17.lq.AchievementProgress\x12L\n\x0erewarded_group\x18\x02 \x01(\x0b\x32\x34.lq.AccountAchievementSnapshot.RewardedGroupSnapshot\x12\x42\n\x07version\x18\x03 \x01(\x0b\x32\x31.lq.AccountAchievementSnapshot.AchievementVersion\x1a,\n\x15RewardedGroupSnapshot\x12\x13\n\x0brewarded_id\x18\x01 \x01(\r\x1a%\n\x12\x41\x63hievementVersion\x12\x0f\n\x07version\x18\x01 \x01(\r\"\xe9\x07\n\x13\x41\x63\x63ountMiscSnapshot\x12!\n\nfaith_data\x18\x01 \x01(\x0b\x32\r.lq.FaithData\x12K\n\x11vip_reward_gained\x18\x02 \x01(\x0b\x32\x30.lq.AccountMiscSnapshot.AccountVIPRewardSnapshot\x12/\n\x03vip\x18\x03 \x01(\x0b\x32\".lq.AccountMiscSnapshot.AccountVIP\x12\x1f\n\tshop_info\x18\x04 \x01(\x0b\x32\x0c.lq.ShopInfo\x12H\n\x0cmonth_ticket\x18\x05 \x01(\x0b\x32\x32.lq.AccountMiscSnapshot.AccountMonthTicketSnapshot\x12>\n\trecharged\x18\x06 \x01(\x0b\x32+.lq.AccountMiscSnapshot.AccountRechargeInfo\x12M\n\x0fmonth_ticket_v2\x18\x07 \x01(\x0b\x32\x34.lq.AccountMiscSnapshot.AccountMonthTicketSnapshotV2\x1a,\n\x18\x41\x63\x63ountVIPRewardSnapshot\x12\x10\n\x08rewarded\x18\x01 \x03(\r\x1ar\n\x0fMonthTicketInfo\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\r\x12\x15\n\rlast_pay_time\x18\x03 \x01(\r\x12\x19\n\x11record_start_time\x18\x04 \x01(\r\x12\x0f\n\x07history\x18\x05 \x03(\r\x1aV\n\x1a\x41\x63\x63ountMonthTicketSnapshot\x12\x38\n\x07tickets\x18\x01 \x03(\x0b\x32\'.lq.AccountMiscSnapshot.MonthTicketInfo\x1a\x19\n\nAccountVIP\x12\x0b\n\x03vip\x18\x01 \x01(\r\x1a\xac\x01\n\x13\x41\x63\x63ountRechargeInfo\x12K\n\x07records\x18\x01 \x03(\x0b\x32:.lq.AccountMiscSnapshot.AccountRechargeInfo.RechargeRecord\x12\x10\n\x08has_data\x18\x02 \x01(\r\x1a\x36\n\x0eRechargeRecord\x12\r\n\x05level\x18\x01 \x01(\r\x12\x15\n\rrecharge_time\x18\x02 \x01(\r\x1as\n\x1c\x41\x63\x63ountMonthTicketSnapshotV2\x12\x10\n\x08\x65nd_time\x18\x01 \x01(\r\x12\x15\n\rlast_pay_time\x18\x02 \x01(\r\x12\x19\n\x11record_start_time\x18\x03 \x01(\r\x12\x0f\n\x07history\x18\x04 \x03(\r\"/\n\x15\x41\x63\x63ountGiftCodeRecord\x12\x16\n\x0eused_gift_code\x18\x01 \x03(\t\"\x96\x02\n\x05\x41\x63\x63Sn\x12-\n\x08resource\x18\x01 \x01(\x0b\x32\x1b.lq.AccountResourceSnapshot\x12/\n\tcharacter\x18\x02 \x01(\x0b\x32\x1c.lq.AccountCharacterSnapshot\x12#\n\x04mail\x18\x03 \x01(\x0b\x32\x15.lq.AccountMailRecord\x12\x33\n\x0b\x61\x63hievement\x18\x04 \x01(\x0b\x32\x1e.lq.AccountAchievementSnapshot\x12%\n\x04misc\x18\x05 \x01(\x0b\x32\x17.lq.AccountMiscSnapshot\x12,\n\tgift_code\x18\x06 \x01(\x0b\x32\x19.lq.AccountGiftCodeRecord\"=\n\x07\x41\x63\x63SnDa\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0c\n\x04time\x18\x02 \x01(\r\x12\x10\n\x08snapshot\x18\x03 \x01(\x0c\"e\n\x0fTransparentData\x12\x0e\n\x06method\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\x12\x0f\n\x07session\x18\x03 \x01(\t\x12#\n\x06remote\x18\x04 \x01(\x0b\x32\x13.lq.NetworkEndpoint\"&\n\nAmuletTile\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\"B\n\tAmuletFan\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03val\x18\x02 \x01(\x03\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\r\n\x05yiman\x18\x04 \x01(\x08\")\n\rAmuletReplace\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\"1\n\x0e\x41muletMingInfo\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x11\n\ttile_list\x18\x02 \x03(\r\"\x8b\x05\n\x18\x41muletActivityHookEffect\x12\x10\n\x08\x61\x64\x64_dora\x18\x01 \x03(\r\x12\x15\n\radd_tian_dora\x18\x03 \x03(\t\x12L\n\nadd_effect\x18\x04 \x03(\x0b\x32\x38.lq.AmuletActivityHookEffect.AmuletActivityAddHookEffect\x12\x15\n\rremove_effect\x18\x05 \x03(\r\x12\x10\n\x08\x61\x64\x64_buff\x18\x06 \x03(\r\x12\x13\n\x0bremove_buff\x18\x07 \x03(\r\x12\x10\n\x08\x61\x64\x64_coin\x18\t \x01(\x05\x12\'\n\x0ctile_replace\x18\x0b \x03(\x0b\x32\x11.lq.AmuletReplace\x12\x0f\n\x07\x61\x64\x64_fan\x18\x0c \x01(\t\x12\x10\n\x08\x61\x64\x64_base\x18\r \x01(\t\x12!\n\nmodify_fan\x18\x0e \x03(\x0b\x32\r.lq.AmuletFan\x12\n\n\x02id\x18\x0f \x01(\r\x12\x13\n\x0bmodify_dora\x18\x10 \x01(\x08\x12\x0b\n\x03uid\x18\x11 \x01(\r\x12\x15\n\radd_show_tile\x18\x12 \x03(\r\x12\x16\n\x0e\x61\x64\x64_dora_count\x18\x13 \x01(\x05\x12\x18\n\x10\x61\x64\x64_dora_no_hook\x18\x14 \x03(\r\x12\x18\n\x10\x61\x64\x64_coin_no_hook\x18\x15 \x01(\x05\x12\x33\n\x0e\x61\x64\x64_tile_score\x18\x16 \x03(\x0b\x32\x1b.lq.AmuletGameTileScoreData\x12;\n\x16\x61\x64\x64_tile_score_no_hook\x18\x17 \x03(\x0b\x32\x1b.lq.AmuletGameTileScoreData\x1a\x36\n\x1b\x41muletActivityAddHookEffect\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03uid\x18\x02 \x01(\r\"i\n\x0e\x41muletHuleInfo\x12\x0c\n\x04tile\x18\x01 \x01(\r\x12\x1f\n\x08\x66\x61n_list\x18\x02 \x03(\x0b\x32\r.lq.AmuletFan\x12\x0b\n\x03\x66\x61n\x18\x03 \x01(\t\x12\r\n\x05point\x18\x04 \x01(\t\x12\x0c\n\x04\x62\x61se\x18\x05 \x01(\t\"\x97\x01\n\x17\x41muletHuleOperateResult\x12$\n\x08hu_final\x18\x02 \x01(\x0b\x32\x12.lq.AmuletHuleInfo\x12#\n\x07hu_base\x18\x03 \x01(\x0b\x32\x12.lq.AmuletHuleInfo\x12\x31\n\x0bhook_effect\x18\x05 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\"^\n\x17\x41muletGangOperateResult\x12\x10\n\x08new_dora\x18\x04 \x03(\r\x12\x31\n\x0bhook_effect\x18\x05 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\"W\n\x14\x41muletDealTileResult\x12\x0c\n\x04tile\x18\x01 \x01(\r\x12\x31\n\x0bhook_effect\x18\x05 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\"Z\n\x17\x41muletDiscardTileResult\x12\x0c\n\x04tile\x18\x01 \x01(\r\x12\x31\n\x0bhook_effect\x18\x05 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\"J\n\x15\x41muletStartGameResult\x12\x31\n\x0bhook_effect\x18\x05 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\"\xa7\x01\n\x11\x41muletRoundResult\x12.\n\thu_result\x18\x02 \x01(\x0b\x32\x1b.lq.AmuletHuleOperateResult\x12-\n\x0b\x64\x65\x61l_result\x18\x04 \x01(\x0b\x32\x18.lq.AmuletDealTileResult\x12\x33\n\x0e\x64iscard_result\x18\x05 \x01(\x0b\x32\x1b.lq.AmuletDiscardTileResult\"\xd9\x01\n\x13\x41muletUpgradeResult\x12,\n\rremain_rounds\x18\x01 \x03(\x0b\x32\x15.lq.AmuletRoundResult\x12\x12\n\npoint_coin\x18\x02 \x01(\r\x12\x12\n\nlevel_coin\x18\x03 \x01(\r\x12$\n\x04shop\x18\x04 \x01(\x0b\x32\x16.lq.AmuletGameShopData\x12\x31\n\x0bhook_effect\x18\x05 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\x12\x13\n\x0breward_pack\x18\x06 \x03(\r\"9\n\x13QuestionnaireReward\x12\x13\n\x0bresource_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\xc8\x02\n\x13QuestionnaireDetail\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\nversion_id\x18\x02 \x01(\r\x12\x1c\n\x14\x65\x66\x66\x65\x63tive_time_start\x18\x03 \x01(\r\x12\x1a\n\x12\x65\x66\x66\x65\x63tive_time_end\x18\x04 \x01(\r\x12(\n\x07rewards\x18\x05 \x03(\x0b\x32\x17.lq.QuestionnaireReward\x12\x14\n\x0c\x62\x61nner_title\x18\x06 \x01(\t\x12\r\n\x05title\x18\x07 \x01(\t\x12\x1a\n\x12\x61nnouncement_title\x18\x08 \x01(\t\x12\x1c\n\x14\x61nnouncement_content\x18\t \x01(\t\x12\x12\n\nfinal_text\x18\n \x01(\t\x12,\n\tquestions\x18\x0b \x03(\x0b\x32\x19.lq.QuestionnaireQuestion\x12\x0c\n\x04type\x18\x0c \x01(\r\"\xdc\x05\n\x15QuestionnaireQuestion\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05title\x18\x02 \x01(\t\x12\x10\n\x08\x64\x65scribe\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x10\n\x08sub_type\x18\x05 \x01(\t\x12\x39\n\x07options\x18\x06 \x03(\x0b\x32(.lq.QuestionnaireQuestion.QuestionOption\x12\x1a\n\x12option_random_sort\x18\x07 \x01(\x08\x12\x0f\n\x07require\x18\x08 \x01(\x08\x12\x12\n\nmax_choice\x18\t \x01(\r\x12\x41\n\rnext_question\x18\n \x03(\x0b\x32*.lq.QuestionnaireQuestion.NextQuestionData\x12\x12\n\nmatrix_row\x18\x0b \x03(\t\x12 \n\x18option_random_sort_index\x18\x0c \x01(\x05\x1a\x43\n\x0eQuestionOption\x12\r\n\x05label\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\x13\n\x0b\x61llow_input\x18\x03 \x01(\x08\x1a\xbb\x02\n\x10NextQuestionData\x12\x1a\n\x12target_question_id\x18\x01 \x01(\r\x12W\n\nconditions\x18\n \x03(\x0b\x32\x43.lq.QuestionnaireQuestion.NextQuestionData.QuestionconditionWrapper\x1a\x44\n\x11QuestionCondition\x12\x13\n\x0bquestion_id\x18\x01 \x01(\r\x12\n\n\x02op\x18\x02 \x01(\t\x12\x0e\n\x06values\x18\x03 \x03(\t\x1al\n\x18QuestionconditionWrapper\x12P\n\nconditions\x18\x01 \x03(\x0b\x32<.lq.QuestionnaireQuestion.NextQuestionData.QuestionCondition\"\xcb\x01\n\x12QuestionnaireBrief\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\nversion_id\x18\x02 \x01(\r\x12\x1c\n\x14\x65\x66\x66\x65\x63tive_time_start\x18\x03 \x01(\r\x12\x1a\n\x12\x65\x66\x66\x65\x63tive_time_end\x18\x04 \x01(\r\x12(\n\x07rewards\x18\x05 \x03(\x0b\x32\x17.lq.QuestionnaireReward\x12\x14\n\x0c\x62\x61nner_title\x18\x06 \x01(\t\x12\r\n\x05title\x18\x07 \x01(\t\x12\x0c\n\x04type\x18\x08 \x01(\r\"X\n\nSeerReport\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x1d\n\x06\x65vents\x18\x02 \x03(\x0b\x32\r.lq.SeerEvent\x12\x1d\n\x06rounds\x18\x03 \x03(\x0b\x32\r.lq.SeerRound\"\\\n\tSeerEvent\x12\x14\n\x0crecord_index\x18\x01 \x01(\x05\x12\x12\n\nseer_index\x18\x02 \x01(\x05\x12%\n\nrecommends\x18\x03 \x03(\x0b\x32\x11.lq.SeerRecommend\"F\n\rSeerRecommend\x12\x0c\n\x04seat\x18\x01 \x01(\x05\x12\'\n\x0bpredictions\x18\x02 \x03(\x0b\x32\x12.lq.SeerPrediction\"/\n\x0eSeerPrediction\x12\x0e\n\x06\x61\x63tion\x18\x01 \x01(\x05\x12\r\n\x05score\x18\x02 \x01(\x05\"Y\n\tSeerRound\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12$\n\rplayer_scores\x18\x04 \x03(\x0b\x32\r.lq.SeerScore\")\n\tSeerScore\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0e\n\x06rating\x18\x02 \x01(\r\"x\n\tSeerBrief\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\r\n\x05state\x18\x02 \x01(\r\x12\x13\n\x0b\x65xpire_time\x18\x03 \x01(\r\x12$\n\rplayer_scores\x18\x04 \x03(\x0b\x32\r.lq.SeerScore\x12\x13\n\x0b\x63reate_time\x18\x05 \x01(\r\"\xf0\x02\n\x16SimulationV2SeasonData\x12\r\n\x05round\x18\x01 \x01(\r\x12(\n\x07\x61\x62ility\x18\x02 \x01(\x0b\x32\x17.lq.SimulationV2Ability\x12+\n\x0b\x65\x66\x66\x65\x63t_list\x18\x03 \x03(\x0b\x32\x16.lq.SimulationV2Effect\x12$\n\x05match\x18\x04 \x01(\x0b\x32\x15.lq.SimulationV2Match\x12$\n\x05\x65vent\x18\x05 \x01(\x0b\x32\x15.lq.SimulationV2Event\x12\x33\n\revent_history\x18\x06 \x03(\x0b\x32\x1c.lq.SimulationV2EventHistory\x12&\n\x06record\x18\x07 \x01(\x0b\x32\x16.lq.SimulationV2Record\x12\x13\n\x0btotal_score\x18\x08 \x01(\x05\x12\x32\n\rmatch_history\x18\t \x03(\x0b\x32\x1b.lq.SimulationV2MatchRecord\"_\n\x18SimulationV2PlayerRecord\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04main\x18\x02 \x01(\x08\x12\r\n\x05score\x18\x03 \x01(\x05\x12\x0c\n\x04rank\x18\x04 \x01(\r\x12\x0c\n\x04seat\x18\x05 \x01(\r\"W\n\x17SimulationV2MatchRecord\x12-\n\x07players\x18\x01 \x03(\x0b\x32\x1c.lq.SimulationV2PlayerRecord\x12\r\n\x05round\x18\x02 \x01(\r\"5\n\x18SimulationV2EventHistory\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05round\x18\x02 \x01(\r\"\xb6\x02\n\x11SimulationV2Event\x12\n\n\x02id\x18\x01 \x01(\r\x12\x44\n\nselections\x18\x02 \x03(\x0b\x32\x30.lq.SimulationV2Event.SimulationV2EventSelection\x12\x12\n\nnext_round\x18\x03 \x01(\r\x1a\xba\x01\n\x1aSimulationV2EventSelection\x12\n\n\x02id\x18\x01 \x01(\r\x12Y\n\x07results\x18\x02 \x03(\x0b\x32H.lq.SimulationV2Event.SimulationV2EventSelection.SimulationV2EventResult\x1a\x35\n\x17SimulationV2EventResult\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06weight\x18\x02 \x01(\r\"V\n\x13SimulationV2Ability\x12\x0b\n\x03luk\x18\x01 \x01(\r\x12\x0b\n\x03tec\x18\x02 \x01(\r\x12\x0b\n\x03ins\x18\x03 \x01(\r\x12\x0b\n\x03int\x18\x04 \x01(\r\x12\x0b\n\x03res\x18\x05 \x01(\r\"<\n\x10SimulationV2Buff\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05round\x18\x02 \x01(\r\x12\r\n\x05store\x18\x03 \x03(\r\" \n\x12SimulationV2Effect\x12\n\n\x02id\x18\x01 \x01(\r\"]\n\x15SimulationV2MatchInfo\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x0c\n\x04gong\x18\x04 \x01(\r\x12\x0e\n\x06remain\x18\x05 \x01(\r\"r\n\x12SimulationV2Record\x12\x10\n\x08hu_count\x18\x01 \x01(\r\x12\x13\n\x0b\x63hong_count\x18\x02 \x01(\r\x12\x12\n\nhighest_hu\x18\x03 \x01(\r\x12\x0c\n\x04rank\x18\x04 \x03(\r\x12\x13\n\x0bround_count\x18\x05 \x01(\r\"\xe6\x07\n\x18SimulationV2MatchHistory\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0e\n\x06remain\x18\x02 \x01(\r\x12\x14\n\x0cscore_modify\x18\x03 \x03(\x05\x12@\n\x0bround_start\x18\x04 \x01(\x0b\x32+.lq.SimulationV2MatchHistory.RoundStartArgs\x12\x37\n\x06riichi\x18\x05 \x01(\x0b\x32\'.lq.SimulationV2MatchHistory.RiichiArgs\x12\x33\n\x04\x66ulu\x18\x06 \x01(\x0b\x32%.lq.SimulationV2MatchHistory.FuluArgs\x12\x33\n\x04hule\x18\x07 \x03(\x0b\x32%.lq.SimulationV2MatchHistory.HuleArgs\x12<\n\tpush_ting\x18\x08 \x01(\x0b\x32).lq.SimulationV2MatchHistory.PushTingArgs\x12<\n\tfind_ting\x18\t \x01(\x0b\x32).lq.SimulationV2MatchHistory.FindTingArgs\x12\x35\n\x05liuju\x18\n \x01(\x0b\x32&.lq.SimulationV2MatchHistory.LiujuArgs\x12\x35\n\x05story\x18\x0b \x01(\x0b\x32&.lq.SimulationV2MatchHistory.StoryArgs\x1as\n\x0eRoundStartArgs\x12\'\n\x04info\x18\x01 \x01(\x0b\x32\x19.lq.SimulationV2MatchInfo\x12\x0e\n\x06scores\x18\x02 \x03(\x05\x12\x0c\n\x04ting\x18\x03 \x01(\r\x12\x1a\n\x12\x65\x66\x66\x65\x63ted_buff_list\x18\x04 \x03(\r\x1a\x1a\n\nRiichiArgs\x12\x0c\n\x04seat\x18\x01 \x01(\r\x1a\x34\n\x08\x46uluArgs\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04ting\x18\x02 \x01(\r\x12\x0c\n\x04\x66ulu\x18\x03 \x01(\r\x1al\n\x08HuleArgs\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04zimo\x18\x02 \x01(\x08\x12\x12\n\nchong_seat\x18\x03 \x01(\r\x12\r\n\x05point\x18\x04 \x01(\x05\x12\x0b\n\x03\x66\x61n\x18\x05 \x01(\x05\x12\x14\n\x0cscore_modify\x18\x06 \x03(\x05\x1a*\n\x0cPushTingArgs\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04ting\x18\x02 \x01(\r\x1a,\n\x0c\x46indTingArgs\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0e\n\x06target\x18\x02 \x01(\r\x1a\x19\n\tLiujuArgs\x12\x0c\n\x04ting\x18\x01 \x03(\r\x1a\x1d\n\tStoryArgs\x12\x10\n\x08story_id\x18\x01 \x01(\r\"\xd2\x04\n\x11SimulationV2Match\x12\'\n\x04info\x18\x01 \x01(\x0b\x32\x19.lq.SimulationV2MatchInfo\x12\x39\n\x07players\x18\x02 \x03(\x0b\x32(.lq.SimulationV2Match.SimulationV2Player\x12-\n\x07history\x18\x03 \x03(\x0b\x32\x1c.lq.SimulationV2MatchHistory\x12\x0c\n\x04rank\x18\x04 \x03(\r\x12\x14\n\x0cis_match_end\x18\x05 \x01(\x08\x12)\n\x07\x61\x63tions\x18\x06 \x03(\x0b\x32\x18.lq.SimulationActionData\x12\'\n\tbuff_list\x18\t \x03(\x0b\x32\x14.lq.SimulationV2Buff\x12\x16\n\x0eis_first_round\x18\n \x01(\x08\x12\x19\n\x11last_event_remain\x18\x0b \x01(\r\x12\x1a\n\x12\x65\x66\x66\x65\x63ted_buff_list\x18\x0c \x03(\r\x12\x17\n\x0ftriggered_story\x18\r \x03(\r\x1a\xc9\x01\n\x12SimulationV2Player\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04main\x18\x02 \x01(\x08\x12\x0c\n\x04ting\x18\x04 \x01(\r\x12\r\n\x05score\x18\x05 \x01(\x05\x12\x0c\n\x04\x66ulu\x18\x06 \x01(\r\x12\x0e\n\x06riichi\x18\x07 \x01(\x08\x12\x11\n\tfind_ting\x18\x08 \x03(\r\x12\x0c\n\x04seat\x18\t \x01(\r\x12\x15\n\rcon_push_ting\x18\n \x01(\r\x12\x15\n\rcon_keep_ting\x18\x0b \x01(\r\x12\x0f\n\x07ippatsu\x18\x0c \x01(\x08\"\xae\x05\n\x14SimulationActionData\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x39\n\x06riichi\x18\x02 \x01(\x0b\x32).lq.SimulationActionData.ActionRiichiData\x12\x35\n\x04hule\x18\x03 \x01(\x0b\x32\'.lq.SimulationActionData.ActionHuleData\x12\x35\n\x04\x66ulu\x18\x04 \x01(\x0b\x32\'.lq.SimulationActionData.ActionFuluData\x12@\n\x0c\x64iscard_tile\x18\x05 \x01(\x0b\x32*.lq.SimulationActionData.ActionDiscardData\x12>\n\tdeal_tile\x18\x06 \x01(\x0b\x32+.lq.SimulationActionData.ActionDealTileData\x1a \n\x10\x41\x63tionRiichiData\x12\x0c\n\x04seat\x18\x01 \x01(\r\x1a\xc3\x01\n\x0e\x41\x63tionHuleData\x12>\n\x04hule\x18\x01 \x03(\x0b\x32\x30.lq.SimulationActionData.ActionHuleData.HuleInfo\x1aq\n\x08HuleInfo\x12\x0b\n\x03\x66\x61n\x18\x01 \x01(\r\x12\x0c\n\x04zimo\x18\x02 \x01(\x08\x12\r\n\x05point\x18\x03 \x01(\r\x12\x0b\n\x03oya\x18\x04 \x01(\x08\x12\x0e\n\x06player\x18\x05 \x01(\r\x12\r\n\x05\x63hong\x18\x06 \x01(\r\x12\x0f\n\x07toutiao\x18\x07 \x01(\x08\x1a\x1e\n\x0e\x41\x63tionFuluData\x12\x0c\n\x04seat\x18\x01 \x01(\r\x1a\x31\n\x11\x41\x63tionDiscardData\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0e\n\x06riichi\x18\x02 \x01(\x08\x1a\"\n\x12\x41\x63tionDealTileData\x12\x0c\n\x04seat\x18\x01 \x01(\r\"[\n\x11ResConnectionInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12,\n\x0f\x63lient_endpoint\x18\x02 \x01(\x0b\x32\x13.lq.NetworkEndpoint\"K\n\x11ResFetchQueueInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0e\n\x06remain\x18\x02 \x01(\r\x12\x0c\n\x04rank\x18\x03 \x01(\r\"-\n\x0eReqOpenidCheck\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05token\x18\x02 \x01(\t\"\xa3\x01\n\x10ReqSignupAccount\x12\x0f\n\x07\x61\x63\x63ount\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\r\x12$\n\x06\x64\x65vice\x18\x05 \x01(\x0b\x32\x14.lq.ClientDeviceInfo\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\x12\x0b\n\x03tag\x18\x07 \x01(\t\",\n\x10ResSignupAccount\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"\xaa\x02\n\x08ReqLogin\x12\x0f\n\x07\x61\x63\x63ount\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x11\n\treconnect\x18\x03 \x01(\x08\x12$\n\x06\x64\x65vice\x18\x04 \x01(\x0b\x32\x14.lq.ClientDeviceInfo\x12\x12\n\nrandom_key\x18\x05 \x01(\t\x12-\n\x0e\x63lient_version\x18\x06 \x01(\x0b\x32\x15.lq.ClientVersionInfo\x12\x18\n\x10gen_access_token\x18\x07 \x01(\x08\x12\x1a\n\x12\x63urrency_platforms\x18\x08 \x03(\r\x12\x0c\n\x04type\x18\t \x01(\r\x12\x0f\n\x07version\x18\n \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x0b \x01(\t\x12\x0b\n\x03tag\x18\x0c \x01(\t\"\xa9\x02\n\x08ResLogin\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x1c\n\x07\x61\x63\x63ount\x18\x03 \x01(\x0b\x32\x0b.lq.Account\x12&\n\tgame_info\x18\x04 \x01(\x0b\x32\x13.lq.GameConnectInfo\x12\x1f\n\x17has_unread_announcement\x18\x05 \x01(\x08\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x06 \x01(\t\x12\x13\n\x0bsignup_time\x18\x07 \x01(\r\x12\x19\n\x11is_id_card_authed\x18\x08 \x01(\x08\x12\x0f\n\x07\x63ountry\x18\t \x01(\t\x12\x17\n\x0flogined_version\x18\n \x03(\r\x12\x18\n\x10rewarded_version\x18\x0b \x03(\r\"5\n\x0fReqPrepareLogin\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\r\"h\n\x0cResFastLogin\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12&\n\tgame_info\x18\x02 \x01(\x0b\x32\x13.lq.GameConnectInfo\x12\x16\n\x04room\x18\x03 \x01(\x0b\x32\x08.lq.Room\"\xcb\x01\n\rReqEmailLogin\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x11\n\treconnect\x18\x03 \x01(\x08\x12$\n\x06\x64\x65vice\x18\x04 \x01(\x0b\x32\x14.lq.ClientDeviceInfo\x12\x12\n\nrandom_key\x18\x05 \x01(\t\x12\x16\n\x0e\x63lient_version\x18\x06 \x01(\t\x12\x18\n\x10gen_access_token\x18\x07 \x01(\x08\x12\x1a\n\x12\x63urrency_platforms\x18\x08 \x03(\r\"3\n\x0eReqBindAccount\x12\x0f\n\x07\x61\x63\x63ount\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\"8\n\x18ReqCreatePhoneVerifyCode\x12\r\n\x05phone\x18\x01 \x01(\t\x12\r\n\x05usage\x18\x02 \x01(\r\"8\n\x18ReqCreateEmailVerifyCode\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\r\n\x05usage\x18\x02 \x01(\r\"9\n\x16ReqVerifyCodeForSecure\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x11\n\toperation\x18\x02 \x01(\r\"H\n\x16ResVerfiyCodeForSecure\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x14\n\x0csecure_token\x18\x02 \x01(\t\"_\n\x12ReqBindPhoneNumber\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\r\n\x05phone\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\x12\x1a\n\x12multi_bind_version\x18\x04 \x01(\x08\"E\n\x14ReqUnbindPhoneNumber\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\r\n\x05phone\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\"G\n\x16ResFetchPhoneLoginBind\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bphone_login\x18\x02 \x01(\r\"+\n\x17ReqCreatePhoneLoginBind\x12\x10\n\x08password\x18\x01 \x01(\t\"=\n\x0cReqBindEmail\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\"U\n\x11ReqModifyPassword\x12\x14\n\x0cnew_password\x18\x01 \x01(\t\x12\x14\n\x0cold_password\x18\x02 \x01(\t\x12\x14\n\x0csecure_token\x18\x03 \x01(\t\"W\n\rReqOauth2Auth\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0b\n\x03uid\x18\x03 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x04 \x01(\t\"?\n\rResOauth2Auth\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x02 \x01(\t\"4\n\x0eReqOauth2Check\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x02 \x01(\t\"?\n\x0eResOauth2Check\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bhas_account\x18\x02 \x01(\x08\"\xdc\x01\n\x0fReqOauth2Signup\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x15\n\radvertise_str\x18\x04 \x01(\t\x12$\n\x06\x64\x65vice\x18\x05 \x01(\x0b\x32\x14.lq.ClientDeviceInfo\x12-\n\x0e\x63lient_version\x18\x06 \x01(\x0b\x32\x15.lq.ClientVersionInfo\x12\x1d\n\x15\x63lient_version_string\x18\x07 \x01(\t\x12\x0b\n\x03tag\x18\x08 \x01(\t\"+\n\x0fResOauth2Signup\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"\xa3\x02\n\x0eReqOauth2Login\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x02 \x01(\t\x12\x11\n\treconnect\x18\x03 \x01(\x08\x12$\n\x06\x64\x65vice\x18\x04 \x01(\x0b\x32\x14.lq.ClientDeviceInfo\x12\x12\n\nrandom_key\x18\x05 \x01(\t\x12-\n\x0e\x63lient_version\x18\x06 \x01(\x0b\x32\x15.lq.ClientVersionInfo\x12\x18\n\x10gen_access_token\x18\x07 \x01(\x08\x12\x1a\n\x12\x63urrency_platforms\x18\x08 \x03(\r\x12\x0f\n\x07version\x18\t \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\n \x01(\t\x12\x0b\n\x03tag\x18\x0b \x01(\t\"$\n\x0eReqDMMPreLogin\x12\x12\n\nfinish_url\x18\x01 \x01(\t\"=\n\x0eResDMMPreLogin\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12\x11\n\tparameter\x18\x01 \x01(\t\"\x0b\n\tReqLogout\"%\n\tResLogout\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"+\n\x0bReqHeatBeat\x12\x1c\n\x14no_operation_counter\x18\x01 \x01(\r\")\n\x1aReqSearchAccountByEidLobby\x12\x0b\n\x03\x65id\x18\x01 \x01(\r\"J\n\x1aResSearchAccountbyEidLobby\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\naccount_id\x18\x02 \x01(\r\" \n\x0cReqLoginBeat\x12\x10\n\x08\x63ontract\x18\x01 \x01(\t\"F\n\x11ReqJoinMatchQueue\x12\x12\n\nmatch_mode\x18\x01 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x02 \x01(\t\")\n\x13ReqCancelMatchQueue\x12\x12\n\nmatch_mode\x18\x01 \x01(\r\"$\n\x0eReqAccountInfo\x12\x12\n\naccount_id\x18\x01 \x01(\r\"`\n\x0eResAccountInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1c\n\x07\x61\x63\x63ount\x18\x02 \x01(\x0b\x32\x0b.lq.Account\x12\x16\n\x04room\x18\x03 \x01(\x0b\x32\x08.lq.Room\"I\n\x11ReqCreateNickname\x12\x10\n\x08nickname\x18\x01 \x01(\t\x12\x15\n\radvertise_str\x18\x02 \x01(\t\x12\x0b\n\x03tag\x18\x03 \x01(\t\":\n\x11ReqModifyNickname\x12\x10\n\x08nickname\x18\x01 \x01(\t\x12\x13\n\x0buse_item_id\x18\x02 \x01(\r\"%\n\x11ReqModifyBirthday\x12\x10\n\x08\x62irthday\x18\x01 \x01(\x05\"?\n\x0bResSelfRoom\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x16\n\x04room\x18\x02 \x01(\x0b\x32\x08.lq.Room\"V\n\x12ResFetchGamingInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12&\n\tgame_info\x18\x02 \x01(\x0b\x32\x13.lq.GameConnectInfo\"\x87\x01\n\rReqCreateRoom\x12\x14\n\x0cplayer_count\x18\x01 \x01(\r\x12\x1a\n\x04mode\x18\x02 \x01(\x0b\x32\x0c.lq.GameMode\x12\x13\n\x0bpublic_live\x18\x03 \x01(\x08\x12\x1d\n\x15\x63lient_version_string\x18\x04 \x01(\t\x12\x10\n\x08pre_rule\x18\x05 \x01(\t\"A\n\rResCreateRoom\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x16\n\x04room\x18\x02 \x01(\x0b\x32\x08.lq.Room\"=\n\x0bReqJoinRoom\x12\x0f\n\x07room_id\x18\x01 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x02 \x01(\t\"?\n\x0bResJoinRoom\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x16\n\x04room\x18\x02 \x01(\x0b\x32\x08.lq.Room\"\x1d\n\x0cReqRoomReady\x12\r\n\x05ready\x18\x01 \x01(\x08\"#\n\x0fReqRoomDressing\x12\x10\n\x08\x64ressing\x18\x01 \x01(\x08\"\x0e\n\x0cReqRoomStart\"\x1f\n\x11ReqRoomKickPlayer\x12\n\n\x02id\x18\x01 \x01(\r\"$\n\rReqModifyRoom\x12\x13\n\x0brobot_count\x18\x01 \x01(\r\"#\n\x0fReqAddRoomRobot\x12\x10\n\x08position\x18\x01 \x01(\r\"$\n\x0fReqChangeAvatar\x12\x11\n\tavatar_id\x18\x01 \x01(\r\"-\n\x17ReqAccountStatisticInfo\x12\x12\n\naccount_id\x18\x01 \x01(\r\"\x98\x01\n\x17ResAccountStatisticInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x30\n\x0estatistic_data\x18\x02 \x03(\x0b\x32\x18.lq.AccountStatisticData\x12\x31\n\x0b\x64\x65tail_data\x18\x03 \x01(\x0b\x32\x1c.lq.AccountDetailStatisticV2\"\xb9\x01\n\x1bResAccountChallengeRankInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x42\n\x0bseason_info\x18\x02 \x03(\x0b\x32-.lq.ResAccountChallengeRankInfo.ChallengeRank\x1a<\n\rChallengeRank\x12\x0e\n\x06season\x18\x01 \x01(\r\x12\x0c\n\x04rank\x18\x02 \x01(\r\x12\r\n\x05level\x18\x03 \x01(\r\"H\n\x17ResAccountCharacterInfo\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bunlock_list\x18\x01 \x03(\r\"+\n\x0fReqShopPurchase\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\r\"N\n\x0fResShopPurchase\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12!\n\x06update\x18\x02 \x01(\x0b\x32\x11.lq.AccountUpdate\"A\n\rReqGameRecord\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x02 \x01(\t\"g\n\rResGameRecord\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1c\n\x04head\x18\x03 \x01(\x0b\x32\x0e.lq.RecordGame\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\x0c\x12\x10\n\x08\x64\x61ta_url\x18\x05 \x01(\t\"?\n\x11ReqGameRecordList\x12\r\n\x05start\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\"g\n\x11ResGameRecordList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0btotal_count\x18\x02 \x01(\r\x12#\n\x0brecord_list\x18\x03 \x03(\x0b\x32\x0e.lq.RecordGame\"\x8f\x01\n\x13ReqGameRecordListV2\x12\x0b\n\x03tag\x18\x01 \x01(\r\x12\x12\n\nbegin_time\x18\x02 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\r\x12\r\n\x05ranks\x18\x04 \x03(\r\x12\r\n\x05modes\x18\x05 \x03(\r\x12\x13\n\x0bmax_hu_type\x18\x06 \x01(\r\x12\x12\n\nlevel_mode\x18\x07 \x03(\r\"\x8e\x01\n\x13ResGameRecordListV2\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08iterator\x18\x02 \x01(\t\x12\x17\n\x0fiterator_expire\x18\x03 \x01(\r\x12\x19\n\x11\x61\x63tual_begin_time\x18\x04 \x01(\r\x12\x17\n\x0f\x61\x63tual_end_time\x18\x05 \x01(\r\"8\n\x15ReqNextGameRecordList\x12\x10\n\x08iterator\x18\x01 \x01(\t\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\x95\x01\n\x15ResNextGameRecordList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0c\n\x04next\x18\x02 \x01(\x08\x12$\n\x07\x65ntries\x18\x03 \x03(\x0b\x32\x13.lq.RecordListEntry\x12\x17\n\x0fiterator_expire\x18\x04 \x01(\r\x12\x15\n\rnext_end_time\x18\x05 \x01(\r\"\x82\x01\n\x1aResCollectedGameRecordList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12,\n\x0brecord_list\x18\x02 \x03(\x0b\x32\x17.lq.RecordCollectedData\x12\x1c\n\x14record_collect_limit\x18\x03 \x01(\r\")\n\x14ReqGameRecordsDetail\x12\x11\n\tuuid_list\x18\x01 \x03(\t\"U\n\x14ResGameRecordsDetail\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12#\n\x0brecord_list\x18\x02 \x03(\x0b\x32\x0e.lq.RecordGame\"+\n\x16ReqGameRecordsDetailV2\x12\x11\n\tuuid_list\x18\x01 \x03(\t\"X\n\x16ResGameRecordsDetailV2\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x07\x65ntries\x18\x02 \x03(\x0b\x32\x13.lq.RecordListEntry\"`\n\x19ReqAddCollectedGameRecord\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x0f\n\x07remarks\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x04 \x01(\r\"5\n\x19ResAddCollectedGameRecord\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\",\n\x1cReqRemoveCollectedGameRecord\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"8\n\x1cResRemoveCollectedGameRecord\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"D\n#ReqChangeCollectedGameRecordRemarks\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x0f\n\x07remarks\x18\x02 \x01(\t\"?\n#ResChangeCollectedGameRecordRemarks\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"#\n\x13ReqLevelLeaderboard\x12\x0c\n\x04type\x18\x01 \x01(\r\"\xac\x01\n\x13ResLevelLeaderboard\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12+\n\x05items\x18\x02 \x03(\x0b\x32\x1c.lq.ResLevelLeaderboard.Item\x12\x11\n\tself_rank\x18\x03 \x01(\r\x1a;\n\x04Item\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x1f\n\x05level\x18\x02 \x01(\x0b\x32\x10.lq.AccountLevel\")\n\x17ReqChallangeLeaderboard\x12\x0e\n\x06season\x18\x01 \x01(\r\"\xb4\x01\n\x17ResChallengeLeaderboard\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12/\n\x05items\x18\x02 \x03(\x0b\x32 .lq.ResChallengeLeaderboard.Item\x12\x11\n\tself_rank\x18\x03 \x01(\r\x1a;\n\x04Item\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12\x10\n\x08nickname\x18\x03 \x01(\t\"@\n\x15ReqMutiChallengeLevel\x12\x17\n\x0f\x61\x63\x63ount_id_list\x18\x01 \x03(\r\x12\x0e\n\x06season\x18\x02 \x01(\r\"\x8b\x01\n\x15ResMutiChallengeLevel\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12-\n\x05items\x18\x02 \x03(\x0b\x32\x1e.lq.ResMutiChallengeLevel.Item\x1a)\n\x04Item\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\",\n\x11ReqMultiAccountId\x12\x17\n\x0f\x61\x63\x63ount_id_list\x18\x01 \x03(\r\"U\n\x14ResMultiAccountBrief\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12#\n\x07players\x18\x02 \x03(\x0b\x32\x12.lq.PlayerBaseView\"v\n\rResFriendList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1b\n\x07\x66riends\x18\x02 \x03(\x0b\x32\n.lq.Friend\x12\x18\n\x10\x66riend_max_count\x18\x03 \x01(\r\x12\x14\n\x0c\x66riend_count\x18\x04 \x01(\r\"\x9a\x01\n\x12ResFriendApplyList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x33\n\x07\x61pplies\x18\x02 \x03(\x0b\x32\".lq.ResFriendApplyList.FriendApply\x1a\x35\n\x0b\x46riendApply\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x12\n\napply_time\x18\x02 \x01(\r\"#\n\x0eReqApplyFriend\x12\x11\n\ttarget_id\x18\x01 \x01(\r\"9\n\x14ReqHandleFriendApply\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12\x0e\n\x06method\x18\x02 \x01(\r\"$\n\x0fReqRemoveFriend\x12\x11\n\ttarget_id\x18\x01 \x01(\r\"A\n\x19ReqSearchAccountByPattern\x12\x13\n\x0bsearch_next\x18\x01 \x01(\x08\x12\x0f\n\x07pattern\x18\x02 \x01(\t\"u\n\x19ResSearchAccountByPattern\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bis_finished\x18\x02 \x01(\x08\x12\x16\n\x0ematch_accounts\x18\x03 \x03(\r\x12\x11\n\tdecode_id\x18\x04 \x01(\r\")\n\x0eReqAccountList\x12\x17\n\x0f\x61\x63\x63ount_id_list\x18\x01 \x03(\r\"T\n\x10ResAccountStates\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12&\n\x06states\x18\x02 \x03(\x0b\x32\x16.lq.AccountActiveState\"*\n\x14ReqSearchAccountById\x12\x12\n\naccount_id\x18\x01 \x01(\r\"T\n\x14ResSearchAccountById\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x06player\x18\x02 \x01(\x0b\x32\x12.lq.PlayerBaseView\"<\n\nResBagInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x14\n\x03\x62\x61g\x18\x02 \x01(\x0b\x32\x07.lq.Bag\" \n\rReqUseBagItem\x12\x0f\n\x07item_id\x18\x01 \x01(\r\"F\n\x11ReqOpenManualItem\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x11\n\tselect_id\x18\x03 \x01(\r\"9\n\x17ReqOpenRandomRewardItem\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"T\n\x17ResOpenRandomRewardItem\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\x07results\x18\x02 \x03(\x0b\x32\x0e.lq.OpenResult\"\'\n\x14ReqOpenAllRewardItem\x12\x0f\n\x07item_id\x18\x01 \x01(\r\"Q\n\x14ResOpenAllRewardItem\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\x07results\x18\x02 \x03(\x0b\x32\x0e.lq.OpenResult\"\"\n\x0fReqComposeShard\x12\x0f\n\x07item_id\x18\x01 \x01(\r\"6\n\x14ReqFetchAnnouncement\x12\x0c\n\x04lang\x18\x01 \x01(\t\x12\x10\n\x08platform\x18\x02 \x01(\t\"u\n\x0fResAnnouncement\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\rannouncements\x18\x02 \x03(\x0b\x32\x10.lq.Announcement\x12\x0c\n\x04sort\x18\x03 \x03(\r\x12\x11\n\tread_list\x18\x04 \x03(\r\"@\n\x0bResMailInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x17\n\x05mails\x18\x02 \x03(\x0b\x32\x08.lq.Mail\"\x1e\n\x0bReqReadMail\x12\x0f\n\x07mail_id\x18\x01 \x01(\r\" \n\rReqDeleteMail\x12\x0f\n\x07mail_id\x18\x01 \x01(\r\"$\n\x11ReqTakeAttachment\x12\x0f\n\x07mail_id\x18\x01 \x01(\r\"4\n ReqReceiveAchievementGroupReward\x12\x10\n\x08group_id\x18\x01 \x01(\r\"g\n ResReceiveAchievementGroupReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12)\n\x0e\x65xecute_reward\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"5\n\x1bReqReceiveAchievementReward\x12\x16\n\x0e\x61\x63hievement_id\x18\x01 \x01(\r\"b\n\x1bResReceiveAchievementReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12)\n\x0e\x65xecute_reward\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"\x9b\x01\n\x17ResFetchAchievementRate\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12\x39\n\x04rate\x18\x01 \x03(\x0b\x32+.lq.ResFetchAchievementRate.AchievementRate\x1a+\n\x0f\x41\x63hievementRate\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04rate\x18\x02 \x01(\r\"o\n\x0eResAchievement\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12+\n\nprogresses\x18\x02 \x03(\x0b\x32\x17.lq.AchievementProgress\x12\x16\n\x0erewarded_group\x18\x03 \x03(\r\"<\n\x0cResTitleList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\ntitle_list\x18\x02 \x03(\r\"\x1c\n\x0bReqUseTitle\x12\r\n\x05title\x18\x01 \x01(\r\"\x1d\n\rReqBuyShiLian\x12\x0c\n\x04type\x18\x01 \x01(\r\"2\n\x14ReqUpdateClientValue\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"\x91\x01\n\x0eResClientValue\x12\x18\n\x05\x65rror\x18\x03 \x01(\x0b\x32\t.lq.Error\x12\'\n\x05\x64\x61tas\x18\x01 \x03(\x0b\x32\x18.lq.ResClientValue.Value\x12\x17\n\x0frecharged_count\x18\x02 \x01(\r\x1a#\n\x05Value\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"6\n\x10ReqClientMessage\x12\x11\n\ttimestamp\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"(\n\x13ReqCurrentMatchInfo\x12\x11\n\tmode_list\x18\x01 \x03(\r\"\xa6\x01\n\x13ResCurrentMatchInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x39\n\x07matches\x18\x02 \x03(\x0b\x32(.lq.ResCurrentMatchInfo.CurrentMatchInfo\x1a:\n\x10\x43urrentMatchInfo\x12\x0f\n\x07mode_id\x18\x01 \x01(\r\x12\x15\n\rplaying_count\x18\x02 \x01(\r\"\xe1\x01\n\x0fReqUserComplain\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x11\n\tgame_uuid\x18\x04 \x01(\t\x12\x35\n\nround_info\x18\x05 \x01(\x0b\x32!.lq.ReqUserComplain.GameRoundInfo\x1aR\n\rGameRoundInfo\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x0c\n\x04seat\x18\x04 \x01(\r\x12\x0b\n\x03xun\x18\x05 \x01(\r\"I\n\x13ReqReadAnnouncement\x12\x17\n\x0f\x61nnouncement_id\x18\x01 \x01(\r\x12\x19\n\x11\x61nnouncement_list\x18\x02 \x03(\r\"A\n\x11ResReviveCoinInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\nhas_gained\x18\x02 \x01(\x08\"\x9e\x01\n\x0cResDailyTask\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\nprogresses\x18\x02 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x19\n\x11has_refresh_count\x18\x03 \x01(\x08\x12\x1c\n\x14max_daily_task_count\x18\x04 \x01(\r\x12\x15\n\rrefresh_count\x18\x05 \x01(\r\"&\n\x13ReqRefreshDailyTask\x12\x0f\n\x07task_id\x18\x01 \x01(\r\"j\n\x13ResRefreshDailyTask\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x08progress\x18\x02 \x01(\x0b\x32\x10.lq.TaskProgress\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\"\x1e\n\x0eReqUseGiftCode\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\"K\n\x0eResUseGiftCode\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\x07rewards\x18\x06 \x03(\x0b\x32\x0e.lq.RewardSlot\"U\n\x15ResUseSpecialGiftCode\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x07rewards\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"H\n\x14ReqSendClientMessage\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\"$\n\x0fReqGameLiveInfo\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\"\xaf\x01\n\x0fResGameLiveInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1a\n\x12left_start_seconds\x18\x02 \x01(\r\x12#\n\tlive_head\x18\x03 \x01(\x0b\x32\x10.lq.GameLiveHead\x12(\n\x08segments\x18\x04 \x03(\x0b\x32\x16.lq.GameLiveSegmentUri\x12\x17\n\x0fnow_millisecond\x18\x05 \x01(\r\"D\n\x16ReqGameLiveLeftSegment\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\x12\x17\n\x0flast_segment_id\x18\x02 \x01(\r\"\xaa\x01\n\x16ResGameLiveLeftSegment\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\nlive_state\x18\x02 \x01(\r\x12(\n\x08segments\x18\x04 \x03(\x0b\x32\x16.lq.GameLiveSegmentUri\x12\x17\n\x0fnow_millisecond\x18\x05 \x01(\r\x12\x1f\n\x17segment_end_millisecond\x18\x06 \x01(\r\"$\n\x0fReqGameLiveList\x12\x11\n\tfilter_id\x18\x01 \x01(\r\"P\n\x0fResGameLiveList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12#\n\tlive_list\x18\x02 \x03(\x0b\x32\x10.lq.GameLiveHead\"D\n\x11ResCommentSetting\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\rcomment_allow\x18\x02 \x01(\r\"0\n\x17ReqUpdateCommentSetting\x12\x15\n\rcomment_allow\x18\x01 \x01(\r\"(\n\x13ReqFetchCommentList\x12\x11\n\ttarget_id\x18\x01 \x01(\r\"u\n\x13ResFetchCommentList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\rcomment_allow\x18\x02 \x01(\r\x12\x17\n\x0f\x63omment_id_list\x18\x03 \x03(\r\x12\x14\n\x0clast_read_id\x18\x04 \x01(\r\"D\n\x16ReqFetchCommentContent\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12\x17\n\x0f\x63omment_id_list\x18\x02 \x03(\r\"U\n\x16ResFetchCommentContent\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12!\n\x08\x63omments\x18\x02 \x03(\x0b\x32\x0f.lq.CommentItem\"5\n\x0fReqLeaveComment\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\":\n\x10ReqDeleteComment\x12\x11\n\ttarget_id\x18\x01 \x01(\r\x12\x13\n\x0b\x64\x65lete_list\x18\x02 \x03(\r\"\'\n\x14ReqUpdateReadComment\x12\x0f\n\x07read_id\x18\x01 \x01(\r\"T\n\x15ResFetchRollingNotice\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12!\n\x06notice\x18\x03 \x01(\x0b\x32\x11.lq.RollingNotice\"V\n\x16ResFetchMaintainNotice\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x06notice\x18\x02 \x01(\x0b\x32\x12.lq.MaintainNotice\"%\n\x15ReqFetchRollingNotice\x12\x0c\n\x04lang\x18\x01 \x01(\t\">\n\rResServerTime\x12\x13\n\x0bserver_time\x18\x01 \x01(\r\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\"0\n\x1aReqPlatformBillingProducts\x12\x12\n\nshelves_id\x18\x01 \x01(\r\"\\\n\x1aResPlatformBillingProducts\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x08products\x18\x02 \x03(\x0b\x32\x12.lq.BillingProduct\"\x8b\x01\n\x15ReqCreateBillingOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x18\n\x10payment_platform\x18\x02 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x03 \x01(\r\x12\x12\n\naccount_id\x18\x04 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x05 \x01(\t\"C\n\x15ResCreateBillingOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"T\n\x17ReqSolveGooglePlayOrder\x12\x1b\n\x13inapp_purchase_data\x18\x02 \x01(\t\x12\x1c\n\x14inapp_data_signature\x18\x03 \x01(\t\"h\n\x19ReqSolveGooglePlayOrderV3\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x16\n\x0etransaction_id\x18\x02 \x01(\t\x12\r\n\x05token\x18\x03 \x01(\t\x12\x12\n\naccount_id\x18\x04 \x01(\r\",\n\x18ReqCancelGooglePlayOrder\x12\x10\n\x08order_id\x18\x01 \x01(\t\"\x8a\x01\n\x1aReqCreateWechatNativeOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\naccount_ip\x18\x04 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x05 \x01(\t\"_\n\x1aResCreateWechatNativeOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\rqrcode_buffer\x18\x02 \x01(\t\x12\x10\n\x08order_id\x18\x03 \x01(\t\"\x87\x01\n\x17ReqCreateWechatAppOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\naccount_ip\x18\x04 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x05 \x01(\t\"\x91\x02\n\x17ResCreateWechatAppOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12M\n\x15\x63\x61ll_wechat_app_param\x18\x02 \x01(\x0b\x32..lq.ResCreateWechatAppOrder.CallWechatAppParam\x1a\x8c\x01\n\x12\x43\x61llWechatAppParam\x12\r\n\x05\x61ppid\x18\x01 \x01(\t\x12\x11\n\tpartnerid\x18\x02 \x01(\t\x12\x10\n\x08prepayid\x18\x03 \x01(\t\x12\x0f\n\x07package\x18\x04 \x01(\t\x12\x10\n\x08noncestr\x18\x05 \x01(\t\x12\x11\n\ttimestamp\x18\x06 \x01(\t\x12\x0c\n\x04sign\x18\x07 \x01(\t\"\x9f\x01\n\x14ReqCreateAlipayOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x19\n\x11\x61lipay_trade_type\x18\x04 \x01(\t\x12\x12\n\nreturn_url\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x14ResCreateAlipayOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\nalipay_url\x18\x02 \x01(\t\"t\n\x18ReqCreateAlipayScanOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x04 \x01(\t\"n\n\x18ResCreateAlipayScanOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\rqrcode_buffer\x18\x02 \x01(\t\x12\x10\n\x08order_id\x18\x03 \x01(\t\x12\x0f\n\x07qr_code\x18\x04 \x01(\t\"s\n\x17ReqCreateAlipayAppOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x04 \x01(\t\"G\n\x17ResCreateAlipayAppOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\nalipay_url\x18\x02 \x01(\t\"\xa0\x01\n\x1aReqCreateJPCreditCardOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"H\n\x1aResCreateJPCreditCardOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateJPPaypalOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateJPPaypalOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x98\x01\n\x12ReqCreateJPAuOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"@\n\x12ResCreateJPAuOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateJPDocomoOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateJPDocomoOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9e\x01\n\x18ReqCreateJPWebMoneyOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"F\n\x18ResCreateJPWebMoneyOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9e\x01\n\x18ReqCreateJPSoftbankOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"F\n\x18ResCreateJPSoftbankOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateJPPayPayOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateJPPayPayOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"G\n\x1fReqFetchJPCommonCreditCardOrder\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\r\";\n\x1fResFetchJPCommonCreditCardOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"\x99\x01\n\x13ReqCreateJPGMOOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"A\n\x13ResCreateJPGMOOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x84\x01\n\x14ReqCreateYostarOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\norder_type\x18\x04 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x05 \x01(\t\"B\n\x14ResCreateYostarOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateENPaypalOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateENPaypalOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x99\x01\n\x13ReqCreateENJCBOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"A\n\x13ResCreateENJCBOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\xa0\x01\n\x1aReqCreateENMasterCardOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"H\n\x1aResCreateENMasterCardOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9a\x01\n\x14ReqCreateENVisaOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"B\n\x14ResCreateENVisaOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateENAlipayOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateENAlipayOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateKRPaypalOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateKRPaypalOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x99\x01\n\x13ReqCreateKRJCBOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"A\n\x13ResCreateKRJCBOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\xa0\x01\n\x1aReqCreateKRMasterCardOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"H\n\x1aResCreateKRMasterCardOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9a\x01\n\x14ReqCreateKRVisaOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"B\n\x14ResCreateKRVisaOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"\x9c\x01\n\x16ReqCreateKRAlipayOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x12\n\nreturn_url\x18\x04 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"D\n\x16ResCreateKRAlipayOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"m\n\x11ReqCreateDMMOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x03 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x04 \x01(\t\"\xbb\x01\n\x11ResCreateDmmOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\x12\x16\n\x0etransaction_id\x18\x03 \x01(\t\x12\x13\n\x0b\x64mm_user_id\x18\x04 \x01(\t\x12\r\n\x05token\x18\x05 \x01(\t\x12\x14\n\x0c\x63\x61llback_url\x18\x06 \x01(\t\x12\x14\n\x0crequest_time\x18\t \x01(\t\x12\x12\n\ndmm_app_id\x18\n \x01(\t\"\x9a\x01\n\x11ReqCreateIAPOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x04 \x01(\t\x12\x15\n\rdebt_order_id\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\"?\n\x11ResCreateIAPOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\"m\n\x17ReqVerificationIAPOrder\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x16\n\x0etransaction_id\x18\x02 \x01(\t\x12\x14\n\x0creceipt_data\x18\x03 \x01(\t\x12\x12\n\naccount_id\x18\x04 \x01(\r\"3\n\x17ResVerificationIAPOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\"\xaa\x01\n\x13ReqCreateSteamOrder\x12\x10\n\x08language\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x03 \x01(\r\x12\x10\n\x08goods_id\x18\x04 \x01(\r\x12\x10\n\x08steam_id\x18\x05 \x01(\t\x12\x15\n\rdebt_order_id\x18\x06 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x07 \x01(\t\"\\\n\x13ResCreateSteamOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\x12\x19\n\x11platform_order_id\x18\x03 \x01(\t\"b\n\x12ResRandomCharacter\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\x12!\n\x04pool\x18\x03 \x03(\x0b\x32\x13.lq.RandomCharacter\"H\n\x12ReqRandomCharacter\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12!\n\x04pool\x18\x02 \x03(\x0b\x32\x13.lq.RandomCharacter\";\n\x13ReqVerifySteamOrder\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\r\"\x87\x01\n\x14ReqCreateMyCardOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x15\n\rdebt_order_id\x18\x04 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x05 \x01(\t\"U\n\x14ResCreateMyCardOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x11\n\tauth_code\x18\x02 \x01(\t\x12\x10\n\x08order_id\x18\x03 \x01(\t\"<\n\x14ReqVerifyMyCardOrder\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\r\"\x87\x01\n\x14ReqCreatePaypalOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x15\n\rdebt_order_id\x18\x04 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x05 \x01(\t\"O\n\x14ResCreatePaypalOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\"\xb3\x01\n\x14ReqCreateXsollaOrder\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\x13\n\x0b\x63lient_type\x18\x02 \x01(\r\x12\x12\n\naccount_id\x18\x03 \x01(\r\x12\x16\n\x0epayment_method\x18\x04 \x01(\r\x12\x15\n\rdebt_order_id\x18\x05 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x06 \x01(\t\x12\x12\n\naccount_ip\x18\x07 \x01(\t\"O\n\x14ResCreateXsollaOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08order_id\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\"L\n\x13ReqDeliverAA32Order\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0e\n\x06nsa_id\x18\x02 \x01(\t\x12\x11\n\tnsa_token\x18\x03 \x01(\t\"b\n\x0cReqOpenChest\x12\x10\n\x08\x63hest_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x12\n\nuse_ticket\x18\x03 \x01(\x08\x12\x1d\n\x15\x63hoose_up_activity_id\x18\x04 \x01(\r\"\xee\x01\n\x0cResOpenChest\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\x07results\x18\x02 \x03(\x0b\x32\x0e.lq.OpenResult\x12\x18\n\x10total_open_count\x18\x03 \x01(\r\x12\x13\n\x0b\x66\x61ith_count\x18\x04 \x01(\r\x12@\n\x10\x63hest_replace_up\x18\x05 \x03(\x0b\x32&.lq.ResOpenChest.ChestReplaceCountData\x1a\x32\n\x15\x43hestReplaceCountData\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"6\n\x13ReqBuyFromChestShop\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"m\n\x13ResBuyFromChestShop\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08\x63hest_id\x18\x02 \x01(\r\x12\x15\n\rconsume_count\x18\x03 \x01(\r\x12\x13\n\x0b\x66\x61ith_count\x18\x04 \x01(\x05\"D\n\x12ResDailySignInInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x14\n\x0csign_in_days\x18\x02 \x01(\r\"*\n\x13ReqDoActivitySignIn\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"\xad\x01\n\x13ResDoActivitySignIn\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x33\n\x07rewards\x18\x02 \x03(\x0b\x32\".lq.ResDoActivitySignIn.RewardData\x12\x15\n\rsign_in_count\x18\x03 \x01(\r\x1a\x30\n\nRewardData\x12\x13\n\x0bresource_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\xb0\x02\n\x10ResCharacterInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12!\n\ncharacters\x18\x02 \x03(\x0b\x32\r.lq.Character\x12\r\n\x05skins\x18\x03 \x03(\r\x12\x19\n\x11main_character_id\x18\x04 \x01(\r\x12\x17\n\x0fsend_gift_count\x18\x05 \x01(\r\x12\x17\n\x0fsend_gift_limit\x18\x06 \x01(\r\x12\x18\n\x10\x66inished_endings\x18\x07 \x03(\r\x12\x18\n\x10rewarded_endings\x18\x08 \x03(\r\x12\x16\n\x0e\x63haracter_sort\x18\t \x03(\r\x12\x19\n\x11hidden_characters\x18\n \x03(\r\x12\x1c\n\x14other_character_sort\x18\x0b \x03(\r\"U\n\x16ReqUpdateCharacterSort\x12\x0c\n\x04sort\x18\x01 \x03(\r\x12\x12\n\nother_sort\x18\x02 \x03(\r\x12\x19\n\x11hidden_characters\x18\x03 \x03(\r\".\n\x16ReqChangeMainCharacter\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\"<\n\x16ReqChangeCharacterSkin\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12\x0c\n\x04skin\x18\x02 \x01(\r\"M\n\x16ReqChangeCharacterView\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12\x0c\n\x04slot\x18\x02 \x01(\r\x12\x0f\n\x07item_id\x18\x03 \x01(\r\"+\n\x15ReqSetHiddenCharacter\x12\x12\n\nchara_list\x18\x01 \x03(\r\"L\n\x15ResSetHiddenCharacter\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x19\n\x11hidden_characters\x18\x02 \x03(\r\"\x86\x01\n\x16ReqSendGiftToCharacter\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12.\n\x05gifts\x18\x02 \x03(\x0b\x32\x1f.lq.ReqSendGiftToCharacter.Gift\x1a&\n\x04Gift\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"N\n\x16ResSendGiftToCharacter\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0b\n\x03\x65xp\x18\x03 \x01(\r\"Z\n\x0bReqSellItem\x12#\n\x05sells\x18\x01 \x03(\x0b\x32\x14.lq.ReqSellItem.Item\x1a&\n\x04Item\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"u\n\rResCommonView\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12%\n\x05slots\x18\x02 \x03(\x0b\x32\x16.lq.ResCommonView.Slot\x1a#\n\x04Slot\x12\x0c\n\x04slot\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"2\n\x13ReqChangeCommonView\x12\x0c\n\x04slot\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"c\n\x12ReqSaveCommonViews\x12\x1b\n\x05views\x18\x01 \x03(\x0b\x32\x0c.lq.ViewSlot\x12\x12\n\nsave_index\x18\x02 \x01(\r\x12\x0e\n\x06is_use\x18\x03 \x01(\r\x12\x0c\n\x04name\x18\x04 \x01(\t\"\x1f\n\x0eReqCommonViews\x12\r\n\x05index\x18\x01 \x01(\r\"U\n\x0eResCommonViews\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12\x1b\n\x05views\x18\x01 \x03(\x0b\x32\x0c.lq.ViewSlot\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xaa\x01\n\x11ResAllcommonViews\x12*\n\x05views\x18\x01 \x03(\x0b\x32\x1b.lq.ResAllcommonViews.Views\x12\x0b\n\x03use\x18\x02 \x01(\r\x12\x18\n\x05\x65rror\x18\x03 \x01(\x0b\x32\t.lq.Error\x1a\x42\n\x05Views\x12\x1c\n\x06values\x18\x01 \x03(\x0b\x32\x0c.lq.ViewSlot\x12\r\n\x05index\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\"!\n\x10ReqUseCommonView\x12\r\n\x05index\x18\x03 \x01(\r\"+\n\x13ReqUpgradeCharacter\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\"Q\n\x13ResUpgradeCharacter\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12 \n\tcharacter\x18\x02 \x01(\x0b\x32\r.lq.Character\"N\n\x11ReqFinishedEnding\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\x12\x11\n\tending_id\x18\x03 \x01(\r\"\x1f\n\x0cReqGMCommand\x12\x0f\n\x07\x63ommand\x18\x01 \x01(\t\"H\n\x0bResShopInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\tshop_info\x18\x02 \x01(\x0b\x32\x0c.lq.ShopInfo\"\xac\x01\n\x0eReqBuyFromShop\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12*\n\tver_price\x18\x03 \x03(\x0b\x32\x17.lq.ReqBuyFromShop.Item\x12*\n\tver_goods\x18\x04 \x03(\x0b\x32\x17.lq.ReqBuyFromShop.Item\x1a!\n\x04Item\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"K\n\x0eResBuyFromShop\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\x07rewards\x18\x02 \x03(\x0b\x32\x0e.lq.RewardSlot\"0\n\rReqBuyFromZHP\x12\x10\n\x08goods_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"&\n\x11ReqPayMonthTicket\x12\x11\n\tticket_id\x18\x01 \x01(\r\"Z\n\x11ResPayMonthTicket\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bresource_id\x18\x02 \x01(\r\x12\x16\n\x0eresource_count\x18\x03 \x01(\r\"<\n\x0eReqReshZHPShop\x12\x14\n\x0c\x66ree_refresh\x18\x01 \x01(\r\x12\x14\n\x0c\x63ost_refresh\x18\x02 \x01(\r\"G\n\x11ResRefreshZHPShop\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x18\n\x03zhp\x18\x02 \x01(\x0b\x32\x0b.lq.ZHPShop\"^\n\x12ResMonthTicketInfo\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12.\n\x11month_ticket_info\x18\x01 \x01(\x0b\x32\x13.lq.MonthTicketInfo\"0\n\x13ReqExchangeCurrency\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"S\n\x11ResServerSettings\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12$\n\x08settings\x18\x01 \x01(\x0b\x32\x12.lq.ServerSettings\"T\n\x12ResAccountSettings\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x08settings\x18\x02 \x03(\x0b\x32\x12.lq.AccountSetting\"?\n\x18ReqUpdateAccountSettings\x12#\n\x07setting\x18\x01 \x01(\x0b\x32\x12.lq.AccountSetting\"E\n\x12ResModNicknameTime\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12\x15\n\rlast_mod_time\x18\x01 \x01(\r\"\xef\x01\n\x07ResMisc\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x16\n\x0erecharged_list\x18\x02 \x03(\r\x12)\n\x06\x66\x61iths\x18\x03 \x03(\x0b\x32\x19.lq.ResMisc.MiscFaithData\x12\x17\n\x0fverified_hidden\x18\x04 \x01(\r\x12\x16\n\x0everified_value\x18\x05 \x01(\r\x12$\n\x1c\x64isable_room_random_bot_char\x18\x06 \x01(\r\x1a\x30\n\rMiscFaithData\x12\x10\n\x08\x66\x61ith_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\"\'\n\x12ReqModifySignature\x12\x11\n\tsignature\x18\x01 \x01(\t\"M\n\rResIDCardInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x11\n\tis_authed\x18\x02 \x01(\x08\x12\x0f\n\x07\x63ountry\x18\x03 \x01(\t\"8\n\x13ReqUpdateIDCardInfo\x12\x10\n\x08\x66ullname\x18\x01 \x01(\t\x12\x0f\n\x07\x63\x61rd_no\x18\x02 \x01(\t\"C\n\x0cResVipReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x19\n\x11gained_vip_levels\x18\x02 \x03(\r\"\xf4\x01\n\x13ResFetchRefundOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x31\n\x06orders\x18\x02 \x03(\x0b\x32!.lq.ResFetchRefundOrder.OrderInfo\x12\x16\n\x0e\x63lear_deadline\x18\x03 \x01(\r\x12 \n\x07message\x18\x04 \x03(\x0b\x32\x0f.lq.I18nContext\x1aV\n\tOrderInfo\x12\x14\n\x0csuccess_time\x18\x01 \x01(\r\x12\x10\n\x08goods_id\x18\x02 \x01(\r\x12\x0f\n\x07\x63leared\x18\x03 \x01(\r\x12\x10\n\x08order_id\x18\x04 \x01(\t\"%\n\x10ReqGainVipReward\x12\x11\n\tvip_level\x18\x01 \x01(\r\"K\n\x1dReqFetchCustomizedContestList\x12\r\n\x05start\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0c\n\x04lang\x18\x03 \x01(\t\"\x9a\x01\n\x1dResFetchCustomizedContestList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12+\n\x08\x63ontests\x18\x02 \x03(\x0b\x32\x19.lq.CustomizedContestBase\x12\x32\n\x0f\x66ollow_contests\x18\x03 \x03(\x0b\x32\x19.lq.CustomizedContestBase\"6\n!ReqFetchCustomizedContestAuthInfo\x12\x11\n\tunique_id\x18\x01 \x01(\r\"U\n!ResFetchCustomizedContestAuthInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x16\n\x0eobserver_level\x18\x02 \x01(\r\"<\n\x19ReqEnterCustomizedContest\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x0c\n\x04lang\x18\x02 \x01(\t\"\xd7\x01\n\x19ResEnterCustomizedContest\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x30\n\x0b\x64\x65tail_info\x18\x02 \x01(\x0b\x32\x1b.lq.CustomizedContestDetail\x12\x38\n\rplayer_report\x18\x03 \x01(\x0b\x32!.lq.CustomizedContestPlayerReport\x12\x13\n\x0bis_followed\x18\x04 \x01(\x08\x12\r\n\x05state\x18\x05 \x01(\r\x12\x10\n\x08is_admin\x18\x06 \x01(\x08\"8\n#ReqFetchCustomizedContestOnlineInfo\x12\x11\n\tunique_id\x18\x01 \x01(\r\"V\n#ResFetchCustomizedContestOnlineInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\ronline_player\x18\x02 \x01(\r\"H\n$ReqFetchCustomizedContestByContestId\x12\x12\n\ncontest_id\x18\x01 \x01(\r\x12\x0c\n\x04lang\x18\x02 \x01(\t\"u\n$ResFetchCustomizedContestByContestId\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x33\n\x0c\x63ontest_info\x18\x02 \x01(\x0b\x32\x1d.lq.CustomizedContestAbstract\"N\n\x1aReqSignupCustomizedContest\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x02 \x01(\t\"E\n\x1aResSignupCustomizedContest\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05state\x18\x02 \x01(\r\"M\n\x19ReqStartCustomizedContest\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x1d\n\x15\x63lient_version_string\x18\x02 \x01(\t\"-\n\x18ReqStopCustomizedContest\x12\x11\n\tunique_id\x18\x01 \x01(\r\"5\n ReqJoinCustomizedContestChatRoom\x12\x11\n\tunique_id\x18\x01 \x01(\r\"K\n ResJoinCustomizedContestChatRoom\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05token\x18\x02 \x01(\t\"7\n\x11ReqSayChatMessage\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x11\n\tunique_id\x18\x02 \x01(\r\":\n%ReqFetchCustomizedContestGameLiveList\x12\x11\n\tunique_id\x18\x01 \x01(\r\"f\n%ResFetchCustomizedContestGameLiveList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12#\n\tlive_list\x18\x02 \x03(\x0b\x32\x10.lq.GameLiveHead\"`\n$ReqFetchCustomizedContestGameRecords\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x12\n\nlast_index\x18\x02 \x01(\r\x12\x11\n\tseason_id\x18\x03 \x01(\r\"y\n$ResFetchCustomizedContestGameRecords\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\nnext_index\x18\x02 \x01(\r\x12#\n\x0brecord_list\x18\x03 \x03(\x0b\x32\x0e.lq.RecordGame\"/\n\x1aReqTargetCustomizedContest\x12\x11\n\tunique_id\x18\x01 \x01(\r\"M\n\x0fResActivityList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12 \n\nactivities\x18\x02 \x03(\x0b\x32\x0c.lq.Activity\"\x9c\x0f\n\x16ResAccountActivityData\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12,\n\x10\x65xchange_records\x18\x02 \x03(\x0b\x32\x12.lq.ExchangeRecord\x12,\n\x12task_progress_list\x18\x03 \x03(\x0b\x32\x10.lq.TaskProgress\x12@\n\x16\x61\x63\x63umulated_point_list\x18\x04 \x03(\x0b\x32 .lq.ActivityAccumulatedPointData\x12\x31\n\x0erank_data_list\x18\x05 \x03(\x0b\x32\x19.lq.ActivityRankPointData\x12\x31\n\x17\x66lip_task_progress_list\x18\x06 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x43\n\x0csign_in_data\x18\x07 \x03(\x0b\x32-.lq.ResAccountActivityData.ActivitySignInData\x12\x44\n\x0crichman_data\x18\x08 \x03(\x0b\x32..lq.ResAccountActivityData.ActivityRichmanData\x12\x33\n\x19period_task_progress_list\x18\t \x03(\x0b\x32\x10.lq.TaskProgress\x12\x33\n\x19random_task_progress_list\x18\n \x03(\x0b\x32\x10.lq.TaskProgress\x12=\n\rchest_up_data\x18\x0b \x03(\x0b\x32&.lq.ResAccountActivityData.ChestUpData\x12<\n\x08sns_data\x18\x0c \x01(\x0b\x32*.lq.ResAccountActivityData.ActivitySNSData\x12\'\n\tmine_data\x18\r \x03(\x0b\x32\x14.lq.MineActivityData\x12!\n\x08rpg_data\x18\x0e \x03(\x0b\x32\x0f.lq.RPGActivity\x12)\n\narena_data\x18\x0f \x03(\x0b\x32\x15.lq.ActivityArenaData\x12\'\n\tfeed_data\x18\x10 \x03(\x0b\x32\x14.lq.FeedActivityData\x12;\n\x1asegment_task_progress_list\x18\x11 \x03(\x0b\x32\x17.lq.SegmentTaskProgress\x12\"\n\x0cvote_records\x18\x12 \x03(\x0b\x32\x0c.lq.VoteData\x12\'\n\tspot_data\x18\x13 \x03(\x0b\x32\x14.lq.ActivitySpotData\x12\x34\n\x10\x66riend_gift_data\x18\x14 \x03(\x0b\x32\x1a.lq.ActivityFriendGiftData\x12-\n\x0cupgrade_data\x18\x15 \x03(\x0b\x32\x17.lq.ActivityUpgradeData\x12/\n\ngacha_data\x18\x16 \x03(\x0b\x32\x1b.lq.ActivityGachaUpdateData\x12\x33\n\x0fsimulation_data\x18\x17 \x03(\x0b\x32\x1a.lq.ActivitySimulationData\x12\x33\n\x0e\x63ombining_data\x18\x18 \x03(\x0b\x32\x1b.lq.ActivityCombiningLQData\x12-\n\x0cvillage_data\x18\x19 \x03(\x0b\x32\x17.lq.ActivityVillageData\x12/\n\rfestival_data\x18\x1a \x03(\x0b\x32\x18.lq.ActivityFestivalData\x12+\n\x0bisland_data\x18\x1b \x03(\x0b\x32\x16.lq.ActivityIslandData\x12)\n\nstory_data\x18\x1d \x03(\x0b\x32\x15.lq.ActivityStoryData\x12\x30\n\x0e\x63hoose_up_data\x18\x1e \x03(\x0b\x32\x18.lq.ActivityChooseUpData\x1a[\n\x12\x41\x63tivitySignInData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x15\n\rsign_in_count\x18\x02 \x01(\r\x12\x19\n\x11last_sign_in_time\x18\x03 \x01(\r\x1a\x38\n\x08\x42uffData\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0e\n\x06remain\x18\x02 \x01(\r\x12\x0e\n\x06\x65\x66\x66\x65\x63t\x18\x03 \x01(\r\x1a\xbf\x01\n\x13\x41\x63tivityRichmanData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08location\x18\x02 \x01(\r\x12\x16\n\x0e\x66inished_count\x18\x03 \x01(\r\x12\x16\n\x0e\x63hest_position\x18\x04 \x01(\r\x12\x11\n\tbank_save\x18\x05 \x01(\r\x12\x0b\n\x03\x65xp\x18\x06 \x01(\r\x12\x31\n\x04\x62uff\x18\x07 \x03(\x0b\x32#.lq.ResAccountActivityData.BuffData\x1a(\n\x0b\x43hestUpData\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x1a[\n\x0f\x41\x63tivitySNSData\x12\x19\n\x04\x62log\x18\x01 \x03(\x0b\x32\x0b.lq.SNSBlog\x12\x10\n\x08liked_id\x18\x02 \x03(\r\x12\x1b\n\x05reply\x18\x03 \x03(\x0b\x32\x0c.lq.SNSReply\"(\n\x07SNSBlog\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\tread_time\x18\x02 \x01(\r\"*\n\x08SNSReply\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\nreply_time\x18\x02 \x01(\r\"=\n\x17ReqExchangeActivityItem\x12\x13\n\x0b\x65xchange_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"^\n\x17ResExchangeActivityItem\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12)\n\x0e\x65xecute_reward\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"*\n\x17ReqCompleteActivityTask\x12\x0f\n\x07task_id\x18\x01 \x01(\r\"1\n\x1cReqCompleteActivityTaskBatch\x12\x11\n\ttask_list\x18\x01 \x03(\r\"7\n\"ReqCompletePeriodActivityTaskBatch\x12\x11\n\ttask_list\x18\x01 \x03(\r\"-\n\x1aReqReceiveActivityFlipTask\x12\x0f\n\x07task_id\x18\x01 \x01(\r\"E\n\x1aResReceiveActivityFlipTask\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\">\n\x1cReqCompleteSegmentTaskReward\x12\x0f\n\x07task_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\\\n\x1cResCompleteSegmentTaskReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x07rewards\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"/\n\x18ReqFetchActivityFlipInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"T\n\x18ResFetchActivityFlipInfo\x12\x0f\n\x07rewards\x18\x01 \x03(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x18\n\x05\x65rror\x18\x03 \x01(\x0b\x32\t.lq.Error\"O\n%ReqGainAccumulatedPointActivityReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\treward_id\x18\x02 \x01(\r\"N\n\x1fReqGainMultiPointActivityReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x16\n\x0ereward_id_list\x18\x02 \x03(\r\"6\n\x1cReqFetchRankPointLeaderboard\x12\x16\n\x0eleaderboard_id\x18\x01 \x01(\r\"\xe4\x01\n\x1cResFetchRankPointLeaderboard\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x34\n\x05items\x18\x02 \x03(\x0b\x32%.lq.ResFetchRankPointLeaderboard.Item\x12\x19\n\x11last_refresh_time\x18\x03 \x01(\r\x1aY\n\x04Item\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0c\n\x04rank\x18\x02 \x01(\r\x12 \n\x04view\x18\x03 \x01(\x0b\x32\x12.lq.PlayerBaseView\x12\r\n\x05point\x18\x04 \x01(\r\"E\n\x16ReqGainRankPointReward\x12\x16\n\x0eleaderboard_id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\")\n\x12ReqRichmanNextMove\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x94\x04\n\x12ResRichmanNextMove\x12.\n\x05paths\x18\x01 \x03(\x0b\x32\x1f.lq.ResRichmanNextMove.PathData\x12\x0c\n\x04\x64ice\x18\x02 \x01(\r\x12\x10\n\x08location\x18\x03 \x01(\r\x12\x16\n\x0e\x66inished_count\x18\x04 \x01(\r\x12\x0c\n\x04step\x18\x05 \x01(\r\x12-\n\x04\x62uff\x18\x06 \x03(\x0b\x32\x1f.lq.ResRichmanNextMove.BuffData\x12\x11\n\tbank_save\x18\x07 \x01(\r\x12\x16\n\x0e\x63hest_position\x18\x08 \x01(\r\x12\x0b\n\x03\x65xp\x18\t \x01(\r\x12\x15\n\rbank_save_add\x18\n \x01(\r\x12\x18\n\x05\x65rror\x18\x0b \x01(\x0b\x32\t.lq.Error\x1aT\n\nRewardData\x12\x13\n\x0bresource_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x14\n\x0corigin_count\x18\x03 \x01(\r\x12\x0c\n\x04type\x18\x05 \x01(\r\x1a`\n\x08PathData\x12\x10\n\x08location\x18\x01 \x01(\r\x12\x32\n\x07rewards\x18\x02 \x03(\x0b\x32!.lq.ResRichmanNextMove.RewardData\x12\x0e\n\x06\x65vents\x18\x03 \x03(\r\x1a\x38\n\x08\x42uffData\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0e\n\x06remain\x18\x02 \x01(\r\x12\x0e\n\x06\x65\x66\x66\x65\x63t\x18\x03 \x01(\r\":\n\x15ReqRichmanSpecialMove\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04step\x18\x02 \x01(\r\"*\n\x13ReqRichmanChestInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x87\x01\n\x13ResRichmanChestInfo\x12/\n\x05items\x18\x01 \x03(\x0b\x32 .lq.ResRichmanChestInfo.ItemData\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x1a%\n\x08ItemData\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"-\n\x18ReqCreateGameObserveAuth\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\"U\n\x18ResCreateGameObserveAuth\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05token\x18\x02 \x01(\t\x12\x10\n\x08location\x18\x03 \x01(\t\"*\n\x19ReqRefreshGameObserveAuth\x12\r\n\x05token\x18\x01 \x01(\t\"B\n\x19ResRefreshGameObserveAuth\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0b\n\x03ttl\x18\x02 \x01(\r\"T\n\x0fResActivityBuff\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\tbuff_list\x18\x02 \x03(\x0b\x32\x14.lq.ActivityBuffData\")\n\x16ReqUpgradeActivityBuff\x12\x0f\n\x07\x62uff_id\x18\x01 \x01(\r\"L\n\x17ReqUpgradeActivityLevel\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05group\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\"W\n\x17ResUpgradeActivityLevel\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x07rewards\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"6\n\x1fReqReceiveUpgradeActivityReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"_\n\x1fResReceiveUpgradeActivityReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x07rewards\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"0\n\x19ReqReceiveAllActivityGift\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\xf4\x01\n\x19ResReceiveAllActivityGift\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x07rewards\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\x12\x42\n\x0creceive_gift\x18\x03 \x03(\x0b\x32,.lq.ResReceiveAllActivityGift.ReceiveRewards\x1aU\n\x0eReceiveRewards\x12\n\n\x02id\x18\x01 \x01(\r\x12\x17\n\x0f\x66rom_account_id\x18\x02 \x01(\r\x12\x0f\n\x07item_id\x18\x03 \x01(\r\x12\r\n\x05\x63ount\x18\x04 \x01(\r\"\xa6\x01\n\x13ResUpgradeChallenge\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\rtask_progress\x18\x02 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\x12\r\n\x05level\x18\x04 \x01(\r\x12\x13\n\x0bmatch_count\x18\x05 \x01(\r\x12\x11\n\tticket_id\x18\x06 \x01(\r\"\xa6\x01\n\x13ResRefreshChallenge\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\rtask_progress\x18\x02 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\x12\r\n\x05level\x18\x04 \x01(\r\x12\x13\n\x0bmatch_count\x18\x05 \x01(\r\x12\x11\n\tticket_id\x18\x06 \x01(\r\"\xc1\x01\n\x15ResFetchChallengeInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\rtask_progress\x18\x02 \x03(\x0b\x32\x10.lq.TaskProgress\x12\x15\n\rrefresh_count\x18\x03 \x01(\r\x12\r\n\x05level\x18\x04 \x01(\r\x12\x13\n\x0bmatch_count\x18\x05 \x01(\r\x12\x11\n\tticket_id\x18\x06 \x01(\r\x12\x17\n\x0frewarded_season\x18\x07 \x03(\r\"0\n\x1dReqForceCompleteChallengeTask\x12\x0f\n\x07task_id\x18\x01 \x01(\r\"\xff\x01\n\x0fResFetchABMatch\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08match_id\x18\x02 \x01(\r\x12\x13\n\x0bmatch_count\x18\x03 \x01(\r\x12\x14\n\x0c\x62uy_in_count\x18\x04 \x01(\r\x12\r\n\x05point\x18\x05 \x01(\r\x12\x10\n\x08rewarded\x18\x06 \x01(\x08\x12\x37\n\x0fmatch_max_point\x18\x07 \x03(\x0b\x32\x1e.lq.ResFetchABMatch.MatchPoint\x12\x0c\n\x04quit\x18\x08 \x01(\x08\x1a-\n\nMatchPoint\x12\x10\n\x08match_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\"H\n\x14ReqStartUnifiedMatch\x12\x11\n\tmatch_sid\x18\x01 \x01(\t\x12\x1d\n\x15\x63lient_version_string\x18\x02 \x01(\t\"*\n\x15ReqCancelUnifiedMatch\x12\x11\n\tmatch_sid\x18\x01 \x01(\t\"\xd4\x01\n\x16ResChallengeSeasonInfo\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12G\n\x15\x63hallenge_season_list\x18\x01 \x03(\x0b\x32(.lq.ResChallengeSeasonInfo.ChallengeInfo\x1aW\n\rChallengeInfo\x12\x11\n\tseason_id\x18\x01 \x01(\r\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\r\x12\r\n\x05state\x18\x04 \x01(\r\"2\n\x1dReqReceiveChallengeRankReward\x12\x11\n\tseason_id\x18\x01 \x01(\r\"\xa2\x01\n\x1dResReceiveChallengeRankReward\x12\x18\n\x05\x65rror\x18\x02 \x01(\x0b\x32\t.lq.Error\x12\x39\n\x07rewards\x18\x01 \x03(\x0b\x32(.lq.ResReceiveChallengeRankReward.Reward\x1a,\n\x06Reward\x12\x13\n\x0bresource_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"#\n\x0fReqBuyInABMatch\x12\x10\n\x08match_id\x18\x01 \x01(\r\"\'\n\x10ReqGamePointRank\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x9b\x01\n\x10ResGamePointRank\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12+\n\x04rank\x18\x02 \x03(\x0b\x32\x1d.lq.ResGamePointRank.RankInfo\x12\x11\n\tself_rank\x18\x03 \x01(\r\x1a-\n\x08RankInfo\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\"H\n\x19ResFetchSelfGamePointRank\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x11\n\tself_rate\x18\x02 \x01(\r\"\x18\n\nReqReadSNS\x12\n\n\x02id\x18\x01 \x01(\r\"H\n\nResReadSNS\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12 \n\x0bsns_content\x18\x02 \x01(\x0b\x32\x0b.lq.SNSBlog\"\x19\n\x0bReqReplySNS\x12\n\n\x02id\x18\x01 \x01(\r\"H\n\x0bResReplySNS\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\tsns_reply\x18\x02 \x01(\x0b\x32\x0c.lq.SNSReply\"\x18\n\nReqLikeSNS\x12\n\n\x02id\x18\x01 \x01(\r\"8\n\nResLikeSNS\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08is_liked\x18\x02 \x01(\r\";\n\nReqDigMine\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x18\n\x05point\x18\x02 \x01(\x0b\x32\t.lq.Point\"c\n\nResDigMine\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1b\n\x03map\x18\x02 \x03(\x0b\x32\x0e.lq.MineReward\x12\x1e\n\x06reward\x18\x03 \x03(\x0b\x32\x0e.lq.RewardSlot\"#\n\x13ReqFetchLastPrivacy\x12\x0c\n\x04type\x18\x01 \x03(\r\"\x93\x01\n\x13ResFetchLastPrivacy\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x34\n\x07privacy\x18\x02 \x03(\x0b\x32#.lq.ResFetchLastPrivacy.PrivacyInfo\x1a,\n\x0bPrivacyInfo\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0f\n\x07version\x18\x02 \x01(\t\"\x81\x01\n\x0fReqCheckPrivacy\x12\x13\n\x0b\x64\x65vice_type\x18\x01 \x01(\t\x12.\n\x08versions\x18\x02 \x03(\x0b\x32\x1c.lq.ReqCheckPrivacy.Versions\x1a)\n\x08Versions\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\r\"/\n\x18ReqFetchRPGBattleHistory\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\xce\x03\n\x18ResFetchRPGBattleHistory\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12@\n\rbattle_result\x18\x02 \x03(\x0b\x32).lq.ResFetchRPGBattleHistory.BattleResult\x12!\n\x0bstart_state\x18\x03 \x01(\x0b\x32\x0c.lq.RPGState\x12#\n\rcurrent_state\x18\x04 \x01(\x0b\x32\x0c.lq.RPGState\x1a\x8d\x02\n\x0c\x42\x61ttleResult\x12\x0c\n\x04uuid\x18\x0e \x01(\t\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x0e\n\x06target\x18\x04 \x01(\r\x12\x0e\n\x06\x64\x61mage\x18\x05 \x01(\r\x12\x0c\n\x04heal\x18\x06 \x01(\r\x12\x13\n\x0bmonster_seq\x18\x07 \x01(\r\x12\x11\n\tchain_atk\x18\x08 \x01(\r\x12\x0e\n\x06killed\x18\t \x01(\r\x12\x0e\n\x06is_luk\x18\n \x01(\r\x12\x0e\n\x06is_dex\x18\x0b \x01(\r\x12\x10\n\x08is_extra\x18\x0c \x01(\r\x12\x0e\n\x06reward\x18\r \x01(\t\x12\x0e\n\x06points\x18\x0f \x01(\r\x12\x0f\n\x07is_zimo\x18\x10 \x01(\r\"\xc3\x03\n\x1aResFetchRPGBattleHistoryV2\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x44\n\rbattle_result\x18\x02 \x03(\x0b\x32-.lq.ResFetchRPGBattleHistoryV2.BattleResultV2\x12!\n\x0bstart_state\x18\x03 \x01(\x0b\x32\x0c.lq.RPGState\x12#\n\rcurrent_state\x18\x04 \x01(\x0b\x32\x0c.lq.RPGState\x12K\n\x14recent_battle_result\x18\x05 \x03(\x0b\x32-.lq.ResFetchRPGBattleHistoryV2.BattleResultV2\x1a\xaf\x01\n\x0e\x42\x61ttleResultV2\x12\x0c\n\x04uuid\x18\x0e \x01(\t\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x0e\n\x06\x64\x61mage\x18\x05 \x01(\r\x12\x13\n\x0bmonster_seq\x18\x07 \x01(\r\x12\x0e\n\x06killed\x18\t \x01(\r\x12\"\n\x04\x62uff\x18\n \x03(\x0b\x32\x14.lq.ActivityBuffData\x12\x0e\n\x06points\x18\x0b \x01(\r\"(\n\x11ReqBuyArenaTicket\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"%\n\x0eReqArenaReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"$\n\rReqEnterArena\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x81\x01\n\x0eResArenaReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12,\n\x05items\x18\x02 \x03(\x0b\x32\x1d.lq.ResArenaReward.RewardItem\x1a\'\n\nRewardItem\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"+\n\x14ReqReceiveRPGRewards\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"?\n\x13ReqReceiveRPGReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x13\n\x0bmonster_seq\x18\x02 \x01(\r\"\x8d\x01\n\x14ResReceiveRPGRewards\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x32\n\x05items\x18\x02 \x03(\x0b\x32#.lq.ResReceiveRPGRewards.RewardItem\x1a\'\n\nRewardItem\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\x1f\n\x0fReqFetchOBToken\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"r\n\x0fResFetchOBToken\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05token\x18\x02 \x01(\t\x12\x13\n\x0b\x63reate_time\x18\x03 \x01(\r\x12\r\n\x05\x64\x65lay\x18\x04 \x01(\r\x12\x12\n\nstart_time\x18\x05 \x01(\r\"A\n\x1aReqReceiveCharacterRewards\x12\x14\n\x0c\x63haracter_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\"\x99\x01\n\x1aResReceiveCharacterRewards\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x38\n\x05items\x18\x02 \x03(\x0b\x32).lq.ResReceiveCharacterRewards.RewardItem\x1a\'\n\nRewardItem\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"9\n\x13ReqFeedActivityFeed\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\x9f\x01\n\x13ResFeedActivityFeed\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x31\n\x05items\x18\x02 \x03(\x0b\x32\".lq.ResFeedActivityFeed.RewardItem\x12\x12\n\nfeed_count\x18\x03 \x01(\r\x1a\'\n\nRewardItem\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"V\n\x1bReqSendActivityGiftToFriend\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\x11\n\ttarget_id\x18\x03 \x01(\r\"P\n\x1bResSendActivityGiftToFriend\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x17\n\x0fsend_gift_count\x18\x02 \x01(\r\"9\n\x16ReqReceiveActivityGift\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\"K\n\x1eReqFetchFriendGiftActivityData\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x14\n\x0c\x61\x63\x63ount_list\x18\x02 \x03(\r\"\x9f\x02\n\x1eResFetchFriendGiftActivityData\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12;\n\x04list\x18\x02 \x03(\x0b\x32-.lq.ResFetchFriendGiftActivityData.FriendData\x1a,\n\rItemCountData\x12\x0c\n\x04item\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x1ax\n\nFriendData\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12?\n\x05items\x18\x02 \x03(\x0b\x32\x30.lq.ResFetchFriendGiftActivityData.ItemCountData\x12\x15\n\rreceive_count\x18\x03 \x01(\r\"7\n\x13ReqOpenPreChestItem\x12\x0f\n\x07item_id\x18\x01 \x01(\r\x12\x0f\n\x07pool_id\x18\x02 \x01(\r\"P\n\x13ResOpenPreChestItem\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1f\n\x07results\x18\x02 \x03(\x0b\x32\x0e.lq.OpenResult\"+\n\x14ReqFetchVoteActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"X\n\x14ResFetchVoteActivity\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x11\n\tvote_rank\x18\x02 \x03(\r\x12\x13\n\x0bupdate_time\x18\x03 \x01(\r\"4\n\x0fReqVoteActivity\x12\x0c\n\x04vote\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"O\n\x0fResVoteActivity\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x0cvote_records\x18\x02 \x03(\x0b\x32\x0c.lq.VoteData\"*\n\x15ReqUnlockActivitySpot\x12\x11\n\tunique_id\x18\x01 \x01(\r\"C\n\x1bReqUnlockActivitySpotEnding\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x11\n\tending_id\x18\x02 \x01(\r\"1\n\x1cReqReceiveActivitySpotReward\x12\x11\n\tunique_id\x18\x01 \x01(\r\"\x9d\x01\n\x1cResReceiveActivitySpotReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12:\n\x05items\x18\x02 \x03(\x0b\x32+.lq.ResReceiveActivitySpotReward.RewardItem\x1a\'\n\nRewardItem\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"/\n\x0cReqLogReport\x12\x0f\n\x07success\x18\x01 \x01(\r\x12\x0e\n\x06\x66\x61iled\x18\x02 \x01(\r\",\n\rReqBindOauth2\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05token\x18\x02 \x01(\t\"\x1e\n\x0eReqFetchOauth2\x12\x0c\n\x04type\x18\x01 \x01(\r\":\n\x0eResFetchOauth2\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0e\n\x06openid\x18\x02 \x01(\t\"A\n\x10ResDeleteAccount\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0b\x64\x65lete_time\x18\x02 \x01(\r\"$\n\x12ReqSetLoadingImage\x12\x0e\n\x06images\x18\x01 \x03(\r\"\x9b\x01\n\x14ResFetchShopInterval\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x35\n\x06result\x18\x02 \x03(\x0b\x32%.lq.ResFetchShopInterval.ShopInterval\x1a\x32\n\x0cShopInterval\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\x10\n\x08interval\x18\x02 \x01(\r\"\xae\x01\n\x18ResFetchActivityInterval\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12=\n\x06result\x18\x02 \x03(\x0b\x32-.lq.ResFetchActivityInterval.ActivityInterval\x1a\x39\n\x10\x41\x63tivityInterval\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08interval\x18\x02 \x01(\r\"F\n\x14ResFetchrecentFriend\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x14\n\x0c\x61\x63\x63ount_list\x18\x02 \x03(\r\"2\n\x0cReqOpenGacha\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"\xa8\x01\n\x0cResOpenGacha\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bresult_list\x18\x02 \x03(\r\x12\'\n\x0creward_items\x18\x03 \x03(\x0b\x32\x11.lq.ExecuteReward\x12*\n\x0fsp_reward_items\x18\x04 \x03(\x0b\x32\x11.lq.ExecuteReward\x12\x14\n\x0cremain_count\x18\x05 \x01(\r\" \n\x0eReqTaskRequest\x12\x0e\n\x06params\x18\x01 \x03(\r\"?\n\x1aReqSimulationActivityTrain\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\"`\n\x1aResSimulationActivityTrain\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x13\n\x0bresult_type\x18\x02 \x01(\r\x12\x13\n\x0b\x66inal_stats\x18\x04 \x03(\r\"F\n\x1cReqFetchSimulationGameRecord\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"s\n\x1cResFetchSimulationGameRecord\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x39\n\x08messages\x18\x02 \x03(\x0b\x32\'.lq.ActivitySimulationGameRecordMessage\"5\n\x1eReqStartSimulationActivityGame\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"m\n\x1eResStartSimulationActivityGame\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x31\n\x07records\x18\x02 \x03(\x0b\x32 .lq.ActivitySimulationGameRecord\">\n\x1aReqFetchSimulationGameRank\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0b\n\x03\x64\x61y\x18\x02 \x01(\r\"\x9b\x01\n\x1aResFetchSimulationGameRank\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x35\n\x04rank\x18\x02 \x03(\x0b\x32\'.lq.ResFetchSimulationGameRank.RankInfo\x1a,\n\x08RankInfo\x12\x11\n\tcharacter\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\x02\"@\n\x19ReqGenerateCombiningCraft\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0e\n\x06\x62in_id\x18\x02 \x01(\r\"T\n\x19ResGenerateCombiningCraft\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0b\n\x03pos\x18\x02 \x01(\r\x12\x10\n\x08\x63raft_id\x18\x03 \x01(\r\"F\n\x15ReqMoveCombiningCraft\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04\x66rom\x18\x02 \x01(\r\x12\n\n\x02to\x18\x03 \x01(\r\"\xc2\x01\n\x15ResMoveCombiningCraft\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0b\n\x03pos\x18\x02 \x01(\r\x12\x10\n\x08\x63ombined\x18\x03 \x01(\r\x12\x10\n\x08\x63raft_id\x18\x04 \x01(\r\x12\x32\n\x05\x62onus\x18\x05 \x01(\x0b\x32#.lq.ResMoveCombiningCraft.BonusData\x1a*\n\tBonusData\x12\x10\n\x08\x63raft_id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x01(\r\"<\n\x18ReqCombiningRecycleCraft\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x01(\r\"]\n\x18ResCombiningRecycleCraft\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x0creward_items\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"1\n\x1aReqRecoverCombiningRecycle\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"U\n\x1aResRecoverCombiningRecycle\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x10\n\x08\x63raft_id\x18\x02 \x01(\r\x12\x0b\n\x03pos\x18\x03 \x01(\r\"T\n\x17ReqFinishCombiningOrder\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\tcraft_pos\x18\x02 \x01(\r\x12\x11\n\torder_pos\x18\x03 \x01(\r\"\\\n\x17ResFinishCombiningOrder\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x0creward_items\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"\xf3\x0b\n\x0cResFetchInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12&\n\x0bserver_time\x18\x02 \x01(\x0b\x32\x11.lq.ResServerTime\x12-\n\x0eserver_setting\x18\x03 \x01(\x0b\x32\x15.lq.ResServerSettings\x12(\n\x0c\x63lient_value\x18\x04 \x01(\x0b\x32\x12.lq.ResClientValue\x12&\n\x0b\x66riend_list\x18\x05 \x01(\x0b\x32\x11.lq.ResFriendList\x12\x31\n\x11\x66riend_apply_list\x18\x06 \x01(\x0b\x32\x16.lq.ResFriendApplyList\x12/\n\rrecent_friend\x18\x07 \x01(\x0b\x32\x18.lq.ResFetchrecentFriend\x12\"\n\tmail_info\x18\x08 \x01(\x0b\x32\x0f.lq.ResMailInfo\x12\x30\n\x11receive_coin_info\x18\t \x01(\x0b\x32\x15.lq.ResReviveCoinInfo\x12$\n\ntitle_list\x18\n \x01(\x0b\x32\x10.lq.ResTitleList\x12 \n\x08\x62\x61g_info\x18\x0b \x01(\x0b\x32\x0e.lq.ResBagInfo\x12\"\n\tshop_info\x18\x0c \x01(\x0b\x32\x0f.lq.ResShopInfo\x12/\n\rshop_interval\x18\r \x01(\x0b\x32\x18.lq.ResFetchShopInterval\x12\x31\n\ractivity_data\x18\x0e \x01(\x0b\x32\x1a.lq.ResAccountActivityData\x12\x37\n\x11\x61\x63tivity_interval\x18\x0f \x01(\x0b\x32\x1c.lq.ResFetchActivityInterval\x12*\n\ractivity_buff\x18\x10 \x01(\x0b\x32\x13.lq.ResActivityBuff\x12$\n\nvip_reward\x18\x11 \x01(\x0b\x32\x10.lq.ResVipReward\x12\x31\n\x11month_ticket_info\x18\x12 \x01(\x0b\x32\x16.lq.ResMonthTicketInfo\x12\'\n\x0b\x61\x63hievement\x18\x13 \x01(\x0b\x32\x12.lq.ResAchievement\x12.\n\x0f\x63omment_setting\x18\x14 \x01(\x0b\x32\x15.lq.ResCommentSetting\x12\x30\n\x10\x61\x63\x63ount_settings\x18\x15 \x01(\x0b\x32\x16.lq.ResAccountSettings\x12\x31\n\x11mod_nickname_time\x18\x16 \x01(\x0b\x32\x16.lq.ResModNicknameTime\x12\x19\n\x04misc\x18\x17 \x01(\x0b\x32\x0b.lq.ResMisc\x12)\n\x0c\x61nnouncement\x18\x18 \x01(\x0b\x32\x13.lq.ResAnnouncement\x12*\n\ractivity_list\x18\x1a \x01(\x0b\x32\x13.lq.ResActivityList\x12,\n\x0e\x63haracter_info\x18\x1b \x01(\x0b\x32\x14.lq.ResCharacterInfo\x12/\n\x10\x61ll_common_views\x18\x1c \x01(\x0b\x32\x15.lq.ResAllcommonViews\x12\x42\n\x1a\x63ollected_game_record_list\x18\x1d \x01(\x0b\x32\x1e.lq.ResCollectedGameRecordList\x12\x33\n\x0fmaintain_notice\x18\x1e \x01(\x0b\x32\x1a.lq.ResFetchMaintainNotice\x12\x30\n\x10random_character\x18\x1f \x01(\x0b\x32\x16.lq.ResRandomCharacter\x12;\n\x10maintenance_info\x18  \x01(\x0b\x32!.lq.ResFetchServerMaintenanceInfo\x12\'\n\tseer_info\x18! \x01(\x0b\x32\x14.lq.ResFetchSeerInfo\x12\x38\n\x12\x61nnual_report_info\x18\" \x01(\x0b\x32\x1c.lq.ResFetchAnnualReportInfo\"k\n\x10ResFetchSeerInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x14\n\x0cremain_count\x18\x02 \x01(\r\x12\x12\n\ndate_limit\x18\x03 \x01(\r\x12\x13\n\x0b\x65xpire_time\x18\x04 \x01(\r\"\xbb\x01\n\x1dResFetchServerMaintenanceInfo\x12]\n\x14\x66unction_maintenance\x18\x01 \x03(\x0b\x32?.lq.ResFetchServerMaintenanceInfo.ServerFunctionMaintenanceInfo\x1a;\n\x1dServerFunctionMaintenanceInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04open\x18\x02 \x01(\x08\"E\n\x19ReqUpgradeVillageBuilding\x12\x13\n\x0b\x62uilding_id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"l\n\x1fReqReceiveVillageBuildingReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x13\n\x0b\x62uilding_id\x18\x02 \x01(\r\x12\x1f\n\x07rewards\x18\x03 \x03(\x0b\x32\x0e.lq.RewardSlot\"d\n\x1fResReceiveVillageBuildingReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x0creward_items\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"8\n\x13ReqStartVillageTrip\x12\x0c\n\x04\x64\x65st\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"d\n\x1bReqReceiveVillageTripReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07\x64\x65st_id\x18\x02 \x01(\r\x12\x1f\n\x07rewards\x18\x03 \x03(\x0b\x32\x0e.lq.RewardSlot\"`\n\x1bResReceiveVillageTripReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x0creward_items\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\">\n\x16ReqCompleteVillageTask\x12\x0f\n\x07task_id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"[\n\x16ResCompleteVillageTask\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x0creward_items\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"D\n\x17ReqGetFriendVillageData\x12\x14\n\x0c\x61\x63\x63ount_list\x18\x01 \x03(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\"\xa8\x01\n\x17ResGetFriendVillageData\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12;\n\x04list\x18\x02 \x03(\x0b\x32-.lq.ResGetFriendVillageData.FriendVillageData\x1a\x36\n\x11\x46riendVillageData\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\"S\n\x13ReqSetVillageWorker\x12\x13\n\x0b\x62uilding_id\x18\x01 \x01(\r\x12\x12\n\nworker_pos\x18\x02 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x03 \x01(\r\"o\n\x13ResSetVillageWorker\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12)\n\x08\x62uilding\x18\x02 \x01(\x0b\x32\x17.lq.VillageBuildingData\x12\x13\n\x0bupdate_time\x18\x03 \x01(\r\"*\n\x13ReqNextRoundVillage\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"_\n\x13ResNextRoundVillage\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12.\n\ractivity_data\x18\x02 \x01(\x0b\x32\x17.lq.ActivityVillageData\"U\n\"ReqResolveFestivalActivityProposal\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\x12\x0e\n\x06select\x18\x03 \x01(\r\"\x9d\x01\n\"ResResolveFestivalActivityProposal\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\reffected_buff\x18\x02 \x03(\r\x12\x0e\n\x06result\x18\x03 \x01(\r\x12\'\n\x0creward_items\x18\x04 \x03(\x0b\x32\x11.lq.ExecuteResult\x12\r\n\x05level\x18\x05 \x01(\r\"R\n\x1fReqResolveFestivalActivityEvent\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\x12\x0e\n\x06select\x18\x03 \x01(\r\"\x9d\x01\n\x1fResResolveFestivalActivityEvent\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x15\n\reffected_buff\x18\x02 \x03(\r\x12\'\n\x0creward_items\x18\x04 \x03(\x0b\x32\x11.lq.ExecuteResult\x12\x11\n\tending_id\x18\x05 \x01(\r\x12\r\n\x05level\x18\x06 \x01(\r\"-\n\x16ReqBuyFestivalProposal\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"b\n\x16ResBuyFestivalProposal\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12.\n\x0cnew_proposal\x18\x02 \x01(\x0b\x32\x18.lq.FestivalProposalData\"=\n\x15ReqIslandActivityMove\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07zone_id\x18\x02 \x01(\r\"\xb7\x01\n\x14ReqIslandActivityBuy\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x30\n\x05items\x18\x02 \x03(\x0b\x32!.lq.ReqIslandActivityBuy.BuyItems\x1aX\n\x08\x42uyItems\x12\x10\n\x08goods_id\x18\x02 \x01(\r\x12\x0b\n\x03pos\x18\x03 \x03(\r\x12\x0e\n\x06rotate\x18\x04 \x01(\r\x12\x0e\n\x06\x62\x61g_id\x18\x05 \x01(\r\x12\r\n\x05price\x18\x06 \x01(\r\"\x96\x01\n\x15ReqIslandActivitySell\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x31\n\x05items\x18\x02 \x03(\x0b\x32\".lq.ReqIslandActivitySell.SellItem\x1a\x35\n\x08SellItem\x12\x0e\n\x06\x62\x61g_id\x18\x02 \x01(\r\x12\n\n\x02id\x18\x03 \x01(\r\x12\r\n\x05price\x18\x04 \x01(\r\"\x85\x02\n\x18ReqIslandActivityTidyBag\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x36\n\x08\x62\x61g_data\x18\x02 \x03(\x0b\x32$.lq.ReqIslandActivityTidyBag.BagData\x1a\x9b\x01\n\x07\x42\x61gData\x12\x0e\n\x06\x62\x61g_id\x18\x02 \x01(\r\x12<\n\x05items\x18\x03 \x03(\x0b\x32-.lq.ReqIslandActivityTidyBag.BagData.ITemData\x12\r\n\x05\x64rops\x18\x04 \x03(\r\x1a\x33\n\x08ITemData\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03pos\x18\x02 \x03(\r\x12\x0e\n\x06rotate\x18\x03 \x01(\r\"R\n\x1eReqIslandActivityUnlockBagGrid\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0e\n\x06\x62\x61g_id\x18\x02 \x01(\r\x12\x0b\n\x03pos\x18\x03 \x03(\r\"\x9d\x01\n\x0e\x43ontestSetting\x12\x32\n\x0blevel_limit\x18\x01 \x03(\x0b\x32\x1d.lq.ContestSetting.LevelLimit\x12\x12\n\ngame_limit\x18\x02 \x01(\r\x12\x18\n\x10system_broadcast\x18\x03 \x01(\r\x1a)\n\nLevelLimit\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"\xe0\x01\n\x1aReqCreateCustomizedContest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\topen_show\x18\x02 \x01(\r\x12\'\n\x11game_rule_setting\x18\x03 \x01(\x0b\x32\x0c.lq.GameMode\x12\x12\n\nstart_time\x18\x04 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x05 \x01(\r\x12\x12\n\nauto_match\x18\x06 \x01(\r\x12\x11\n\trank_rule\x18\x07 \x01(\r\x12+\n\x0f\x63ontest_setting\x18\x08 \x01(\x0b\x32\x12.lq.ContestSetting\"I\n\x1aResCreateCustomizedContest\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x11\n\tunique_id\x18\x02 \x01(\r\"4\n$ReqFetchmanagerCustomizedContestList\x12\x0c\n\x04lang\x18\x01 \x01(\t\"m\n$ResFetchManagerCustomizedContestList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12+\n\x08\x63ontests\x18\x02 \x03(\x0b\x32\x19.lq.CustomizedContestBase\"5\n ReqFetchManagerCustomizedContest\x12\x11\n\tunique_id\x18\x01 \x01(\r\"\xac\x02\n ResFetchManagerCustomizedContest\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\topen_show\x18\x03 \x01(\r\x12\'\n\x11game_rule_setting\x18\x04 \x01(\x0b\x32\x0c.lq.GameMode\x12\x12\n\nstart_time\x18\x05 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x06 \x01(\r\x12\x12\n\nauto_match\x18\x07 \x01(\r\x12\x11\n\trank_rule\x18\x08 \x01(\r\x12\x13\n\x0b\x63heck_state\x18\t \x01(\r\x12\x15\n\rchecking_name\x18\n \x01(\t\x12+\n\x0f\x63ontest_setting\x18\x0b \x01(\x0b\x32\x12.lq.ContestSetting\"\xfa\x01\n!ReqUpdateManagerCustomizedContest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\topen_show\x18\x02 \x01(\r\x12\'\n\x11game_rule_setting\x18\x03 \x01(\x0b\x32\x0c.lq.GameMode\x12\x12\n\nstart_time\x18\x04 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x05 \x01(\r\x12\x11\n\tunique_id\x18\x06 \x01(\r\x12\x12\n\nauto_match\x18\x07 \x01(\r\x12\x11\n\trank_rule\x18\x08 \x01(\r\x12+\n\x0f\x63ontest_setting\x18\t \x01(\x0b\x32\x12.lq.ContestSetting\"M\n\x19ReqFetchContestPlayerRank\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\"\xd9\x06\n\x19ResFetchContestPlayerRank\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05total\x18\x02 \x01(\r\x12\x36\n\x04rank\x18\x03 \x03(\x0b\x32(.lq.ResFetchContestPlayerRank.SeasonRank\x12=\n\x0bplayer_data\x18\x04 \x01(\x0b\x32(.lq.ResFetchContestPlayerRank.PlayerData\x1a\xbf\x03\n\x18\x43ontestPlayerAccountData\x12\x18\n\x10total_game_count\x18\x01 \x01(\r\x12^\n\x0crecent_games\x18\x02 \x03(\x0b\x32H.lq.ResFetchContestPlayerRank.ContestPlayerAccountData.ContestGameResult\x12m\n\x15highest_series_points\x18\x03 \x03(\x0b\x32N.lq.ResFetchContestPlayerRank.ContestPlayerAccountData.ContestSeriesGameResult\x1a\x36\n\x11\x43ontestGameResult\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x13\n\x0btotal_point\x18\x02 \x01(\x05\x1a\x81\x01\n\x17\x43ontestSeriesGameResult\x12\x0b\n\x03key\x18\x01 \x01(\r\x12Y\n\x07results\x18\x02 \x03(\x0b\x32H.lq.ResFetchContestPlayerRank.ContestPlayerAccountData.ContestGameResult\x1ax\n\nSeasonRank\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x44\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32\x36.lq.ResFetchContestPlayerRank.ContestPlayerAccountData\x1a`\n\nPlayerData\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x44\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x36.lq.ResFetchContestPlayerRank.ContestPlayerAccountData\",\n\x17ReqFetchReadyPlayerList\x12\x11\n\tunique_id\x18\x01 \x01(\r\"\x95\x01\n\x17ResFetchReadyPlayerList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x30\n\x04list\x18\x02 \x03(\x0b\x32\".lq.ResFetchReadyPlayerList.Player\x1a.\n\x06Player\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08nickname\x18\x02 \x01(\t\"~\n\x11ReqCreateGamePlan\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x14\n\x0c\x61\x63\x63ount_list\x18\x02 \x03(\r\x12\x17\n\x0fgame_start_time\x18\x03 \x01(\r\x12\x15\n\rshuffle_seats\x18\x04 \x01(\r\x12\x10\n\x08\x61i_level\x18\x05 \x01(\r\"L\n\"ResGenerateContestManagerLoginCode\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\"1\n\x1aReqAmuletActivityFetchInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\\\n\x1aResAmuletActivityFetchInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x16.lq.ActivityAmuletData\"2\n\x1bReqAmuletActivityFetchBrief\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\xd2\x01\n\x1bResAmuletActivityFetchBrief\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12.\n\x07upgrade\x18\x04 \x01(\x0b\x32\x1d.lq.ActivityAmuletUpgradeData\x12?\n\x10illustrated_book\x18\x05 \x01(\x0b\x32%.lq.ActivityAmuletIllustratedBookData\x12(\n\x04task\x18\x06 \x01(\x0b\x32\x1a.lq.ActivityAmuletTaskData\"1\n\x1aReqAmuletActivityStartGame\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"X\n\x1aResAmuletActivityStartGame\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12 \n\x04game\x18\x02 \x01(\x0b\x32\x12.lq.AmuletGameData\"K\n\x18ReqAmuletActivityOperate\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0c\n\x04tile\x18\x03 \x03(\r\"\xad\x03\n\x18ResAmuletActivityOperate\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12.\n\thu_result\x18\x02 \x01(\x0b\x32\x1b.lq.AmuletHuleOperateResult\x12\x30\n\x0bgang_result\x18\x03 \x01(\x0b\x32\x1b.lq.AmuletGangOperateResult\x12-\n\x0b\x64\x65\x61l_result\x18\x04 \x01(\x0b\x32\x18.lq.AmuletDealTileResult\x12/\n\x0eupgrade_result\x18\x05 \x01(\x0b\x32\x17.lq.AmuletUpgradeResult\x12\x10\n\x08upgraded\x18\x06 \x01(\x08\x12\x0e\n\x06\x66\x61iled\x18\x07 \x01(\x08\x12-\n\x0bgame_update\x18\x08 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\x12\x33\n\x0e\x64iscard_result\x18\t \x01(\x0b\x32\x1b.lq.AmuletDiscardTileResult\x12/\n\x0cstart_result\x18\n \x01(\x0b\x32\x19.lq.AmuletStartGameResult\"B\n\x1cReqAmuletActivityChangeHands\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05hands\x18\x02 \x03(\r\"\xc3\x01\n\x1cResAmuletActivityChangeHands\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05hands\x18\x02 \x03(\r\x12 \n\x18remain_change_tile_count\x18\x03 \x01(\r\x12-\n\tting_list\x18\x04 \x03(\x0b\x32\x1a.lq.AmuletActivityTingInfo\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\x05 \x03(\x0b\x32\x14.lq.AmuletEffectData\"/\n\x18ReqAmuletActivityUpgrade\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x89\x01\n\x18ResAmuletActivityUpgrade\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12 \n\x04game\x18\x02 \x01(\x0b\x32\x12.lq.AmuletGameData\x12\x31\n\x0bhook_effect\x18\x03 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\">\n\x1bReqAmuletActivitySelectPack\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\"\x88\x01\n\x1bResAmuletActivitySelectPack\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\x02 \x03(\x0b\x32\x14.lq.AmuletEffectData\x12$\n\x04shop\x18\x03 \x01(\x0b\x32\x16.lq.AmuletGameShopData\"7\n\x14ReqAmuletActivityBuy\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x03 \x01(\r\"\xbb\x01\n\x14ResAmuletActivityBuy\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0c\n\x04\x63oin\x18\x02 \x01(\r\x12$\n\x04shop\x18\x03 \x01(\x0b\x32\x16.lq.AmuletGameShopData\x12\r\n\x05stage\x18\x04 \x01(\r\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\x05 \x03(\x0b\x32\x14.lq.AmuletEffectData\x12\x1b\n\x13total_consumed_coin\x18\x06 \x01(\r\">\n\x1bReqAmuletActivitySellEffect\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\"\x9a\x02\n\x1bResAmuletActivitySellEffect\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0c\n\x04\x63oin\x18\x02 \x01(\r\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\x03 \x03(\x0b\x32\x14.lq.AmuletEffectData\x12-\n\x0bgame_update\x18\x04 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\x12 \n\x18remain_change_tile_count\x18\x05 \x01(\r\x12\x31\n\x0bhook_effect\x18\x06 \x03(\x0b\x32\x1c.lq.AmuletActivityHookEffect\x12$\n\x04shop\x18\x07 \x01(\x0b\x32\x16.lq.AmuletGameShopData\"E\n\x1bReqAmuletActivityEffectSort\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\tsorted_id\x18\x02 \x03(\r\".\n\x17ReqAmuletActivityGiveup\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"3\n\x1cReqAmuletActivityRefreshShop\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x97\x01\n\x1cResAmuletActivityRefreshShop\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x04shop\x18\x02 \x01(\x0b\x32\x16.lq.AmuletGameShopData\x12\x0c\n\x04\x63oin\x18\x03 \x01(\r\x12)\n\x0b\x65\x66\x66\x65\x63t_list\x18\x04 \x03(\x0b\x32\x14.lq.AmuletEffectData\"M\n!ReqAmuletActivitySelectFreeEffect\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x13\n\x0bselected_id\x18\x02 \x01(\r\"\xbe\x01\n!ResAmuletActivitySelectFreeEffect\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12-\n\x0bgame_update\x18\x03 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\x12 \n\x18remain_change_tile_count\x18\x04 \x01(\r\x12\x13\n\x0blocked_tile\x18\x05 \x03(\r\x12\x19\n\x11locked_tile_count\x18\x06 \x01(\r\"C\n ReqAmuletActivityUpgradeShopBuff\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\"\xb6\x01\n ResAmuletActivityUpgradeShopBuff\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12-\n\x0bgame_update\x18\x03 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\x12,\n\x0eshop_buff_list\x18\x04 \x03(\x0b\x32\x14.lq.AmuletEffectData\x12\x1b\n\x13total_consumed_coin\x18\x05 \x01(\r\"3\n\x1cReqAmuletActivityEndShopping\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"g\n\x1cResAmuletActivityEndShopping\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12-\n\x0bgame_update\x18\x03 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\"Y\n\x1eReqAmuletActivitySetSkillLevel\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\"\n\x05skill\x18\x02 \x03(\x0b\x32\x13.lq.AmuletSkillData\"G\n\x1dResAmuletActivityMaintainInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0c\n\x04mode\x18\x02 \x01(\t\"D\n!ReqAmuletActivitySelectRewardPack\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\"\x92\x01\n!ResAmuletActivitySelectRewardPack\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12-\n\x0bgame_update\x18\x02 \x01(\x0b\x32\x18.lq.AmuletGameUpdateData\x12$\n\x04shop\x18\x03 \x01(\x0b\x32\x16.lq.AmuletGameShopData\"L\n\"ReqAmuletActivityReceiveTaskReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\ttask_list\x18\x02 \x03(\r\"h\n\"ResAmuletActivityReceiveTaskReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12(\n\x04task\x18\x02 \x01(\x0b\x32\x1a.lq.ActivityAmuletTaskData\"?\n\x16ReqStoryActivityUnlock\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\"X\n\x1cReqStoryActivityUnlockEnding\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\x12\x11\n\tending_id\x18\x03 \x01(\r\"_\n#ReqStoryActivityReceiveEndingReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\x12\x11\n\tending_id\x18\x03 \x01(\r\"S\n\x0eResStoryReward\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x0creward_items\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\"L\n#ReqStoryActivityReceiveFinishReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\"O\n&ReqStoryActivityReceiveAllFinishReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\"b\n&ReqStoryActivityUnlockEndingAndReceive\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08story_id\x18\x02 \x01(\r\x12\x11\n\tending_id\x18\x03 \x01(\r\"\xc4\x01\n&ResStoryActivityUnlockEndingAndReceive\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12(\n\rending_reward\x18\x02 \x03(\x0b\x32\x11.lq.ExecuteReward\x12(\n\rfinish_reward\x18\x03 \x03(\x0b\x32\x11.lq.ExecuteReward\x12,\n\x11\x61ll_finish_reward\x18\x04 \x03(\x0b\x32\x11.lq.ExecuteReward\"A\n\x14ReqFetchActivityRank\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x14\n\x0c\x61\x63\x63ount_list\x18\x02 \x03(\r\"\xf6\x01\n\x14ResFetchActivityRank\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x38\n\x05items\x18\x04 \x03(\x0b\x32).lq.ResFetchActivityRank.ActivityRankItem\x12\x37\n\x04self\x18\x05 \x01(\x0b\x32).lq.ResFetchActivityRank.ActivityRankItem\x1aQ\n\x10\x41\x63tivityRankItem\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\x04\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\t\x12\x0c\n\x04rank\x18\x04 \x01(\r\":\n\x19ReqFetchQuestionnaireList\x12\x0c\n\x04lang\x18\x01 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x02 \x01(\t\"r\n\x19ResFetchQuestionnaireList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x04list\x18\x02 \x03(\x0b\x32\x16.lq.QuestionnaireBrief\x12\x15\n\rfinished_list\x18\x03 \x03(\r\"H\n\x1bReqFetchQuestionnaireDetail\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04lang\x18\x02 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x03 \x01(\t\"`\n\x1bResFetchQuestionnaireDetail\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x06\x64\x65tail\x18\x02 \x01(\x0b\x32\x17.lq.QuestionnaireDetail\"/\n\x14ReqSetVerifiedHidden\x12\x17\n\x0fverified_hidden\x18\x01 \x01(\r\"\x94\x03\n\x16ReqSubmitQuestionnaire\x12\x18\n\x10questionnaire_id\x18\x01 \x01(\r\x12 \n\x18questionnaire_version_id\x18\x02 \x01(\r\x12?\n\x07\x61nswers\x18\x03 \x03(\x0b\x32..lq.ReqSubmitQuestionnaire.QuestionnaireAnswer\x12\x11\n\topen_time\x18\x04 \x01(\r\x12\x13\n\x0b\x66inish_time\x18\x05 \x01(\r\x12\x0e\n\x06\x63lient\x18\x06 \x01(\t\x1a\xc4\x01\n\x13QuestionnaireAnswer\x12\x13\n\x0bquestion_id\x18\x01 \x01(\r\x12W\n\x06values\x18\x02 \x03(\x0b\x32G.lq.ReqSubmitQuestionnaire.QuestionnaireAnswer.QuestionnaireAnswerValue\x1a?\n\x18QuestionnaireAnswerValue\x12\r\n\x05value\x18\x01 \x01(\t\x12\x14\n\x0c\x63ustom_input\x18\x02 \x01(\t\"<\n\x1dReqSetFriendRoomRandomBotChar\x12\x1b\n\x13\x64isable_random_char\x18\x01 \x01(\r\"L\n\x1cReqFetchAccountGameHuRecords\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\"\x82\x02\n\x1cResFetchAccountGameHuRecords\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12?\n\x07records\x18\x02 \x03(\x0b\x32..lq.ResFetchAccountGameHuRecords.GameHuRecords\x1a\x86\x01\n\rGameHuRecords\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x10\n\x08title_id\x18\x04 \x01(\r\x12\r\n\x05hands\x18\x05 \x03(\t\x12\x0c\n\x04ming\x18\x06 \x03(\t\x12\r\n\x05hupai\x18\x07 \x01(\t\x12\x0f\n\x07hu_fans\x18\x08 \x03(\r\"N\n\x18ReqFetchAccountInfoExtra\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\"\xbb\x06\n\x18ResFetchAccountInfoExtra\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12H\n\x0crecent_games\x18\x02 \x03(\x0b\x32\x32.lq.ResFetchAccountInfoExtra.AccountInfoGameRecord\x12\x46\n\x0fhu_type_details\x18\x03 \x03(\x0b\x32-.lq.ResFetchAccountInfoExtra.GameHuTypeDetail\x12M\n\x11game_rank_details\x18\x04 \x03(\x0b\x32\x32.lq.ResFetchAccountInfoExtra.AccountGameRankDetail\x1a\xbc\x03\n\x15\x41\x63\x63ountInfoGameRecord\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\r\x12\x0b\n\x03tag\x18\x04 \x01(\r\x12\x0f\n\x07sub_tag\x18\x05 \x01(\r\x12\x0c\n\x04rank\x18\x06 \x01(\r\x12\x13\n\x0b\x66inal_point\x18\x07 \x01(\r\x12U\n\x07results\x18\x08 \x03(\x0b\x32\x44.lq.ResFetchAccountInfoExtra.AccountInfoGameRecord.AccountGameResult\x1a\xd6\x01\n\x11\x41\x63\x63ountGameResult\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x12\n\naccount_id\x18\x02 \x01(\r\x12\x10\n\x08nickname\x18\x03 \x01(\t\x12\x10\n\x08verified\x18\x04 \x01(\r\x12\x15\n\rgrading_score\x18\x05 \x01(\x05\x12\x13\n\x0b\x66inal_point\x18\x06 \x01(\x05\x12\x0c\n\x04seat\x18\x07 \x01(\r\x12\x1f\n\x05level\x18\x08 \x01(\x0b\x32\x10.lq.AccountLevel\x12 \n\x06level3\x18\t \x01(\x0b\x32\x10.lq.AccountLevel\x1a/\n\x10GameHuTypeDetail\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x1a\x34\n\x15\x41\x63\x63ountGameRankDetail\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"}\n\x17ReqSetAccountFavoriteHu\x12\x0c\n\x04mode\x18\x01 \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\x12\x0c\n\x04uuid\x18\x04 \x01(\t\x12\r\n\x05\x63hang\x18\x05 \x01(\r\x12\n\n\x02ju\x18\x06 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x07 \x01(\r\"\"\n\x12ReqFetchSeerReport\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"N\n\x12ResFetchSeerReport\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x1e\n\x06report\x18\x02 \x01(\x0b\x32\x0e.lq.SeerReport\"#\n\x13ReqCreateSeerReport\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"S\n\x13ResCreateSeerReport\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x0bseer_report\x18\x02 \x01(\x0b\x32\r.lq.SeerBrief\"[\n\x16ResFetchSeerReportList\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\x10seer_report_list\x18\x02 \x03(\x0b\x32\r.lq.SeerBrief\"R\n\x16ReqSelectChestChooseUp\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\tselection\x18\x02 \x01(\r\x12\x10\n\x08\x63hest_id\x18\x03 \x01(\r\",\n\x1cReqGenerateAnnualReportToken\x12\x0c\n\x04lang\x18\x01 \x01(\t\"T\n\x1cResGenerateAnnualReportToken\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05token\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\"Z\n\x18ResFetchAnnualReportInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\r\"5\n\x0fReqRemarkFriend\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0e\n\x06remark\x18\x02 \x01(\t\"0\n\x19ReqSimV2ActivityFetchInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"Y\n\x19ResSimV2ActivityFetchInfo\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\"\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x14.lq.SimulationV2Data\"2\n\x1bReqSimV2ActivityStartSeason\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"c\n\x1bResSimV2ActivityStartSeason\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12*\n\x06season\x18\x02 \x01(\x0b\x32\x1a.lq.SimulationV2SeasonData\"K\n\x15ReqSimV2ActivityTrain\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07\x61\x62ility\x18\x02 \x01(\r\x12\x0c\n\x04skip\x18\x03 \x01(\r\"\x8b\x02\n\x15ResSimV2ActivityTrain\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x05\x65vent\x18\x02 \x01(\x0b\x32\x15.lq.SimulationV2Event\x12(\n\x07\x61\x62ility\x18\x03 \x01(\x0b\x32\x17.lq.SimulationV2Ability\x12\r\n\x05round\x18\x04 \x01(\r\x12+\n\x0b\x65\x66\x66\x65\x63t_list\x18\x05 \x03(\x0b\x32\x16.lq.SimulationV2Effect\x12\x14\n\x0ctrain_result\x18\x06 \x01(\r\x12\x0e\n\x06is_end\x18\x07 \x01(\x08\x12&\n\x06record\x18\x08 \x01(\x0b\x32\x16.lq.SimulationV2Record\"H\n\x1bReqSimV2ActivitySelectEvent\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x14\n\x0cselection_id\x18\x02 \x01(\r\"\xd0\x02\n\x1bResSimV2ActivitySelectEvent\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x05\x65vent\x18\x02 \x01(\x0b\x32\x15.lq.SimulationV2Event\x12(\n\x07\x61\x62ility\x18\x03 \x01(\x0b\x32\x17.lq.SimulationV2Ability\x12$\n\x05match\x18\x04 \x01(\x0b\x32\x15.lq.SimulationV2Match\x12+\n\x0b\x65\x66\x66\x65\x63t_list\x18\x05 \x03(\x0b\x32\x16.lq.SimulationV2Effect\x12\r\n\x05round\x18\x07 \x01(\r\x12\x0e\n\x06is_end\x18\x08 \x01(\x08\x12\x11\n\tresult_id\x18\t \x01(\r\x12&\n\x06record\x18\n \x01(\x0b\x32\x16.lq.SimulationV2Record\x12\x1a\n\x12\x65\x66\x66\x65\x63ted_buff_list\x18\x0b \x03(\r\"1\n\x1aReqSimV2ActivityStartMatch\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\xc5\x01\n\x1aResSimV2ActivityStartMatch\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12$\n\x05\x65vent\x18\x02 \x01(\x0b\x32\x15.lq.SimulationV2Event\x12$\n\x05match\x18\x04 \x01(\x0b\x32\x15.lq.SimulationV2Match\x12+\n\x0b\x65\x66\x66\x65\x63t_list\x18\x05 \x03(\x0b\x32\x16.lq.SimulationV2Effect\x12\x14\n\x0cis_match_end\x18\x06 \x01(\x08\"/\n\x18ReqSimV2ActivityEndMatch\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"\x9b\x03\n\x18ResSimV2ActivityEndMatch\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\r\n\x05round\x18\x02 \x01(\r\x12\x0e\n\x06is_end\x18\x03 \x01(\x08\x12&\n\x06record\x18\x04 \x01(\x0b\x32\x16.lq.SimulationV2Record\x12\x13\n\x0btotal_score\x18\x05 \x01(\x05\x12\x32\n\rmatch_history\x18\x06 \x03(\x0b\x32\x1b.lq.SimulationV2MatchRecord\x12\x45\n\x07rewards\x18\x07 \x03(\x0b\x32\x34.lq.ResSimV2ActivityEndMatch.SimulationV2MatchReward\x12+\n\x0b\x65\x66\x66\x65\x63t_list\x18\x08 \x03(\x0b\x32\x16.lq.SimulationV2Effect\x12(\n\x07\x61\x62ility\x18\t \x01(\x0b\x32\x17.lq.SimulationV2Ability\x1a\x37\n\x17SimulationV2MatchReward\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0e\n\x06params\x18\x02 \x03(\r\"-\n\x16ReqSimV2ActivityGiveUp\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\"[\n\x1aReqSimV2ActivitySetUpgrade\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12(\n\x07upgrade\x18\x02 \x01(\x0b\x32\x17.lq.SimulationV2Ability\"n\n\x0bReqAuthGame\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\r\n\x05token\x18\x02 \x01(\t\x12\x11\n\tgame_uuid\x18\x03 \x01(\t\x12\x0f\n\x07session\x18\x04 \x01(\t\x12\x0c\n\x04gift\x18\x05 \x01(\t\x12\n\n\x02vs\x18\x06 \x01(\r\"\xd6\x01\n\x0bResAuthGame\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12#\n\x07players\x18\x02 \x03(\x0b\x32\x12.lq.PlayerGameView\x12\x11\n\tseat_list\x18\x03 \x03(\r\x12\x15\n\ris_game_start\x18\x04 \x01(\x08\x12#\n\x0bgame_config\x18\x05 \x01(\x0b\x32\x0e.lq.GameConfig\x12\x15\n\rready_id_list\x18\x06 \x03(\r\x12\"\n\x06robots\x18\x07 \x03(\x0b\x32\x12.lq.PlayerGameView\"\xb8\x01\n\x0bGameRestore\x12\"\n\x08snapshot\x18\x01 \x01(\x0b\x32\x10.lq.GameSnapshot\x12$\n\x07\x61\x63tions\x18\x02 \x03(\x0b\x32\x13.lq.ActionPrototype\x12\x1b\n\x13passed_waiting_time\x18\x03 \x01(\r\x12\x12\n\ngame_state\x18\x04 \x01(\r\x12\x12\n\nstart_time\x18\x05 \x01(\r\x12\x1a\n\x12last_pause_time_ms\x18\x06 \x01(\r\"m\n\x0cResEnterGame\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0e\n\x06is_end\x18\x02 \x01(\x08\x12\x0c\n\x04step\x18\x03 \x01(\r\x12%\n\x0cgame_restore\x18\x04 \x01(\x0b\x32\x0f.lq.GameRestore\"-\n\x0bReqSyncGame\x12\x10\n\x08round_id\x18\x01 \x01(\t\x12\x0c\n\x04step\x18\x02 \x01(\r\"l\n\x0bResSyncGame\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0e\n\x06is_end\x18\x02 \x01(\x08\x12\x0c\n\x04step\x18\x03 \x01(\r\x12%\n\x0cgame_restore\x18\x04 \x01(\x0b\x32\x0f.lq.GameRestore\"\xc8\x01\n\x10ReqSelfOperation\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05index\x18\x02 \x01(\r\x12\x0c\n\x04tile\x18\x03 \x01(\t\x12\x18\n\x10\x63\x61ncel_operation\x18\x04 \x01(\x08\x12\r\n\x05moqie\x18\x05 \x01(\x08\x12\x0f\n\x07timeuse\x18\x06 \x01(\r\x12\x12\n\ntile_state\x18\x07 \x01(\x05\x12\x14\n\x0c\x63hange_tiles\x18\x08 \x03(\t\x12\x13\n\x0btile_states\x18\t \x03(\x05\x12\x10\n\x08gap_type\x18\n \x01(\r\"X\n\x0eReqChiPengGang\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05index\x18\x02 \x01(\r\x12\x18\n\x10\x63\x61ncel_operation\x18\x03 \x01(\x08\x12\x0f\n\x07timeuse\x18\x06 \x01(\r\":\n\x12ReqBroadcastInGame\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x13\n\x0b\x65xcept_self\x18\x02 \x01(\x08\")\n\x14ReqGMCommandInGaming\x12\x11\n\tjson_data\x18\x01 \x01(\t\"W\n\x12ResGamePlayerState\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\'\n\nstate_list\x18\x02 \x03(\x0e\x32\x13.lq.GamePlayerState\"\x1d\n\x0eReqVoteGameEnd\x12\x0b\n\x03yes\x18\x01 \x01(\x08\"U\n\x0eResGameEndVote\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x18\n\x10vote_cd_end_time\x18\x02 \x01(\r\x12\x18\n\x05\x65rror\x18\x03 \x01(\x0b\x32\t.lq.Error\"\x1f\n\x0eReqAuthObserve\x12\r\n\x05token\x18\x01 \x01(\t\"V\n\x0fResStartObserve\x12\x1e\n\x04head\x18\x01 \x01(\x0b\x32\x10.lq.GameLiveHead\x12#\n\x06passed\x18\x02 \x01(\x0b\x32\x13.lq.GameLiveSegment\"7\n\rNotifyNewGame\x12\x11\n\tgame_uuid\x18\x01 \x01(\t\x12\x13\n\x0bplayer_list\x18\x02 \x03(\t\"2\n\x19NotifyPlayerLoadGameReady\x12\x15\n\rready_id_list\x18\x01 \x03(\r\"4\n\x13NotifyGameBroadcast\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\"8\n\x13NotifyGameEndResult\x12!\n\x06result\x18\x01 \x01(\x0b\x32\x11.lq.GameEndResult\"%\n\x13NotifyGameTerminate\x12\x0e\n\x06reason\x18\x01 \x01(\t\"O\n\x1bNotifyPlayerConnectionState\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\"\n\x05state\x18\x02 \x01(\x0e\x32\x13.lq.GamePlayerState\"k\n\x18NotifyAccountLevelChange\x12 \n\x06origin\x18\x01 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x1f\n\x05\x66inal\x18\x02 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x0c\n\x04type\x18\x03 \x01(\r\"\xae\x05\n\x16NotifyGameFinishReward\x12\x0f\n\x07mode_id\x18\x01 \x01(\r\x12<\n\x0clevel_change\x18\x02 \x01(\x0b\x32&.lq.NotifyGameFinishReward.LevelChange\x12:\n\x0bmatch_chest\x18\x03 \x01(\x0b\x32%.lq.NotifyGameFinishReward.MatchChest\x12@\n\x0emain_character\x18\x04 \x01(\x0b\x32(.lq.NotifyGameFinishReward.MainCharacter\x12@\n\x0e\x63haracter_gift\x18\x05 \x01(\x0b\x32(.lq.NotifyGameFinishReward.CharacterGift\x12(\n\x06\x62\x61\x64ges\x18\x06 \x03(\x0b\x32\x18.lq.BadgeAchieveProgress\x1a^\n\x0bLevelChange\x12 \n\x06origin\x18\x01 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x1f\n\x05\x66inal\x18\x02 \x01(\x0b\x32\x10.lq.AccountLevel\x12\x0c\n\x04type\x18\x03 \x01(\r\x1aq\n\nMatchChest\x12\x10\n\x08\x63hest_id\x18\x01 \x01(\r\x12\x0e\n\x06origin\x18\x02 \x01(\r\x12\r\n\x05\x66inal\x18\x03 \x01(\r\x12\x11\n\tis_graded\x18\x04 \x01(\x08\x12\x1f\n\x07rewards\x18\x05 \x03(\x0b\x32\x0e.lq.RewardSlot\x1a\x38\n\rMainCharacter\x12\r\n\x05level\x18\x01 \x01(\r\x12\x0b\n\x03\x65xp\x18\x02 \x01(\r\x12\x0b\n\x03\x61\x64\x64\x18\x03 \x01(\r\x1aN\n\rCharacterGift\x12\x0e\n\x06origin\x18\x01 \x01(\r\x12\r\n\x05\x66inal\x18\x02 \x01(\r\x12\x0b\n\x03\x61\x64\x64\x18\x03 \x01(\r\x12\x11\n\tis_graded\x18\x04 \x01(\x08\"\xa0\x01\n\x14NotifyActivityReward\x12@\n\x0f\x61\x63tivity_reward\x18\x01 \x03(\x0b\x32\'.lq.NotifyActivityReward.ActivityReward\x1a\x46\n\x0e\x41\x63tivityReward\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x1f\n\x07rewards\x18\x02 \x03(\x0b\x32\x0e.lq.RewardSlot\"\x8a\x01\n\x13NotifyActivityPoint\x12>\n\x0f\x61\x63tivity_points\x18\x01 \x03(\x0b\x32%.lq.NotifyActivityPoint.ActivityPoint\x1a\x33\n\rActivityPoint\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\"\x9c\x01\n\x16NotifyLeaderboardPoint\x12G\n\x12leaderboard_points\x18\x01 \x03(\x0b\x32+.lq.NotifyLeaderboardPoint.LeaderboardPoint\x1a\x39\n\x10LeaderboardPoint\x12\x16\n\x0eleaderboard_id\x18\x01 \x01(\r\x12\r\n\x05point\x18\x02 \x01(\r\"!\n\x0fNotifyGamePause\x12\x0e\n\x06paused\x18\x01 \x01(\x08\"\xa0\x01\n\x11NotifyEndGameVote\x12\x31\n\x07results\x18\x01 \x03(\x0b\x32 .lq.NotifyEndGameVote.VoteResult\x12\x12\n\nstart_time\x18\x02 \x01(\r\x12\x15\n\rduration_time\x18\x03 \x01(\r\x1a-\n\nVoteResult\x12\x12\n\naccount_id\x18\x01 \x01(\r\x12\x0b\n\x03yes\x18\x02 \x01(\x08\"3\n\x11NotifyObserveData\x12\x1e\n\x04unit\x18\x01 \x01(\x0b\x32\x10.lq.GameLiveUnit\"\x0f\n\rActionMJStart\"A\n\x13NewRoundOpenedTiles\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\r\n\x05tiles\x18\x02 \x03(\t\x12\r\n\x05\x63ount\x18\x03 \x03(\r\"F\n\x08MuyuInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x11\n\tcount_max\x18\x03 \x01(\r\x12\n\n\x02id\x18\x04 \x01(\r\"\x8a\x01\n\x0b\x43huanmaGang\x12\x12\n\nold_scores\x18\x01 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x02 \x03(\x05\x12\x0e\n\x06scores\x18\x03 \x03(\x05\x12\x1c\n\x07gameend\x18\x04 \x01(\x0b\x32\x0b.lq.GameEnd\x12#\n\rhules_history\x18\x05 \x03(\x0b\x32\x0c.lq.HuleInfo\"u\n\rYongchangInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x13\n\x0bmoqie_count\x18\x02 \x01(\r\x12\x13\n\x0bmoqie_bonus\x18\x03 \x01(\r\x12\x15\n\rshouqie_count\x18\x04 \x01(\r\x12\x15\n\rshouqie_bonus\x18\x05 \x01(\r\"$\n\rActionNewCard\x12\x13\n\x0b\x66ield_spell\x18\x01 \x01(\r\"$\n\rRecordNewCard\x12\x13\n\x0b\x66ield_spell\x18\x01 \x01(\r\"\xeb\x03\n\x0e\x41\x63tionNewRound\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\r\n\x05tiles\x18\x04 \x03(\t\x12\x0c\n\x04\x64ora\x18\x05 \x01(\t\x12\x0e\n\x06scores\x18\x06 \x03(\x05\x12,\n\toperation\x18\x07 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x10\n\x08liqibang\x18\x08 \x01(\r\x12)\n\ttingpais0\x18\t \x03(\x0b\x32\x16.lq.TingPaiDiscardInfo\x12\"\n\ttingpais1\x18\n \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\n\n\x02\x61l\x18\x0b \x01(\x08\x12\x0b\n\x03md5\x18\x0c \x01(\t\x12\x17\n\x0fleft_tile_count\x18\r \x01(\r\x12\r\n\x05\x64oras\x18\x0e \x03(\t\x12&\n\x05opens\x18\x0f \x03(\x0b\x32\x17.lq.NewRoundOpenedTiles\x12\x1a\n\x04muyu\x18\x10 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x10\n\x08ju_count\x18\x11 \x01(\r\x12\x13\n\x0b\x66ield_spell\x18\x12 \x01(\r\x12\x0e\n\x06sha256\x18\x13 \x01(\t\x12$\n\tyongchang\x18\x14 \x01(\x0b\x32\x11.lq.YongchangInfo\x12\x13\n\x0bsalt_sha256\x18\x15 \x01(\t\"\xf9\x04\n\x0eRecordNewRound\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x0c\n\x04\x64ora\x18\x04 \x01(\t\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\x10\n\x08liqibang\x18\x06 \x01(\r\x12\x0e\n\x06tiles0\x18\x07 \x03(\t\x12\x0e\n\x06tiles1\x18\x08 \x03(\t\x12\x0e\n\x06tiles2\x18\t \x03(\t\x12\x0e\n\x06tiles3\x18\n \x03(\t\x12+\n\x07tingpai\x18\x0b \x03(\x0b\x32\x1a.lq.RecordNewRound.TingPai\x12,\n\toperation\x18\x0c \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x0b\n\x03md5\x18\r \x01(\t\x12\x0f\n\x07paishan\x18\x0e \x01(\t\x12\x17\n\x0fleft_tile_count\x18\x0f \x01(\r\x12\r\n\x05\x64oras\x18\x10 \x03(\t\x12&\n\x05opens\x18\x11 \x03(\x0b\x32\x17.lq.NewRoundOpenedTiles\x12\x1a\n\x04muyu\x18\x12 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12-\n\noperations\x18\x13 \x03(\x0b\x32\x19.lq.OptionalOperationList\x12\x10\n\x08ju_count\x18\x14 \x01(\r\x12\x13\n\x0b\x66ield_spell\x18\x15 \x01(\r\x12\x0e\n\x06sha256\x18\x16 \x01(\t\x12$\n\tyongchang\x18\x17 \x01(\x0b\x32\x11.lq.YongchangInfo\x12\x13\n\x0bsalt_sha256\x18\x18 \x01(\t\x12\x0c\n\x04salt\x18\x19 \x01(\t\x1a;\n\x07TingPai\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\"\n\ttingpais1\x18\x02 \x03(\x0b\x32\x0f.lq.TingPaiInfo\"\x99\x03\n\x0cGameSnapshot\x12\r\n\x05\x63hang\x18\x01 \x01(\r\x12\n\n\x02ju\x18\x02 \x01(\r\x12\x0b\n\x03\x62\x65n\x18\x03 \x01(\r\x12\x14\n\x0cindex_player\x18\x04 \x01(\r\x12\x17\n\x0fleft_tile_count\x18\x05 \x01(\r\x12\r\n\x05hands\x18\x06 \x03(\t\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x10\n\x08liqibang\x18\x08 \x01(\r\x12\x30\n\x07players\x18\t \x03(\x0b\x32\x1f.lq.GameSnapshot.PlayerSnapshot\x12\x10\n\x08zhenting\x18\n \x01(\x08\x1a\xbd\x01\n\x0ePlayerSnapshot\x12\r\n\x05score\x18\x01 \x01(\x05\x12\x14\n\x0cliqiposition\x18\x02 \x01(\x05\x12\x0f\n\x07tilenum\x18\x03 \x01(\r\x12\x0e\n\x06qipais\x18\x04 \x03(\t\x12\x33\n\x05mings\x18\x05 \x03(\x0b\x32$.lq.GameSnapshot.PlayerSnapshot.Fulu\x1a\x30\n\x04\x46ulu\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x03(\t\x12\x0c\n\x04\x66rom\x18\x03 \x03(\r\";\n\x0f\x41\x63tionPrototype\x12\x0c\n\x04step\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\"c\n\x11GameDetailRecords\x12\x0f\n\x07records\x18\x01 \x03(\x0c\x12\x0f\n\x07version\x18\x02 \x01(\r\x12\x1f\n\x07\x61\x63tions\x18\x03 \x03(\x0b\x32\x0e.lq.GameAction\x12\x0b\n\x03\x62\x61r\x18\x04 \x01(\x0c\"\xc9\x01\n\x11GameSelfOperation\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05index\x18\x02 \x01(\r\x12\x0c\n\x04tile\x18\x03 \x01(\t\x12\x18\n\x10\x63\x61ncel_operation\x18\x04 \x01(\x08\x12\r\n\x05moqie\x18\x05 \x01(\x08\x12\x0f\n\x07timeuse\x18\x06 \x01(\r\x12\x12\n\ntile_state\x18\x07 \x01(\x05\x12\x14\n\x0c\x63hange_tiles\x18\x08 \x03(\t\x12\x13\n\x0btile_states\x18\t \x03(\x05\x12\x10\n\x08gap_type\x18\n \x01(\r\"Y\n\x0fGameChiPengGang\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05index\x18\x02 \x01(\r\x12\x18\n\x10\x63\x61ncel_operation\x18\x03 \x01(\x08\x12\x0f\n\x07timeuse\x18\x06 \x01(\r\"\x1e\n\x0fGameVoteGameEnd\x12\x0b\n\x03yes\x18\x01 \x01(\x08\"\xa7\x01\n\rGameUserInput\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0b\n\x03\x65mo\x18\x03 \x01(\r\x12(\n\toperation\x18\n \x01(\x0b\x32\x15.lq.GameSelfOperation\x12 \n\x03\x63pg\x18\x0b \x01(\x0b\x32\x13.lq.GameChiPengGang\x12!\n\x04vote\x18\x0c \x01(\x0b\x32\x13.lq.GameVoteGameEnd\"+\n\rGameUserEvent\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\"\x9c\x01\n\nGameAction\x12\x0e\n\x06passed\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0e\n\x06result\x18\x03 \x01(\x0c\x12%\n\nuser_input\x18\x04 \x01(\x0b\x32\x11.lq.GameUserInput\x12%\n\nuser_event\x18\x05 \x01(\x0b\x32\x11.lq.GameUserEvent\x12\x12\n\ngame_event\x18\x06 \x01(\r\"z\n\x11OptionalOperation\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x13\n\x0b\x63ombination\x18\x02 \x03(\t\x12\x14\n\x0c\x63hange_tiles\x18\x03 \x03(\t\x12\x1a\n\x12\x63hange_tile_states\x18\x04 \x03(\x05\x12\x10\n\x08gap_type\x18\x05 \x01(\r\"z\n\x15OptionalOperationList\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12-\n\x0eoperation_list\x18\x02 \x03(\x0b\x32\x15.lq.OptionalOperation\x12\x10\n\x08time_add\x18\x04 \x01(\r\x12\x12\n\ntime_fixed\x18\x05 \x01(\r\"n\n\x0bLiQiSuccess\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\x05\x12\x10\n\x08liqibang\x18\x03 \x01(\r\x12\x0e\n\x06\x66\x61iled\x18\x04 \x01(\x08\x12 \n\x18liqi_type_beishuizhizhan\x18\x05 \x01(\r\"0\n\x07\x46\x61nInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\r\x12\n\n\x02id\x18\x03 \x01(\r\"\xe4\x03\n\x08HuleInfo\x12\x0c\n\x04hand\x18\x01 \x03(\t\x12\x0c\n\x04ming\x18\x02 \x03(\t\x12\x0f\n\x07hu_tile\x18\x03 \x01(\t\x12\x0c\n\x04seat\x18\x04 \x01(\r\x12\x0c\n\x04zimo\x18\x05 \x01(\x08\x12\x0e\n\x06qinjia\x18\x06 \x01(\x08\x12\x0c\n\x04liqi\x18\x07 \x01(\x08\x12\r\n\x05\x64oras\x18\x08 \x03(\t\x12\x10\n\x08li_doras\x18\t \x03(\t\x12\r\n\x05yiman\x18\n \x01(\x08\x12\r\n\x05\x63ount\x18\x0b \x01(\r\x12\x19\n\x04\x66\x61ns\x18\x0c \x03(\x0b\x32\x0b.lq.FanInfo\x12\n\n\x02\x66u\x18\r \x01(\r\x12\r\n\x05title\x18\x0e \x01(\t\x12\x12\n\npoint_rong\x18\x0f \x01(\r\x12\x16\n\x0epoint_zimo_qin\x18\x10 \x01(\r\x12\x17\n\x0fpoint_zimo_xian\x18\x11 \x01(\r\x12\x10\n\x08title_id\x18\x12 \x01(\r\x12\x11\n\tpoint_sum\x18\x13 \x01(\r\x12\x0e\n\x06\x64\x61\x64ian\x18\x14 \x01(\r\x12\x0e\n\x06\x62\x61opai\x18\x15 \x01(\r\x12\x14\n\x0c\x62\x61opai_seats\x18\x16 \x03(\r\x12\r\n\x05lines\x18\x17 \x03(\t\x12\x16\n\x0etianming_bonus\x18\x18 \x01(\r\x12\x15\n\rbaida_changed\x18\x19 \x03(\t\x12\x1e\n\x16hu_tile_bai_da_changed\x18\x1a \x01(\t\"\xa7\x01\n\x0bTingPaiInfo\x12\x0c\n\x04tile\x18\x01 \x01(\t\x12\x0e\n\x06haveyi\x18\x02 \x01(\x08\x12\r\n\x05yiman\x18\x03 \x01(\x08\x12\r\n\x05\x63ount\x18\x04 \x01(\r\x12\n\n\x02\x66u\x18\x05 \x01(\r\x12\x17\n\x0f\x62iao_dora_count\x18\x06 \x01(\r\x12\x12\n\nyiman_zimo\x18\x07 \x01(\x08\x12\x12\n\ncount_zimo\x18\x08 \x01(\r\x12\x0f\n\x07\x66u_zimo\x18\t \x01(\r\"T\n\x12TingPaiDiscardInfo\x12\x0c\n\x04tile\x18\x01 \x01(\t\x12\x10\n\x08zhenting\x18\x02 \x01(\x08\x12\x1e\n\x05infos\x18\x03 \x03(\x0b\x32\x0f.lq.TingPaiInfo\"Q\n\x12HunZhiYiJiBuffInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x1b\n\x13\x63ontinue_deal_count\x18\x02 \x01(\r\x12\x10\n\x08overload\x18\x03 \x01(\x08\"\x19\n\x07GameEnd\x12\x0e\n\x06scores\x18\x01 \x03(\x05\"\xa1\x01\n\x0f\x41\x63tionSelectGap\x12\x11\n\tgap_types\x18\x01 \x03(\r\x12)\n\ttingpais0\x18\x02 \x03(\x0b\x32\x16.lq.TingPaiDiscardInfo\x12\"\n\ttingpais1\x18\x03 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\"\xbd\x01\n\x0fRecordSelectGap\x12\x11\n\tgap_types\x18\x01 \x03(\r\x12,\n\x07tingpai\x18\x02 \x03(\x0b\x32\x1b.lq.RecordSelectGap.TingPai\x12,\n\toperation\x18\x03 \x01(\x0b\x32\x19.lq.OptionalOperationList\x1a;\n\x07TingPai\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\"\n\ttingpais1\x18\x02 \x03(\x0b\x32\x0f.lq.TingPaiInfo\"\x89\x02\n\x10\x41\x63tionChangeTile\x12\x10\n\x08in_tiles\x18\x01 \x03(\t\x12\x16\n\x0ein_tile_states\x18\x02 \x03(\x05\x12\x11\n\tout_tiles\x18\x03 \x03(\t\x12\x17\n\x0fout_tile_states\x18\x04 \x03(\x05\x12\r\n\x05\x64oras\x18\x05 \x03(\t\x12)\n\ttingpais0\x18\x06 \x03(\x0b\x32\x16.lq.TingPaiDiscardInfo\x12\"\n\ttingpais1\x18\x07 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12,\n\toperation\x18\x08 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x13\n\x0b\x63hange_type\x18\t \x01(\r\"\x9f\x03\n\x10RecordChangeTile\x12\r\n\x05\x64oras\x18\x01 \x03(\t\x12-\n\x07tingpai\x18\x02 \x03(\x0b\x32\x1c.lq.RecordChangeTile.TingPai\x12:\n\x11\x63hange_tile_infos\x18\x03 \x03(\x0b\x32\x1f.lq.RecordChangeTile.ChangeTile\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x13\n\x0b\x63hange_type\x18\x05 \x01(\r\x12-\n\noperations\x18\x06 \x03(\x0b\x32\x19.lq.OptionalOperationList\x1a;\n\x07TingPai\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\"\n\ttingpais1\x18\x02 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x1a\x62\n\nChangeTile\x12\x10\n\x08in_tiles\x18\x01 \x03(\t\x12\x16\n\x0ein_tile_states\x18\x02 \x03(\x05\x12\x11\n\tout_tiles\x18\x03 \x03(\t\x12\x17\n\x0fout_tile_states\x18\x04 \x03(\x05\"\xe5\x01\n\x10\x41\x63tionRevealTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0f\n\x07is_liqi\x18\x02 \x01(\x08\x12\x10\n\x08is_wliqi\x18\x03 \x01(\x08\x12\r\n\x05moqie\x18\x04 \x01(\x08\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\x10\n\x08liqibang\x18\x06 \x01(\r\x12,\n\toperation\x18\x07 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12!\n\x08tingpais\x18\x08 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\x0c\n\x04tile\x18\t \x01(\t\x12\x10\n\x08zhenting\x18\n \x01(\x08\"\xe6\x01\n\x10RecordRevealTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0f\n\x07is_liqi\x18\x02 \x01(\x08\x12\x10\n\x08is_wliqi\x18\x03 \x01(\x08\x12\r\n\x05moqie\x18\x04 \x01(\x08\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\x10\n\x08liqibang\x18\x06 \x01(\r\x12-\n\noperations\x18\x07 \x03(\x0b\x32\x19.lq.OptionalOperationList\x12!\n\x08tingpais\x18\x08 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\x0c\n\x04tile\x18\t \x01(\t\x12\x10\n\x08zhenting\x18\n \x03(\x08\"p\n\x10\x41\x63tionUnveilTile\x12\x0c\n\x04seat\x18\x01 \x01(\x05\x12\x0e\n\x06scores\x18\x02 \x03(\x05\x12\x10\n\x08liqibang\x18\x03 \x01(\r\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\"p\n\x10RecordUnveilTile\x12\x0c\n\x04seat\x18\x01 \x01(\x05\x12\x0e\n\x06scores\x18\x02 \x03(\x05\x12\x10\n\x08liqibang\x18\x03 \x01(\r\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\"\xd4\x01\n\x0e\x41\x63tionLockTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0e\n\x06scores\x18\x02 \x03(\x05\x12\x10\n\x08liqibang\x18\x03 \x01(\r\x12\x0c\n\x04tile\x18\x04 \x01(\t\x12,\n\toperation\x18\x05 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x10\n\x08zhenting\x18\x06 \x01(\x08\x12!\n\x08tingpais\x18\x07 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\r\n\x05\x64oras\x18\x08 \x03(\t\x12\x12\n\nlock_state\x18\t \x01(\x05\"\xd5\x01\n\x0eRecordLockTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0e\n\x06scores\x18\x02 \x03(\x05\x12\x10\n\x08liqibang\x18\x03 \x01(\r\x12\x0c\n\x04tile\x18\x04 \x01(\t\x12,\n\toperation\x18\x05 \x03(\x0b\x32\x19.lq.OptionalOperationList\x12\x11\n\tzhentings\x18\x06 \x03(\x08\x12!\n\x08tingpais\x18\x07 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\r\n\x05\x64oras\x18\x08 \x03(\t\x12\x12\n\nlock_state\x18\t \x01(\x05\"\xb3\x03\n\x11\x41\x63tionDiscardTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\x12\x0f\n\x07is_liqi\x18\x03 \x01(\x08\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\r\n\x05moqie\x18\x05 \x01(\x08\x12\x10\n\x08zhenting\x18\x06 \x01(\x08\x12!\n\x08tingpais\x18\x07 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\r\n\x05\x64oras\x18\x08 \x03(\t\x12\x10\n\x08is_wliqi\x18\t \x01(\x08\x12\x12\n\ntile_state\x18\n \x01(\r\x12\x1a\n\x04muyu\x18\x0b \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x10\n\x08revealed\x18\x0c \x01(\x08\x12\x0e\n\x06scores\x18\r \x03(\x05\x12\x10\n\x08liqibang\x18\x0e \x01(\r\x12$\n\tyongchang\x18\x19 \x01(\x0b\x32\x11.lq.YongchangInfo\x12\x32\n\x12hun_zhi_yi_ji_info\x18\x1a \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\x12 \n\x18liqi_type_beishuizhizhan\x18\x1b \x01(\r\"\x80\x03\n\x11RecordDiscardTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\x12\x0f\n\x07is_liqi\x18\x03 \x01(\x08\x12\r\n\x05moqie\x18\x05 \x01(\x08\x12\x10\n\x08zhenting\x18\x06 \x03(\x08\x12!\n\x08tingpais\x18\x07 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\r\n\x05\x64oras\x18\x08 \x03(\t\x12\x10\n\x08is_wliqi\x18\t \x01(\x08\x12-\n\noperations\x18\n \x03(\x0b\x32\x19.lq.OptionalOperationList\x12\x12\n\ntile_state\x18\x0b \x01(\r\x12\x1a\n\x04muyu\x18\x0c \x01(\x0b\x32\x0c.lq.MuyuInfo\x12$\n\tyongchang\x18\r \x01(\x0b\x32\x11.lq.YongchangInfo\x12\x32\n\x12hun_zhi_yi_ji_info\x18\x0e \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\x12 \n\x18liqi_type_beishuizhizhan\x18\x1b \x01(\r\"\xd5\x02\n\x0e\x41\x63tionDealTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\x12\x17\n\x0fleft_tile_count\x18\x03 \x01(\r\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x1d\n\x04liqi\x18\x05 \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12\x10\n\x08zhenting\x18\x07 \x01(\x08\x12(\n\x08tingpais\x18\x08 \x03(\x0b\x32\x16.lq.TingPaiDiscardInfo\x12\x12\n\ntile_state\x18\t \x01(\r\x12\x1a\n\x04muyu\x18\n \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x12\n\ntile_index\x18\x0b \x01(\r\x12\x32\n\x12hun_zhi_yi_ji_info\x18\x0c \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\"\xab\x02\n\x0eRecordDealTile\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04tile\x18\x02 \x01(\t\x12\x17\n\x0fleft_tile_count\x18\x03 \x01(\r\x12\x1d\n\x04liqi\x18\x05 \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12\x10\n\x08zhenting\x18\x07 \x03(\x08\x12,\n\toperation\x18\x08 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x12\n\ntile_state\x18\t \x01(\r\x12\x1a\n\x04muyu\x18\x0b \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x12\n\ntile_index\x18\x0c \x01(\r\x12\x32\n\x12hun_zhi_yi_ji_info\x18\r \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\"\x97\x01\n\x17\x41\x63tionFillAwaitingTiles\x12\x16\n\x0e\x61waiting_tiles\x18\x01 \x03(\t\x12\x17\n\x0fleft_tile_count\x18\x02 \x01(\r\x12,\n\toperation\x18\x03 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x1d\n\x04liqi\x18\x04 \x01(\x0b\x32\x0f.lq.LiQiSuccess\"\x97\x01\n\x17RecordFillAwaitingTiles\x12\x16\n\x0e\x61waiting_tiles\x18\x01 \x03(\t\x12\x17\n\x0fleft_tile_count\x18\x02 \x01(\r\x12,\n\toperation\x18\x03 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x1d\n\x04liqi\x18\x04 \x01(\x0b\x32\x0f.lq.LiQiSuccess\"\x83\x03\n\x11\x41\x63tionChiPengGang\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\r\n\x05tiles\x18\x03 \x03(\t\x12\r\n\x05\x66roms\x18\x04 \x03(\r\x12\x1d\n\x04liqi\x18\x05 \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12,\n\toperation\x18\x06 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x10\n\x08zhenting\x18\x07 \x01(\x08\x12(\n\x08tingpais\x18\x08 \x03(\x0b\x32\x16.lq.TingPaiDiscardInfo\x12\x13\n\x0btile_states\x18\t \x03(\r\x12\x1a\n\x04muyu\x18\n \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x0e\n\x06scores\x18\x0b \x03(\x05\x12\x10\n\x08liqibang\x18\x0c \x01(\r\x12$\n\tyongchang\x18\r \x01(\x0b\x32\x11.lq.YongchangInfo\x12\x32\n\x12hun_zhi_yi_ji_info\x18\x0e \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\"\xd9\x02\n\x11RecordChiPengGang\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\r\n\x05tiles\x18\x03 \x03(\t\x12\r\n\x05\x66roms\x18\x04 \x03(\r\x12\x1d\n\x04liqi\x18\x05 \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\x10\n\x08zhenting\x18\x07 \x03(\x08\x12,\n\toperation\x18\x08 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\x13\n\x0btile_states\x18\t \x03(\r\x12\x1a\n\x04muyu\x18\n \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x0e\n\x06scores\x18\x0b \x03(\x05\x12\x10\n\x08liqibang\x18\x0c \x01(\r\x12$\n\tyongchang\x18\r \x01(\x0b\x32\x11.lq.YongchangInfo\x12\x32\n\x12hun_zhi_yi_ji_info\x18\x0e \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\"7\n\x10\x41\x63tionGangResult\x12#\n\ngang_infos\x18\x01 \x01(\x0b\x32\x0f.lq.ChuanmaGang\"7\n\x10RecordGangResult\x12#\n\ngang_infos\x18\x01 \x01(\x0b\x32\x0f.lq.ChuanmaGang\":\n\x13\x41\x63tionGangResultEnd\x12#\n\ngang_infos\x18\x01 \x01(\x0b\x32\x0f.lq.ChuanmaGang\":\n\x13RecordGangResultEnd\x12#\n\ngang_infos\x18\x01 \x01(\x0b\x32\x0f.lq.ChuanmaGang\"\xce\x01\n\x13\x41\x63tionAnGangAddGang\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\r\n\x05tiles\x18\x03 \x01(\t\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12\x10\n\x08zhenting\x18\x07 \x01(\x08\x12!\n\x08tingpais\x18\x08 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\x1a\n\x04muyu\x18\t \x01(\x0b\x32\x0c.lq.MuyuInfo\"\x9a\x01\n\x13RecordAnGangAddGang\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\r\n\x05tiles\x18\x03 \x01(\t\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12-\n\noperations\x18\x07 \x03(\x0b\x32\x19.lq.OptionalOperationList\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\"\xcc\x01\n\x0b\x41\x63tionBaBei\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12,\n\toperation\x18\x04 \x01(\x0b\x32\x19.lq.OptionalOperationList\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12\x10\n\x08zhenting\x18\x07 \x01(\x08\x12!\n\x08tingpais\x18\x08 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\r\n\x05moqie\x18\t \x01(\x08\x12\x12\n\ntile_state\x18\n \x01(\r\x12\x1a\n\x04muyu\x18\x0b \x01(\x0b\x32\x0c.lq.MuyuInfo\"\x98\x01\n\x0bRecordBaBei\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12-\n\noperations\x18\x07 \x03(\x0b\x32\x19.lq.OptionalOperationList\x12\r\n\x05moqie\x18\x08 \x01(\x08\x12\x12\n\ntile_state\x18\n \x01(\r\x12\x1a\n\x04muyu\x18\x0b \x01(\x0b\x32\x0c.lq.MuyuInfo\"\x86\x02\n\nActionHule\x12\x1b\n\x05hules\x18\x01 \x03(\x0b\x32\x0c.lq.HuleInfo\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x14\n\x0cwait_timeout\x18\x04 \x01(\r\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\x1c\n\x07gameend\x18\x06 \x01(\x0b\x32\x0b.lq.GameEnd\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x0e\n\x06\x62\x61opai\x18\t \x01(\x05\x12\x32\n\x12hun_zhi_yi_ji_info\x18\n \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\"\x86\x02\n\nRecordHule\x12\x1b\n\x05hules\x18\x01 \x03(\x0b\x32\x0c.lq.HuleInfo\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x14\n\x0cwait_timeout\x18\x04 \x01(\r\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\x1c\n\x07gameend\x18\x06 \x01(\x0b\x32\x0b.lq.GameEnd\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x0e\n\x06\x62\x61opai\x18\t \x01(\x05\x12\x32\n\x12hun_zhi_yi_ji_info\x18\n \x01(\x0b\x32\x16.lq.HunZhiYiJiBuffInfo\"\xc6\x01\n\x10HuInfoXueZhanMid\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x12\n\nhand_count\x18\x02 \x01(\r\x12\x0c\n\x04hand\x18\x03 \x03(\t\x12\x0c\n\x04ming\x18\x04 \x03(\t\x12\x0f\n\x07hu_tile\x18\x05 \x01(\t\x12\x0c\n\x04zimo\x18\x06 \x01(\x08\x12\r\n\x05yiman\x18\x07 \x01(\x08\x12\r\n\x05\x63ount\x18\x08 \x01(\r\x12\x19\n\x04\x66\x61ns\x18\t \x03(\x0b\x32\x0b.lq.FanInfo\x12\n\n\x02\x66u\x18\n \x01(\r\x12\x10\n\x08title_id\x18\x0b \x01(\r\"\xd1\x01\n\x14\x41\x63tionHuleXueZhanMid\x12#\n\x05hules\x18\x01 \x03(\x0b\x32\x14.lq.HuInfoXueZhanMid\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x1d\n\x04liqi\x18\t \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\x10\n\x08zhenting\x18\n \x01(\x08\"\xd1\x01\n\x14RecordHuleXueZhanMid\x12#\n\x05hules\x18\x01 \x03(\x0b\x32\x14.lq.HuInfoXueZhanMid\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x0e\n\x06scores\x18\x05 \x03(\x05\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12\x1d\n\x04liqi\x18\t \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\x10\n\x08zhenting\x18\n \x03(\x08\"\xf9\x01\n\x14\x41\x63tionHuleXueZhanEnd\x12#\n\x05hules\x18\x01 \x03(\x0b\x32\x14.lq.HuInfoXueZhanMid\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x0e\n\x06scores\x18\x04 \x03(\x05\x12\x14\n\x0cwait_timeout\x18\x05 \x01(\r\x12\x1c\n\x07gameend\x18\x06 \x01(\x0b\x32\x0b.lq.GameEnd\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12#\n\rhules_history\x18\t \x03(\x0b\x32\x0c.lq.HuleInfo\"\xf9\x01\n\x14RecordHuleXueZhanEnd\x12#\n\x05hules\x18\x01 \x03(\x0b\x32\x14.lq.HuInfoXueZhanMid\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x0e\n\x06scores\x18\x04 \x03(\x05\x12\x14\n\x0cwait_timeout\x18\x05 \x01(\r\x12\x1c\n\x07gameend\x18\x06 \x01(\x0b\x32\x0b.lq.GameEnd\x12\r\n\x05\x64oras\x18\x07 \x03(\t\x12\x1a\n\x04muyu\x18\x08 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12#\n\rhules_history\x18\t \x03(\x0b\x32\x0c.lq.HuleInfo\"\xce\x01\n\x0b\x41\x63tionLiuJu\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x1c\n\x07gameend\x18\x02 \x01(\x0b\x32\x0b.lq.GameEnd\x12\x0c\n\x04seat\x18\x03 \x01(\r\x12\r\n\x05tiles\x18\x04 \x03(\t\x12\x1d\n\x04liqi\x18\x05 \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\x16\n\x0e\x61llplayertiles\x18\x06 \x03(\t\x12\x1a\n\x04muyu\x18\x07 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12#\n\rhules_history\x18\t \x03(\x0b\x32\x0c.lq.HuleInfo\"\xce\x01\n\x0bRecordLiuJu\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x1c\n\x07gameend\x18\x02 \x01(\x0b\x32\x0b.lq.GameEnd\x12\x0c\n\x04seat\x18\x03 \x01(\r\x12\r\n\x05tiles\x18\x04 \x03(\t\x12\x1d\n\x04liqi\x18\x05 \x01(\x0b\x32\x0f.lq.LiQiSuccess\x12\x16\n\x0e\x61llplayertiles\x18\x06 \x03(\t\x12\x1a\n\x04muyu\x18\x07 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12#\n\rhules_history\x18\t \x03(\x0b\x32\x0c.lq.HuleInfo\"g\n\x10NoTilePlayerInfo\x12\x0f\n\x07tingpai\x18\x03 \x01(\x08\x12\x0c\n\x04hand\x18\x04 \x03(\t\x12\x1e\n\x05tings\x18\x05 \x03(\x0b\x32\x0f.lq.TingPaiInfo\x12\x14\n\x0c\x61lready_hule\x18\x06 \x01(\x08\"\xa1\x01\n\x0fNoTileScoreInfo\x12\x0c\n\x04seat\x18\x01 \x01(\r\x12\x12\n\nold_scores\x18\x02 \x03(\x05\x12\x14\n\x0c\x64\x65lta_scores\x18\x03 \x03(\x05\x12\x0c\n\x04hand\x18\x04 \x03(\t\x12\x0c\n\x04ming\x18\x05 \x03(\t\x12\r\n\x05\x64oras\x18\x06 \x03(\t\x12\r\n\x05score\x18\x07 \x01(\r\x12\r\n\x05taxes\x18\x08 \x03(\x05\x12\r\n\x05lines\x18\t \x03(\t\"\xc2\x01\n\x0c\x41\x63tionNoTile\x12\x14\n\x0cliujumanguan\x18\x01 \x01(\x08\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.lq.NoTilePlayerInfo\x12#\n\x06scores\x18\x03 \x03(\x0b\x32\x13.lq.NoTileScoreInfo\x12\x0f\n\x07gameend\x18\x04 \x01(\x08\x12\x1a\n\x04muyu\x18\x05 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12#\n\rhules_history\x18\t \x03(\x0b\x32\x0c.lq.HuleInfo\"\xc2\x01\n\x0cRecordNoTile\x12\x14\n\x0cliujumanguan\x18\x01 \x01(\x08\x12%\n\x07players\x18\x02 \x03(\x0b\x32\x14.lq.NoTilePlayerInfo\x12#\n\x06scores\x18\x03 \x03(\x0b\x32\x13.lq.NoTileScoreInfo\x12\x0f\n\x07gameend\x18\x04 \x01(\x08\x12\x1a\n\x04muyu\x18\x05 \x01(\x0b\x32\x0c.lq.MuyuInfo\x12#\n\rhules_history\x18\t \x03(\x0b\x32\x0c.lq.HuleInfo\"\x1d\n\rPlayerLeaving\x12\x0c\n\x04seat\x18\x01 \x01(\r\"I\n\x14ReqRequestConnection\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x10\n\x08route_id\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\x04\"S\n\x14ResRequestConnection\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x11\n\ttimestamp\x18\x02 \x01(\x04\x12\x0e\n\x06result\x18\x03 \x01(\r\"G\n\x15ReqRequestRouteChange\x12\x0e\n\x06\x62\x65\x66ore\x18\x01 \x01(\t\x12\x10\n\x08route_id\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\r\"A\n\x15ResRequestRouteChange\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error\x12\x0e\n\x06result\x18\x03 \x01(\r\"f\n\x0cReqHeartbeat\x12\r\n\x05\x64\x65lay\x18\x01 \x01(\r\x12\x1c\n\x14no_operation_counter\x18\x02 \x01(\r\x12\x10\n\x08platform\x18\x03 \x01(\r\x12\x17\n\x0fnetwork_quality\x18\x04 \x01(\r\"(\n\x0cResHeartbeat\x12\x18\n\x05\x65rror\x18\x01 \x01(\x0b\x32\t.lq.Error*=\n\x0fGamePlayerState\x12\x08\n\x04NULL\x10\x00\x12\x08\n\x04\x41UTH\x10\x01\x12\x0b\n\x07SYNCING\x10\x02\x12\t\n\x05READY\x10\x03\x32\xe7\xd5\x01\n\x05Lobby\x12;\n\x13\x66\x65tchConnectionInfo\x12\r.lq.ReqCommon\x1a\x15.lq.ResConnectionInfo\x12\x36\n\x0e\x66\x65tchQueueInfo\x12\r.lq.ReqCommon\x1a\x15.lq.ResFetchQueueInfo\x12+\n\x0b\x63\x61ncelQueue\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x35\n\x0bopenidCheck\x12\x12.lq.ReqOpenidCheck\x1a\x12.lq.ResOauth2Check\x12\x34\n\x06signup\x12\x14.lq.ReqSignupAccount\x1a\x14.lq.ResSignupAccount\x12#\n\x05login\x12\x0c.lq.ReqLogin\x1a\x0c.lq.ResLogin\x12\x32\n\x0cprepareLogin\x12\x13.lq.ReqPrepareLogin\x1a\r.lq.ResCommon\x12,\n\tfastLogin\x12\r.lq.ReqCommon\x1a\x10.lq.ResFastLogin\x12,\n\tfetchInfo\x12\r.lq.ReqCommon\x1a\x10.lq.ResFetchInfo\x12,\n\x0cloginSuccess\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12N\n\x1a\x66\x65tchServerMaintenanceInfo\x12\r.lq.ReqCommon\x1a!.lq.ResFetchServerMaintenanceInfo\x12-\n\nemailLogin\x12\x11.lq.ReqEmailLogin\x1a\x0c.lq.ResLogin\x12\x32\n\noauth2Auth\x12\x11.lq.ReqOauth2Auth\x1a\x11.lq.ResOauth2Auth\x12\x35\n\x0boauth2Check\x12\x12.lq.ReqOauth2Check\x1a\x12.lq.ResOauth2Check\x12\x38\n\x0coauth2Signup\x12\x13.lq.ReqOauth2Signup\x1a\x13.lq.ResOauth2Signup\x12/\n\x0boauth2Login\x12\x12.lq.ReqOauth2Login\x1a\x0c.lq.ResLogin\x12\x35\n\x0b\x64mmPreLogin\x12\x12.lq.ReqDMMPreLogin\x1a\x12.lq.ResDMMPreLogin\x12\x44\n\x15\x63reatePhoneVerifyCode\x12\x1c.lq.ReqCreatePhoneVerifyCode\x1a\r.lq.ResCommon\x12\x44\n\x15\x63reateEmailVerifyCode\x12\x1c.lq.ReqCreateEmailVerifyCode\x1a\r.lq.ResCommon\x12N\n\x14verfifyCodeForSecure\x12\x1a.lq.ReqVerifyCodeForSecure\x1a\x1a.lq.ResVerfiyCodeForSecure\x12\x38\n\x0f\x62indPhoneNumber\x12\x16.lq.ReqBindPhoneNumber\x1a\r.lq.ResCommon\x12<\n\x11unbindPhoneNumber\x12\x18.lq.ReqUnbindPhoneNumber\x1a\r.lq.ResCommon\x12@\n\x13\x66\x65tchPhoneLoginBind\x12\r.lq.ReqCommon\x1a\x1a.lq.ResFetchPhoneLoginBind\x12\x42\n\x14\x63reatePhoneLoginBind\x12\x1b.lq.ReqCreatePhoneLoginBind\x1a\r.lq.ResCommon\x12,\n\tbindEmail\x12\x10.lq.ReqBindEmail\x1a\r.lq.ResCommon\x12\x36\n\x0emodifyPassword\x12\x15.lq.ReqModifyPassword\x1a\r.lq.ResCommon\x12\x30\n\x0b\x62indAccount\x12\x12.lq.ReqBindAccount\x1a\r.lq.ResCommon\x12&\n\x06logout\x12\r.lq.ReqLogout\x1a\r.lq.ResLogout\x12*\n\x08heatbeat\x12\x0f.lq.ReqHeatBeat\x1a\r.lq.ResCommon\x12T\n\x12searchAccountByEid\x12\x1e.lq.ReqSearchAccountByEidLobby\x1a\x1e.lq.ResSearchAccountbyEidLobby\x12,\n\tloginBeat\x12\x10.lq.ReqLoginBeat\x1a\r.lq.ResCommon\x12\x36\n\x0e\x63reateNickname\x12\x15.lq.ReqCreateNickname\x1a\r.lq.ResCommon\x12\x36\n\x0emodifyNickname\x12\x15.lq.ReqModifyNickname\x1a\r.lq.ResCommon\x12\x36\n\x0emodifyBirthday\x12\x15.lq.ReqModifyBirthday\x1a\r.lq.ResCommon\x12+\n\tfetchRoom\x12\r.lq.ReqCommon\x1a\x0f.lq.ResSelfRoom\x12\x38\n\x0f\x66\x65tchGamingInfo\x12\r.lq.ReqCommon\x1a\x16.lq.ResFetchGamingInfo\x12\x32\n\ncreateRoom\x12\x11.lq.ReqCreateRoom\x1a\x11.lq.ResCreateRoom\x12,\n\x08joinRoom\x12\x0f.lq.ReqJoinRoom\x1a\x0f.lq.ResJoinRoom\x12)\n\tleaveRoom\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12,\n\treadyPlay\x12\x10.lq.ReqRoomReady\x1a\r.lq.ResCommon\x12\x34\n\x0e\x64ressingStatus\x12\x13.lq.ReqRoomDressing\x1a\r.lq.ResCommon\x12,\n\tstartRoom\x12\x10.lq.ReqRoomStart\x1a\r.lq.ResCommon\x12\x36\n\x0eroomKickPlayer\x12\x15.lq.ReqRoomKickPlayer\x1a\r.lq.ResCommon\x12.\n\nmodifyRoom\x12\x11.lq.ReqModifyRoom\x1a\r.lq.ResCommon\x12\x32\n\x0c\x61\x64\x64RoomRobot\x12\x13.lq.ReqAddRoomRobot\x1a\r.lq.ResCommon\x12\x31\n\tmatchGame\x12\x15.lq.ReqJoinMatchQueue\x1a\r.lq.ResCommon\x12\x35\n\x0b\x63\x61ncelMatch\x12\x17.lq.ReqCancelMatchQueue\x1a\r.lq.ResCommon\x12:\n\x10\x66\x65tchAccountInfo\x12\x12.lq.ReqAccountInfo\x1a\x12.lq.ResAccountInfo\x12\x32\n\x0c\x63hangeAvatar\x12\x13.lq.ReqChangeAvatar\x1a\r.lq.ResCommon\x12\x34\n\x14receiveVersionReward\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12U\n\x19\x66\x65tchAccountStatisticInfo\x12\x1b.lq.ReqAccountStatisticInfo\x1a\x1b.lq.ResAccountStatisticInfo\x12T\n\x1d\x66\x65tchAccountChallengeRankInfo\x12\x12.lq.ReqAccountInfo\x1a\x1f.lq.ResAccountChallengeRankInfo\x12G\n\x19\x66\x65tchAccountCharacterInfo\x12\r.lq.ReqCommon\x1a\x1b.lq.ResAccountCharacterInfo\x12\x38\n\x0cshopPurchase\x12\x13.lq.ReqShopPurchase\x1a\x13.lq.ResShopPurchase\x12\x37\n\x0f\x66\x65tchGameRecord\x12\x11.lq.ReqGameRecord\x1a\x11.lq.ResGameRecord\x12\x32\n\x0ereadGameRecord\x12\x11.lq.ReqGameRecord\x1a\r.lq.ResCommon\x12\x43\n\x13\x66\x65tchGameRecordList\x12\x15.lq.ReqGameRecordList\x1a\x15.lq.ResGameRecordList\x12I\n\x15\x66\x65tchGameRecordListV2\x12\x17.lq.ReqGameRecordListV2\x1a\x17.lq.ResGameRecordListV2\x12O\n\x17\x66\x65tchNextGameRecordList\x12\x19.lq.ReqNextGameRecordList\x1a\x19.lq.ResNextGameRecordList\x12M\n\x1c\x66\x65tchCollectedGameRecordList\x12\r.lq.ReqCommon\x1a\x1e.lq.ResCollectedGameRecordList\x12L\n\x16\x66\x65tchGameRecordsDetail\x12\x18.lq.ReqGameRecordsDetail\x1a\x18.lq.ResGameRecordsDetail\x12R\n\x18\x66\x65tchGameRecordsDetailV2\x12\x1a.lq.ReqGameRecordsDetailV2\x1a\x1a.lq.ResGameRecordsDetailV2\x12V\n\x16\x61\x64\x64\x43ollectedGameRecord\x12\x1d.lq.ReqAddCollectedGameRecord\x1a\x1d.lq.ResAddCollectedGameRecord\x12_\n\x19removeCollectedGameRecord\x12 .lq.ReqRemoveCollectedGameRecord\x1a .lq.ResRemoveCollectedGameRecord\x12t\n changeCollectedGameRecordRemarks\x12\'.lq.ReqChangeCollectedGameRecordRemarks\x1a\'.lq.ResChangeCollectedGameRecordRemarks\x12I\n\x15\x66\x65tchLevelLeaderboard\x12\x17.lq.ReqLevelLeaderboard\x1a\x17.lq.ResLevelLeaderboard\x12U\n\x19\x66\x65tchChallengeLeaderboard\x12\x1b.lq.ReqChallangeLeaderboard\x1a\x1b.lq.ResChallengeLeaderboard\x12O\n\x17\x66\x65tchMutiChallengeLevel\x12\x19.lq.ReqMutiChallengeLevel\x1a\x19.lq.ResMutiChallengeLevel\x12I\n\x16\x66\x65tchMultiAccountBrief\x12\x15.lq.ReqMultiAccountId\x1a\x18.lq.ResMultiAccountBrief\x12\x33\n\x0f\x66\x65tchFriendList\x12\r.lq.ReqCommon\x1a\x11.lq.ResFriendList\x12=\n\x14\x66\x65tchFriendApplyList\x12\r.lq.ReqCommon\x1a\x16.lq.ResFriendApplyList\x12\x30\n\x0b\x61pplyFriend\x12\x12.lq.ReqApplyFriend\x1a\r.lq.ResCommon\x12<\n\x11handleFriendApply\x12\x18.lq.ReqHandleFriendApply\x1a\r.lq.ResCommon\x12\x32\n\x0cremoveFriend\x12\x13.lq.ReqRemoveFriend\x1a\r.lq.ResCommon\x12G\n\x11searchAccountById\x12\x18.lq.ReqSearchAccountById\x1a\x18.lq.ResSearchAccountById\x12V\n\x16searchAccountByPattern\x12\x1d.lq.ReqSearchAccountByPattern\x1a\x1d.lq.ResSearchAccountByPattern\x12=\n\x11\x66\x65tchAccountState\x12\x12.lq.ReqAccountList\x1a\x14.lq.ResAccountStates\x12-\n\x0c\x66\x65tchBagInfo\x12\r.lq.ReqCommon\x1a\x0e.lq.ResBagInfo\x12.\n\nuseBagItem\x12\x11.lq.ReqUseBagItem\x1a\r.lq.ResCommon\x12\x36\n\x0eopenManualItem\x12\x15.lq.ReqOpenManualItem\x1a\r.lq.ResCommon\x12P\n\x14openRandomRewardItem\x12\x1b.lq.ReqOpenRandomRewardItem\x1a\x1b.lq.ResOpenRandomRewardItem\x12G\n\x11openAllRewardItem\x12\x18.lq.ReqOpenAllRewardItem\x1a\x18.lq.ResOpenAllRewardItem\x12\x32\n\x0c\x63omposeShard\x12\x13.lq.ReqComposeShard\x1a\r.lq.ResCommon\x12\x42\n\x11\x66\x65tchAnnouncement\x12\x18.lq.ReqFetchAnnouncement\x1a\x13.lq.ResAnnouncement\x12:\n\x10readAnnouncement\x12\x17.lq.ReqReadAnnouncement\x1a\r.lq.ResCommon\x12/\n\rfetchMailInfo\x12\r.lq.ReqCommon\x1a\x0f.lq.ResMailInfo\x12*\n\x08readMail\x12\x0f.lq.ReqReadMail\x1a\r.lq.ResCommon\x12.\n\ndeleteMail\x12\x11.lq.ReqDeleteMail\x1a\r.lq.ResCommon\x12>\n\x16takeAttachmentFromMail\x12\x15.lq.ReqTakeAttachment\x1a\r.lq.ResCommon\x12\\\n\x18receiveAchievementReward\x12\x1f.lq.ReqReceiveAchievementReward\x1a\x1f.lq.ResReceiveAchievementReward\x12k\n\x1dreceiveAchievementGroupReward\x12$.lq.ReqReceiveAchievementGroupReward\x1a$.lq.ResReceiveAchievementGroupReward\x12\x42\n\x14\x66\x65tchAchievementRate\x12\r.lq.ReqCommon\x1a\x1b.lq.ResFetchAchievementRate\x12\x35\n\x10\x66\x65tchAchievement\x12\r.lq.ReqCommon\x1a\x12.lq.ResAchievement\x12.\n\nbuyShiLian\x12\x11.lq.ReqBuyShiLian\x1a\r.lq.ResCommon\x12,\n\x0cmatchShiLian\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12-\n\rgoNextShiLian\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12<\n\x11updateClientValue\x12\x18.lq.ReqUpdateClientValue\x1a\r.lq.ResCommon\x12\x35\n\x10\x66\x65tchClientValue\x12\r.lq.ReqCommon\x1a\x12.lq.ResClientValue\x12\x34\n\rclientMessage\x12\x14.lq.ReqClientMessage\x1a\r.lq.ResCommon\x12I\n\x15\x66\x65tchCurrentMatchInfo\x12\x17.lq.ReqCurrentMatchInfo\x1a\x17.lq.ResCurrentMatchInfo\x12\x32\n\x0cuserComplain\x12\x13.lq.ReqUserComplain\x1a\r.lq.ResCommon\x12;\n\x13\x66\x65tchReviveCoinInfo\x12\r.lq.ReqCommon\x1a\x15.lq.ResReviveCoinInfo\x12.\n\x0egainReviveCoin\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x31\n\x0e\x66\x65tchDailyTask\x12\r.lq.ReqCommon\x1a\x10.lq.ResDailyTask\x12\x44\n\x10refreshDailyTask\x12\x17.lq.ReqRefreshDailyTask\x1a\x17.lq.ResRefreshDailyTask\x12\x35\n\x0buseGiftCode\x12\x12.lq.ReqUseGiftCode\x1a\x12.lq.ResUseGiftCode\x12\x43\n\x12useSpecialGiftCode\x12\x12.lq.ReqUseGiftCode\x1a\x19.lq.ResUseSpecialGiftCode\x12\x31\n\x0e\x66\x65tchTitleList\x12\r.lq.ReqCommon\x1a\x10.lq.ResTitleList\x12*\n\x08useTitle\x12\x0f.lq.ReqUseTitle\x1a\r.lq.ResCommon\x12<\n\x11sendClientMessage\x12\x18.lq.ReqSendClientMessage\x1a\r.lq.ResCommon\x12=\n\x11\x66\x65tchGameLiveInfo\x12\x13.lq.ReqGameLiveInfo\x1a\x13.lq.ResGameLiveInfo\x12R\n\x18\x66\x65tchGameLiveLeftSegment\x12\x1a.lq.ReqGameLiveLeftSegment\x1a\x1a.lq.ResGameLiveLeftSegment\x12=\n\x11\x66\x65tchGameLiveList\x12\x13.lq.ReqGameLiveList\x1a\x13.lq.ResGameLiveList\x12;\n\x13\x66\x65tchCommentSetting\x12\r.lq.ReqCommon\x1a\x15.lq.ResCommentSetting\x12\x42\n\x14updateCommentSetting\x12\x1b.lq.ReqUpdateCommentSetting\x1a\r.lq.ResCommon\x12\x44\n\x10\x66\x65tchCommentList\x12\x17.lq.ReqFetchCommentList\x1a\x17.lq.ResFetchCommentList\x12M\n\x13\x66\x65tchCommentContent\x12\x1a.lq.ReqFetchCommentContent\x1a\x1a.lq.ResFetchCommentContent\x12\x32\n\x0cleaveComment\x12\x13.lq.ReqLeaveComment\x1a\r.lq.ResCommon\x12\x34\n\rdeleteComment\x12\x14.lq.ReqDeleteComment\x1a\r.lq.ResCommon\x12<\n\x11updateReadComment\x12\x18.lq.ReqUpdateReadComment\x1a\r.lq.ResCommon\x12J\n\x12\x66\x65tchRollingNotice\x12\x19.lq.ReqFetchRollingNotice\x1a\x19.lq.ResFetchRollingNotice\x12@\n\x13\x66\x65tchMaintainNotice\x12\r.lq.ReqCommon\x1a\x1a.lq.ResFetchMaintainNotice\x12\x33\n\x0f\x66\x65tchServerTime\x12\r.lq.ReqCommon\x1a\x11.lq.ResServerTime\x12W\n\x15\x66\x65tchPlatformProducts\x12\x1e.lq.ReqPlatformBillingProducts\x1a\x1e.lq.ResPlatformBillingProducts\x12=\n\x14\x66\x65tchRandomCharacter\x12\r.lq.ReqCommon\x1a\x16.lq.ResRandomCharacter\x12;\n\x12setRandomCharacter\x12\x16.lq.ReqRandomCharacter\x1a\r.lq.ResCommon\x12\x44\n\x15\x63\x61ncelGooglePlayOrder\x12\x1c.lq.ReqCancelGooglePlayOrder\x1a\r.lq.ResCommon\x12/\n\topenChest\x12\x10.lq.ReqOpenChest\x1a\x10.lq.ResOpenChest\x12\x44\n\x10\x62uyFromChestShop\x12\x17.lq.ReqBuyFromChestShop\x1a\x17.lq.ResBuyFromChestShop\x12=\n\x14\x66\x65tchDailySignInInfo\x12\r.lq.ReqCommon\x1a\x16.lq.ResDailySignInInfo\x12-\n\rdoDailySignIn\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x44\n\x10\x64oActivitySignIn\x12\x17.lq.ReqDoActivitySignIn\x1a\x17.lq.ResDoActivitySignIn\x12\x39\n\x12\x66\x65tchCharacterInfo\x12\r.lq.ReqCommon\x1a\x14.lq.ResCharacterInfo\x12@\n\x13updateCharacterSort\x12\x1a.lq.ReqUpdateCharacterSort\x1a\r.lq.ResCommon\x12@\n\x13\x63hangeMainCharacter\x12\x1a.lq.ReqChangeMainCharacter\x1a\r.lq.ResCommon\x12@\n\x13\x63hangeCharacterSkin\x12\x1a.lq.ReqChangeCharacterSkin\x1a\r.lq.ResCommon\x12@\n\x13\x63hangeCharacterView\x12\x1a.lq.ReqChangeCharacterView\x1a\r.lq.ResCommon\x12J\n\x12setHiddenCharacter\x12\x19.lq.ReqSetHiddenCharacter\x1a\x19.lq.ResSetHiddenCharacter\x12M\n\x13sendGiftToCharacter\x12\x1a.lq.ReqSendGiftToCharacter\x1a\x1a.lq.ResSendGiftToCharacter\x12*\n\x08sellItem\x12\x0f.lq.ReqSellItem\x1a\r.lq.ResCommon\x12\x33\n\x0f\x66\x65tchCommonView\x12\r.lq.ReqCommon\x1a\x11.lq.ResCommonView\x12:\n\x10\x63hangeCommonView\x12\x17.lq.ReqChangeCommonView\x1a\r.lq.ResCommon\x12\x38\n\x0fsaveCommonViews\x12\x16.lq.ReqSaveCommonViews\x1a\r.lq.ResCommon\x12:\n\x10\x66\x65tchCommonViews\x12\x12.lq.ReqCommonViews\x1a\x12.lq.ResCommonViews\x12;\n\x13\x66\x65tchAllCommonViews\x12\r.lq.ReqCommon\x1a\x15.lq.ResAllcommonViews\x12\x34\n\ruseCommonView\x12\x14.lq.ReqUseCommonView\x1a\r.lq.ResCommon\x12\x44\n\x10upgradeCharacter\x12\x17.lq.ReqUpgradeCharacter\x1a\x17.lq.ResUpgradeCharacter\x12\x39\n\x11\x61\x64\x64\x46inishedEnding\x12\x15.lq.ReqFinishedEnding\x1a\r.lq.ResCommon\x12;\n\x13receiveEndingReward\x12\x15.lq.ReqFinishedEnding\x1a\r.lq.ResCommon\x12\x34\n\x11gameMasterCommand\x12\x10.lq.ReqGMCommand\x1a\r.lq.ResCommon\x12/\n\rfetchShopInfo\x12\r.lq.ReqCommon\x1a\x0f.lq.ResShopInfo\x12\x35\n\x0b\x62uyFromShop\x12\x12.lq.ReqBuyFromShop\x1a\x12.lq.ResBuyFromShop\x12.\n\nbuyFromZHP\x12\x11.lq.ReqBuyFromZHP\x1a\r.lq.ResCommon\x12;\n\x0erefreshZHPShop\x12\x12.lq.ReqReshZHPShop\x1a\x15.lq.ResRefreshZHPShop\x12=\n\x14\x66\x65tchMonthTicketInfo\x12\r.lq.ReqCommon\x1a\x16.lq.ResMonthTicketInfo\x12\x36\n\x0epayMonthTicket\x12\r.lq.ReqCommon\x1a\x15.lq.ResPayMonthTicket\x12:\n\x10\x65xchangeCurrency\x12\x17.lq.ReqExchangeCurrency\x1a\r.lq.ResCommon\x12<\n\x12\x65xchangeChestStone\x12\x17.lq.ReqExchangeCurrency\x1a\r.lq.ResCommon\x12\x39\n\x0f\x65xchangeDiamond\x12\x17.lq.ReqExchangeCurrency\x1a\r.lq.ResCommon\x12;\n\x13\x66\x65tchServerSettings\x12\r.lq.ReqCommon\x1a\x15.lq.ResServerSettings\x12=\n\x14\x66\x65tchAccountSettings\x12\r.lq.ReqCommon\x1a\x16.lq.ResAccountSettings\x12\x44\n\x15updateAccountSettings\x12\x1c.lq.ReqUpdateAccountSettings\x1a\r.lq.ResCommon\x12=\n\x14\x66\x65tchModNicknameTime\x12\r.lq.ReqCommon\x1a\x16.lq.ResModNicknameTime\x12Y\n\x17\x63reateWechatNativeOrder\x12\x1e.lq.ReqCreateWechatNativeOrder\x1a\x1e.lq.ResCreateWechatNativeOrder\x12P\n\x14\x63reateWechatAppOrder\x12\x1b.lq.ReqCreateWechatAppOrder\x1a\x1b.lq.ResCreateWechatAppOrder\x12G\n\x11\x63reateAlipayOrder\x12\x18.lq.ReqCreateAlipayOrder\x1a\x18.lq.ResCreateAlipayOrder\x12S\n\x15\x63reateAlipayScanOrder\x12\x1c.lq.ReqCreateAlipayScanOrder\x1a\x1c.lq.ResCreateAlipayScanOrder\x12P\n\x14\x63reateAlipayAppOrder\x12\x1b.lq.ReqCreateAlipayAppOrder\x1a\x1b.lq.ResCreateAlipayAppOrder\x12Y\n\x17\x63reateJPCreditCardOrder\x12\x1e.lq.ReqCreateJPCreditCardOrder\x1a\x1e.lq.ResCreateJPCreditCardOrder\x12M\n\x13\x63reateJPPaypalOrder\x12\x1a.lq.ReqCreateJPPaypalOrder\x1a\x1a.lq.ResCreateJPPaypalOrder\x12\x41\n\x0f\x63reateJPAuOrder\x12\x16.lq.ReqCreateJPAuOrder\x1a\x16.lq.ResCreateJPAuOrder\x12M\n\x13\x63reateJPDocomoOrder\x12\x1a.lq.ReqCreateJPDocomoOrder\x1a\x1a.lq.ResCreateJPDocomoOrder\x12S\n\x15\x63reateJPWebMoneyOrder\x12\x1c.lq.ReqCreateJPWebMoneyOrder\x1a\x1c.lq.ResCreateJPWebMoneyOrder\x12S\n\x15\x63reateJPSoftbankOrder\x12\x1c.lq.ReqCreateJPSoftbankOrder\x1a\x1c.lq.ResCreateJPSoftbankOrder\x12M\n\x13\x63reateJPPayPayOrder\x12\x1a.lq.ReqCreateJPPayPayOrder\x1a\x1a.lq.ResCreateJPPayPayOrder\x12h\n\x1c\x66\x65tchJPCommonCreditCardOrder\x12#.lq.ReqFetchJPCommonCreditCardOrder\x1a#.lq.ResFetchJPCommonCreditCardOrder\x12\x44\n\x10\x63reateJPGMOOrder\x12\x17.lq.ReqCreateJPGMOOrder\x1a\x17.lq.ResCreateJPGMOOrder\x12M\n\x13\x63reateENPaypalOrder\x12\x1a.lq.ReqCreateENPaypalOrder\x1a\x1a.lq.ResCreateENPaypalOrder\x12Y\n\x17\x63reateENMasterCardOrder\x12\x1e.lq.ReqCreateENMasterCardOrder\x1a\x1e.lq.ResCreateENMasterCardOrder\x12G\n\x11\x63reateENVisaOrder\x12\x18.lq.ReqCreateENVisaOrder\x1a\x18.lq.ResCreateENVisaOrder\x12\x44\n\x10\x63reateENJCBOrder\x12\x17.lq.ReqCreateENJCBOrder\x1a\x17.lq.ResCreateENJCBOrder\x12M\n\x13\x63reateENAlipayOrder\x12\x1a.lq.ReqCreateENAlipayOrder\x1a\x1a.lq.ResCreateENAlipayOrder\x12M\n\x13\x63reateKRPaypalOrder\x12\x1a.lq.ReqCreateKRPaypalOrder\x1a\x1a.lq.ResCreateKRPaypalOrder\x12Y\n\x17\x63reateKRMasterCardOrder\x12\x1e.lq.ReqCreateKRMasterCardOrder\x1a\x1e.lq.ResCreateKRMasterCardOrder\x12G\n\x11\x63reateKRVisaOrder\x12\x18.lq.ReqCreateKRVisaOrder\x1a\x18.lq.ResCreateKRVisaOrder\x12\x44\n\x10\x63reateKRJCBOrder\x12\x17.lq.ReqCreateKRJCBOrder\x1a\x17.lq.ResCreateKRJCBOrder\x12M\n\x13\x63reateKRAlipayOrder\x12\x1a.lq.ReqCreateKRAlipayOrder\x1a\x1a.lq.ResCreateKRAlipayOrder\x12>\n\x0e\x63reateDMMOrder\x12\x15.lq.ReqCreateDMMOrder\x1a\x15.lq.ResCreateDmmOrder\x12>\n\x0e\x63reateIAPOrder\x12\x15.lq.ReqCreateIAPOrder\x1a\x15.lq.ResCreateIAPOrder\x12\x44\n\x10\x63reateSteamOrder\x12\x17.lq.ReqCreateSteamOrder\x1a\x17.lq.ResCreateSteamOrder\x12:\n\x10verifySteamOrder\x12\x17.lq.ReqVerifySteamOrder\x1a\r.lq.ResCommon\x12N\n\x18\x63reateMyCardAndroidOrder\x12\x18.lq.ReqCreateMyCardOrder\x1a\x18.lq.ResCreateMyCardOrder\x12J\n\x14\x63reateMyCardWebOrder\x12\x18.lq.ReqCreateMyCardOrder\x1a\x18.lq.ResCreateMyCardOrder\x12G\n\x11\x63reatePaypalOrder\x12\x18.lq.ReqCreatePaypalOrder\x1a\x18.lq.ResCreatePaypalOrder\x12G\n\x11\x63reateXsollaOrder\x12\x18.lq.ReqCreateXsollaOrder\x1a\x18.lq.ResCreateXsollaOrder\x12I\n\x13\x63reateXsollaV4Order\x12\x18.lq.ReqCreateXsollaOrder\x1a\x18.lq.ResCreateXsollaOrder\x12<\n\x11verifyMyCardOrder\x12\x18.lq.ReqVerifyMyCardOrder\x1a\r.lq.ResCommon\x12P\n\x14verificationIAPOrder\x12\x1b.lq.ReqVerificationIAPOrder\x1a\x1b.lq.ResVerificationIAPOrder\x12J\n\x14\x63reateYostarSDKOrder\x12\x18.lq.ReqCreateYostarOrder\x1a\x18.lq.ResCreateYostarOrder\x12J\n\x12\x63reateBillingOrder\x12\x19.lq.ReqCreateBillingOrder\x1a\x19.lq.ResCreateBillingOrder\x12\x42\n\x14solveGooglePlayOrder\x12\x1b.lq.ReqSolveGooglePlayOrder\x1a\r.lq.ResCommon\x12\x45\n\x15solveGooglePayOrderV3\x12\x1d.lq.ReqSolveGooglePlayOrderV3\x1a\r.lq.ResCommon\x12:\n\x10\x64\x65liverAA32Order\x12\x17.lq.ReqDeliverAA32Order\x1a\r.lq.ResCommon\x12\'\n\tfetchMisc\x12\r.lq.ReqCommon\x1a\x0b.lq.ResMisc\x12\x38\n\x0fmodifySignature\x12\x16.lq.ReqModifySignature\x1a\r.lq.ResCommon\x12\x33\n\x0f\x66\x65tchIDCardInfo\x12\r.lq.ReqCommon\x1a\x11.lq.ResIDCardInfo\x12:\n\x10updateIDCardInfo\x12\x17.lq.ReqUpdateIDCardInfo\x1a\r.lq.ResCommon\x12\x31\n\x0e\x66\x65tchVipReward\x12\r.lq.ReqCommon\x1a\x10.lq.ResVipReward\x12\x34\n\rgainVipReward\x12\x14.lq.ReqGainVipReward\x1a\r.lq.ResCommon\x12:\n\x10\x66\x65tchRefundOrder\x12\r.lq.ReqCommon\x1a\x17.lq.ResFetchRefundOrder\x12\x62\n\x1a\x66\x65tchCustomizedContestList\x12!.lq.ReqFetchCustomizedContestList\x1a!.lq.ResFetchCustomizedContestList\x12n\n\x1e\x66\x65tchCustomizedContestAuthInfo\x12%.lq.ReqFetchCustomizedContestAuthInfo\x1a%.lq.ResFetchCustomizedContestAuthInfo\x12V\n\x16\x65nterCustomizedContest\x12\x1d.lq.ReqEnterCustomizedContest\x1a\x1d.lq.ResEnterCustomizedContest\x12\x36\n\x16leaveCustomizedContest\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12t\n fetchCustomizedContestOnlineInfo\x12\'.lq.ReqFetchCustomizedContestOnlineInfo\x1a\'.lq.ResFetchCustomizedContestOnlineInfo\x12w\n!fetchCustomizedContestByContestId\x12(.lq.ReqFetchCustomizedContestByContestId\x1a(.lq.ResFetchCustomizedContestByContestId\x12Y\n\x17signupCustomizedContest\x12\x1e.lq.ReqSignupCustomizedContest\x1a\x1e.lq.ResSignupCustomizedContest\x12\x46\n\x16startCustomizedContest\x12\x1d.lq.ReqStartCustomizedContest\x1a\r.lq.ResCommon\x12\x44\n\x15stopCustomizedContest\x12\x1c.lq.ReqStopCustomizedContest\x1a\r.lq.ResCommon\x12k\n\x1djoinCustomizedContestChatRoom\x12$.lq.ReqJoinCustomizedContestChatRoom\x1a$.lq.ResJoinCustomizedContestChatRoom\x12>\n\x1eleaveCustomizedContestChatRoom\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x36\n\x0esayChatMessage\x12\x15.lq.ReqSayChatMessage\x1a\r.lq.ResCommon\x12w\n!fetchCustomizedContestGameRecords\x12(.lq.ReqFetchCustomizedContestGameRecords\x1a(.lq.ResFetchCustomizedContestGameRecords\x12z\n\"fetchCustomizedContestGameLiveList\x12).lq.ReqFetchCustomizedContestGameLiveList\x1a).lq.ResFetchCustomizedContestGameLiveList\x12H\n\x17\x66ollowCustomizedContest\x12\x1e.lq.ReqTargetCustomizedContest\x1a\r.lq.ResCommon\x12J\n\x19unfollowCustomizedContest\x12\x1e.lq.ReqTargetCustomizedContest\x1a\r.lq.ResCommon\x12\x37\n\x11\x66\x65tchActivityList\x12\r.lq.ReqCommon\x1a\x13.lq.ResActivityList\x12\x45\n\x18\x66\x65tchAccountActivityData\x12\r.lq.ReqCommon\x1a\x1a.lq.ResAccountActivityData\x12P\n\x14\x65xchangeActivityItem\x12\x1b.lq.ReqExchangeActivityItem\x1a\x1b.lq.ResExchangeActivityItem\x12\x42\n\x14\x63ompleteActivityTask\x12\x1b.lq.ReqCompleteActivityTask\x1a\r.lq.ResCommon\x12L\n\x19\x63ompleteActivityTaskBatch\x12 .lq.ReqCompleteActivityTaskBatch\x1a\r.lq.ResCommon\x12\x46\n\x18\x63ompleteActivityFlipTask\x12\x1b.lq.ReqCompleteActivityTask\x1a\r.lq.ResCommon\x12H\n\x1a\x63ompletePeriodActivityTask\x12\x1b.lq.ReqCompleteActivityTask\x1a\r.lq.ResCommon\x12X\n\x1f\x63ompletePeriodActivityTaskBatch\x12&.lq.ReqCompletePeriodActivityTaskBatch\x1a\r.lq.ResCommon\x12H\n\x1a\x63ompleteRandomActivityTask\x12\x1b.lq.ReqCompleteActivityTask\x1a\r.lq.ResCommon\x12R\n\x1f\x63ompleteRandomActivityTaskBatch\x12 .lq.ReqCompleteActivityTaskBatch\x1a\r.lq.ResCommon\x12Y\n\x17receiveActivityFlipTask\x12\x1e.lq.ReqReceiveActivityFlipTask\x1a\x1e.lq.ResReceiveActivityFlipTask\x12_\n\x19\x63ompleteSegmentTaskReward\x12 .lq.ReqCompleteSegmentTaskReward\x1a .lq.ResCompleteSegmentTaskReward\x12S\n\x15\x66\x65tchActivityFlipInfo\x12\x1c.lq.ReqFetchActivityFlipInfo\x1a\x1c.lq.ResFetchActivityFlipInfo\x12^\n\"gainAccumulatedPointActivityReward\x12).lq.ReqGainAccumulatedPointActivityReward\x1a\r.lq.ResCommon\x12R\n\x1cgainMultiPointActivityReward\x12#.lq.ReqGainMultiPointActivityReward\x1a\r.lq.ResCommon\x12_\n\x19\x66\x65tchRankPointLeaderboard\x12 .lq.ReqFetchRankPointLeaderboard\x1a .lq.ResFetchRankPointLeaderboard\x12@\n\x13gainRankPointReward\x12\x1a.lq.ReqGainRankPointReward\x1a\r.lq.ResCommon\x12I\n\x17richmanActivityNextMove\x12\x16.lq.ReqRichmanNextMove\x1a\x16.lq.ResRichmanNextMove\x12P\n\x1brichmanAcitivitySpecialMove\x12\x19.lq.ReqRichmanSpecialMove\x1a\x16.lq.ResRichmanNextMove\x12L\n\x18richmanActivityChestInfo\x12\x17.lq.ReqRichmanChestInfo\x1a\x17.lq.ResRichmanChestInfo\x12S\n\x15\x63reateGameObserveAuth\x12\x1c.lq.ReqCreateGameObserveAuth\x1a\x1c.lq.ResCreateGameObserveAuth\x12V\n\x16refreshGameObserveAuth\x12\x1d.lq.ReqRefreshGameObserveAuth\x1a\x1d.lq.ResRefreshGameObserveAuth\x12\x37\n\x11\x66\x65tchActivityBuff\x12\r.lq.ReqCommon\x1a\x13.lq.ResActivityBuff\x12\x46\n\x13upgradeActivityBuff\x12\x1a.lq.ReqUpgradeActivityBuff\x1a\x13.lq.ResActivityBuff\x12P\n\x14upgradeActivityLevel\x12\x1b.lq.ReqUpgradeActivityLevel\x1a\x1b.lq.ResUpgradeActivityLevel\x12h\n\x1creceiveUpgradeActivityReward\x12#.lq.ReqReceiveUpgradeActivityReward\x1a#.lq.ResReceiveUpgradeActivityReward\x12:\n\x10upgradeChallenge\x12\r.lq.ReqCommon\x1a\x17.lq.ResUpgradeChallenge\x12:\n\x10refreshChallenge\x12\r.lq.ReqCommon\x1a\x17.lq.ResRefreshChallenge\x12>\n\x12\x66\x65tchChallengeInfo\x12\r.lq.ReqCommon\x1a\x19.lq.ResFetchChallengeInfo\x12N\n\x1a\x66orceCompleteChallengeTask\x12!.lq.ReqForceCompleteChallengeTask\x1a\r.lq.ResCommon\x12\x41\n\x14\x66\x65tchChallengeSeason\x12\r.lq.ReqCommon\x1a\x1a.lq.ResChallengeSeasonInfo\x12\x62\n\x1areceiveChallengeRankReward\x12!.lq.ReqReceiveChallengeRankReward\x1a!.lq.ResReceiveChallengeRankReward\x12\x36\n\x10\x66\x65tchABMatchInfo\x12\r.lq.ReqCommon\x1a\x13.lq.ResFetchABMatch\x12\x32\n\x0c\x62uyInABMatch\x12\x13.lq.ReqBuyInABMatch\x1a\r.lq.ResCommon\x12\x34\n\x14receiveABMatchReward\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12+\n\x0bquitABMatch\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12<\n\x11startUnifiedMatch\x12\x18.lq.ReqStartUnifiedMatch\x1a\r.lq.ResCommon\x12>\n\x12\x63\x61ncelUnifiedMatch\x12\x19.lq.ReqCancelUnifiedMatch\x1a\r.lq.ResCommon\x12@\n\x12\x66\x65tchGamePointRank\x12\x14.lq.ReqGamePointRank\x1a\x14.lq.ResGamePointRank\x12M\n\x16\x66\x65tchSelfGamePointRank\x12\x14.lq.ReqGamePointRank\x1a\x1d.lq.ResFetchSelfGamePointRank\x12)\n\x07readSNS\x12\x0e.lq.ReqReadSNS\x1a\x0e.lq.ResReadSNS\x12,\n\x08replySNS\x12\x0f.lq.ReqReplySNS\x1a\x0f.lq.ResReplySNS\x12)\n\x07likeSNS\x12\x0e.lq.ReqLikeSNS\x1a\x0e.lq.ResLikeSNS\x12)\n\x07\x64igMine\x12\x0e.lq.ReqDigMine\x1a\x0e.lq.ResDigMine\x12\x44\n\x10\x66\x65tchLastPrivacy\x12\x17.lq.ReqFetchLastPrivacy\x1a\x17.lq.ResFetchLastPrivacy\x12\x32\n\x0c\x63heckPrivacy\x12\x13.lq.ReqCheckPrivacy\x1a\r.lq.ResCommon\x12S\n\x15\x66\x65tchRPGBattleHistory\x12\x1c.lq.ReqFetchRPGBattleHistory\x1a\x1c.lq.ResFetchRPGBattleHistory\x12W\n\x17\x66\x65tchRPGBattleHistoryV2\x12\x1c.lq.ReqFetchRPGBattleHistory\x1a\x1e.lq.ResFetchRPGBattleHistoryV2\x12G\n\x11receiveRPGRewards\x12\x18.lq.ReqReceiveRPGRewards\x1a\x18.lq.ResReceiveRPGRewards\x12\x45\n\x10receiveRPGReward\x12\x17.lq.ReqReceiveRPGReward\x1a\x18.lq.ResReceiveRPGRewards\x12\x36\n\x0e\x62uyArenaTicket\x12\x15.lq.ReqBuyArenaTicket\x1a\r.lq.ResCommon\x12.\n\nenterArena\x12\x11.lq.ReqEnterArena\x1a\r.lq.ResCommon\x12<\n\x12receiveArenaReward\x12\x12.lq.ReqArenaReward\x1a\x12.lq.ResArenaReward\x12\x38\n\x0c\x66\x65tchOBToken\x12\x13.lq.ReqFetchOBToken\x1a\x13.lq.ResFetchOBToken\x12Y\n\x17receiveCharacterRewards\x12\x1e.lq.ReqReceiveCharacterRewards\x1a\x1e.lq.ResReceiveCharacterRewards\x12\x44\n\x10\x66\x65\x65\x64\x41\x63tivityFeed\x12\x17.lq.ReqFeedActivityFeed\x1a\x17.lq.ResFeedActivityFeed\x12\\\n\x18sendActivityGiftToFriend\x12\x1f.lq.ReqSendActivityGiftToFriend\x1a\x1f.lq.ResSendActivityGiftToFriend\x12@\n\x13receiveActivityGift\x12\x1a.lq.ReqReceiveActivityGift\x1a\r.lq.ResCommon\x12V\n\x16receiveAllActivityGift\x12\x1d.lq.ReqReceiveAllActivityGift\x1a\x1d.lq.ResReceiveAllActivityGift\x12\x65\n\x1b\x66\x65tchFriendGiftActivityData\x12\".lq.ReqFetchFriendGiftActivityData\x1a\".lq.ResFetchFriendGiftActivityData\x12\x44\n\x10openPreChestItem\x12\x17.lq.ReqOpenPreChestItem\x1a\x17.lq.ResOpenPreChestItem\x12G\n\x11\x66\x65tchVoteActivity\x12\x18.lq.ReqFetchVoteActivity\x1a\x18.lq.ResFetchVoteActivity\x12\x38\n\x0cvoteActivity\x12\x13.lq.ReqVoteActivity\x1a\x13.lq.ResVoteActivity\x12>\n\x12unlockActivitySpot\x12\x19.lq.ReqUnlockActivitySpot\x1a\r.lq.ResCommon\x12J\n\x18unlockActivitySpotEnding\x12\x1f.lq.ReqUnlockActivitySpotEnding\x1a\r.lq.ResCommon\x12_\n\x19receiveActivitySpotReward\x12 .lq.ReqReceiveActivitySpotReward\x1a .lq.ResReceiveActivitySpotReward\x12\x34\n\rdeleteAccount\x12\r.lq.ReqCommon\x1a\x14.lq.ResDeleteAccount\x12\x33\n\x13\x63\x61ncelDeleteAccount\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12,\n\tlogReport\x12\x10.lq.ReqLogReport\x1a\r.lq.ResCommon\x12.\n\nbindOauth2\x12\x11.lq.ReqBindOauth2\x1a\r.lq.ResCommon\x12\x39\n\x0f\x66\x65tchOauth2Info\x12\x12.lq.ReqFetchOauth2\x1a\x12.lq.ResFetchOauth2\x12\x38\n\x0fsetLoadingImage\x12\x16.lq.ReqSetLoadingImage\x1a\r.lq.ResCommon\x12<\n\x11\x66\x65tchShopInterval\x12\r.lq.ReqCommon\x1a\x18.lq.ResFetchShopInterval\x12\x44\n\x15\x66\x65tchActivityInterval\x12\r.lq.ReqCommon\x1a\x1c.lq.ResFetchActivityInterval\x12<\n\x11\x66\x65tchRecentFriend\x12\r.lq.ReqCommon\x1a\x18.lq.ResFetchrecentFriend\x12/\n\topenGacha\x12\x10.lq.ReqOpenGacha\x1a\x10.lq.ResOpenGacha\x12\x30\n\x0btaskRequest\x12\x12.lq.ReqTaskRequest\x1a\r.lq.ResCommon\x12Y\n\x17simulationActivityTrain\x12\x1e.lq.ReqSimulationActivityTrain\x1a\x1e.lq.ResSimulationActivityTrain\x12_\n\x19\x66\x65tchSimulationGameRecord\x12 .lq.ReqFetchSimulationGameRecord\x1a .lq.ResFetchSimulationGameRecord\x12\x65\n\x1bstartSimulationActivityGame\x12\".lq.ReqStartSimulationActivityGame\x1a\".lq.ResStartSimulationActivityGame\x12Y\n\x17\x66\x65tchSimulationGameRank\x12\x1e.lq.ReqFetchSimulationGameRank\x1a\x1e.lq.ResFetchSimulationGameRank\x12V\n\x16generateCombiningCraft\x12\x1d.lq.ReqGenerateCombiningCraft\x1a\x1d.lq.ResGenerateCombiningCraft\x12J\n\x12moveCombiningCraft\x12\x19.lq.ReqMoveCombiningCraft\x1a\x19.lq.ResMoveCombiningCraft\x12S\n\x15\x63ombiningRecycleCraft\x12\x1c.lq.ReqCombiningRecycleCraft\x1a\x1c.lq.ResCombiningRecycleCraft\x12Y\n\x17recoverCombiningRecycle\x12\x1e.lq.ReqRecoverCombiningRecycle\x1a\x1e.lq.ResRecoverCombiningRecycle\x12P\n\x14\x66inishCombiningOrder\x12\x1b.lq.ReqFinishCombiningOrder\x1a\x1b.lq.ResFinishCombiningOrder\x12\x46\n\x16upgradeVillageBuilding\x12\x1d.lq.ReqUpgradeVillageBuilding\x1a\r.lq.ResCommon\x12h\n\x1creceiveVillageBuildingReward\x12#.lq.ReqReceiveVillageBuildingReward\x1a#.lq.ResReceiveVillageBuildingReward\x12:\n\x10startVillageTrip\x12\x17.lq.ReqStartVillageTrip\x1a\r.lq.ResCommon\x12\\\n\x18receiveVillageTripReward\x12\x1f.lq.ReqReceiveVillageTripReward\x1a\x1f.lq.ResReceiveVillageTripReward\x12M\n\x13\x63ompleteVillageTask\x12\x1a.lq.ReqCompleteVillageTask\x1a\x1a.lq.ResCompleteVillageTask\x12P\n\x14getFriendVillageData\x12\x1b.lq.ReqGetFriendVillageData\x1a\x1b.lq.ResGetFriendVillageData\x12\x44\n\x10setVillageWorker\x12\x17.lq.ReqSetVillageWorker\x1a\x17.lq.ResSetVillageWorker\x12\x44\n\x10nextRoundVillage\x12\x17.lq.ReqNextRoundVillage\x1a\x17.lq.ResNextRoundVillage\x12q\n\x1fresolveFestivalActivityProposal\x12&.lq.ReqResolveFestivalActivityProposal\x1a&.lq.ResResolveFestivalActivityProposal\x12h\n\x1cresolveFestivalActivityEvent\x12#.lq.ReqResolveFestivalActivityEvent\x1a#.lq.ResResolveFestivalActivityEvent\x12M\n\x13\x62uyFestivalProposal\x12\x1a.lq.ReqBuyFestivalProposal\x1a\x1a.lq.ResBuyFestivalProposal\x12>\n\x12islandActivityMove\x12\x19.lq.ReqIslandActivityMove\x1a\r.lq.ResCommon\x12<\n\x11islandActivityBuy\x12\x18.lq.ReqIslandActivityBuy\x1a\r.lq.ResCommon\x12>\n\x12islandActivitySell\x12\x19.lq.ReqIslandActivitySell\x1a\r.lq.ResCommon\x12\x44\n\x15islandActivityTidyBag\x12\x1c.lq.ReqIslandActivityTidyBag\x1a\r.lq.ResCommon\x12P\n\x1bislandActivityUnlockBagGrid\x12\".lq.ReqIslandActivityUnlockBagGrid\x1a\r.lq.ResCommon\x12Y\n\x17\x63reateCustomizedContest\x12\x1e.lq.ReqCreateCustomizedContest\x1a\x1e.lq.ResCreateCustomizedContest\x12w\n!fetchManagerCustomizedContestList\x12(.lq.ReqFetchmanagerCustomizedContestList\x1a(.lq.ResFetchManagerCustomizedContestList\x12k\n\x1d\x66\x65tchManagerCustomizedContest\x12$.lq.ReqFetchManagerCustomizedContest\x1a$.lq.ResFetchManagerCustomizedContest\x12V\n\x1eupdateManagerCustomizedContest\x12%.lq.ReqUpdateManagerCustomizedContest\x1a\r.lq.ResCommon\x12V\n\x16\x66\x65tchContestPlayerRank\x12\x1d.lq.ReqFetchContestPlayerRank\x1a\x1d.lq.ResFetchContestPlayerRank\x12P\n\x14\x66\x65tchReadyPlayerList\x12\x1b.lq.ReqFetchReadyPlayerList\x1a\x1b.lq.ResFetchReadyPlayerList\x12\x36\n\x0e\x63reateGamePlan\x12\x15.lq.ReqCreateGamePlan\x1a\r.lq.ResCommon\x12X\n\x1fgenerateContestManagerLoginCode\x12\r.lq.ReqCommon\x1a&.lq.ResGenerateContestManagerLoginCode\x12Y\n\x17\x61muletActivityFetchInfo\x12\x1e.lq.ReqAmuletActivityFetchInfo\x1a\x1e.lq.ResAmuletActivityFetchInfo\x12\\\n\x18\x61muletActivityFetchBrief\x12\x1f.lq.ReqAmuletActivityFetchBrief\x1a\x1f.lq.ResAmuletActivityFetchBrief\x12Y\n\x17\x61muletActivityStartGame\x12\x1e.lq.ReqAmuletActivityStartGame\x1a\x1e.lq.ResAmuletActivityStartGame\x12S\n\x15\x61muletActivityOperate\x12\x1c.lq.ReqAmuletActivityOperate\x1a\x1c.lq.ResAmuletActivityOperate\x12_\n\x19\x61muletActivityChangeHands\x12 .lq.ReqAmuletActivityChangeHands\x1a .lq.ResAmuletActivityChangeHands\x12S\n\x15\x61muletActivityUpgrade\x12\x1c.lq.ReqAmuletActivityUpgrade\x1a\x1c.lq.ResAmuletActivityUpgrade\x12G\n\x11\x61muletActivityBuy\x12\x18.lq.ReqAmuletActivityBuy\x1a\x18.lq.ResAmuletActivityBuy\x12\\\n\x18\x61muletActivitySelectPack\x12\x1f.lq.ReqAmuletActivitySelectPack\x1a\x1f.lq.ResAmuletActivitySelectPack\x12\\\n\x18\x61muletActivitySellEffect\x12\x1f.lq.ReqAmuletActivitySellEffect\x1a\x1f.lq.ResAmuletActivitySellEffect\x12J\n\x18\x61muletActivityEffectSort\x12\x1f.lq.ReqAmuletActivityEffectSort\x1a\r.lq.ResCommon\x12\x42\n\x14\x61muletActivityGiveup\x12\x1b.lq.ReqAmuletActivityGiveup\x1a\r.lq.ResCommon\x12_\n\x19\x61muletActivityRefreshShop\x12 .lq.ReqAmuletActivityRefreshShop\x1a .lq.ResAmuletActivityRefreshShop\x12n\n\x1e\x61muletActivitySelectFreeEffect\x12%.lq.ReqAmuletActivitySelectFreeEffect\x1a%.lq.ResAmuletActivitySelectFreeEffect\x12k\n\x1d\x61muletActivityUpgradeShopBuff\x12$.lq.ReqAmuletActivityUpgradeShopBuff\x1a$.lq.ResAmuletActivityUpgradeShopBuff\x12_\n\x19\x61muletActivityEndShopping\x12 .lq.ReqAmuletActivityEndShopping\x1a .lq.ResAmuletActivityEndShopping\x12P\n\x1b\x61muletActivitySetSkillLevel\x12\".lq.ReqAmuletActivitySetSkillLevel\x1a\r.lq.ResCommon\x12N\n\x1a\x61muletActivityMaintainInfo\x12\r.lq.ReqCommon\x1a!.lq.ResAmuletActivityMaintainInfo\x12n\n\x1e\x61muletActivitySelectRewardPack\x12%.lq.ReqAmuletActivitySelectRewardPack\x1a%.lq.ResAmuletActivitySelectRewardPack\x12q\n\x1f\x61muletActivityReceiveTaskReward\x12&.lq.ReqAmuletActivityReceiveTaskReward\x1a&.lq.ResAmuletActivityReceiveTaskReward\x12@\n\x13storyActivityUnlock\x12\x1a.lq.ReqStoryActivityUnlock\x1a\r.lq.ResCommon\x12L\n\x19storyActivityUnlockEnding\x12 .lq.ReqStoryActivityUnlockEnding\x1a\r.lq.ResCommon\x12_\n storyActivityReceiveEndingReward\x12\'.lq.ReqStoryActivityReceiveEndingReward\x1a\x12.lq.ResStoryReward\x12_\n storyActivityReceiveFinishReward\x12\'.lq.ReqStoryActivityReceiveFinishReward\x1a\x12.lq.ResStoryReward\x12\x65\n#storyActivityReceiveAllFinishReward\x12*.lq.ReqStoryActivityReceiveAllFinishReward\x1a\x12.lq.ResStoryReward\x12}\n#storyActivityUnlockEndingAndReceive\x12*.lq.ReqStoryActivityUnlockEndingAndReceive\x1a*.lq.ResStoryActivityUnlockEndingAndReceive\x12G\n\x11\x66\x65tchActivityRank\x12\x18.lq.ReqFetchActivityRank\x1a\x18.lq.ResFetchActivityRank\x12<\n\x11setVerifiedHidden\x12\x18.lq.ReqSetVerifiedHidden\x1a\r.lq.ResCommon\x12V\n\x16\x66\x65tchQuestionnaireList\x12\x1d.lq.ReqFetchQuestionnaireList\x1a\x1d.lq.ResFetchQuestionnaireList\x12\\\n\x18\x66\x65tchQuestionnaireDetail\x12\x1f.lq.ReqFetchQuestionnaireDetail\x1a\x1f.lq.ResFetchQuestionnaireDetail\x12@\n\x13submitQuestionnaire\x12\x1a.lq.ReqSubmitQuestionnaire\x1a\r.lq.ResCommon\x12N\n\x1asetFriendRoomRandomBotChar\x12!.lq.ReqSetFriendRoomRandomBotChar\x1a\r.lq.ResCommon\x12_\n\x19\x66\x65tchAccountGameHuRecords\x12 .lq.ReqFetchAccountGameHuRecords\x1a .lq.ResFetchAccountGameHuRecords\x12S\n\x15\x66\x65tchAccountInfoExtra\x12\x1c.lq.ReqFetchAccountInfoExtra\x1a\x1c.lq.ResFetchAccountInfoExtra\x12\x42\n\x14setAccountFavoriteHu\x12\x1b.lq.ReqSetAccountFavoriteHu\x1a\r.lq.ResCommon\x12\x41\n\x0f\x66\x65tchSeerReport\x12\x16.lq.ReqFetchSeerReport\x1a\x16.lq.ResFetchSeerReport\x12\x44\n\x10\x63reateSeerReport\x12\x17.lq.ReqCreateSeerReport\x1a\x17.lq.ResCreateSeerReport\x12@\n\x13\x66\x65tchSeerReportList\x12\r.lq.ReqCommon\x1a\x1a.lq.ResFetchSeerReportList\x12\x34\n\rfetchSeerInfo\x12\r.lq.ReqCommon\x1a\x14.lq.ResFetchSeerInfo\x12H\n\x1bselectChestChooseUpActivity\x12\x1a.lq.ReqSelectChestChooseUp\x1a\r.lq.ReqCommon\x12_\n\x19generateAnnualReportToken\x12 .lq.ReqGenerateAnnualReportToken\x1a .lq.ResGenerateAnnualReportToken\x12\x44\n\x15\x66\x65tchAnnualReportInfo\x12\r.lq.ReqCommon\x1a\x1c.lq.ResFetchAnnualReportInfo\x12\x32\n\x0cremarkFriend\x12\x13.lq.ReqRemarkFriend\x1a\r.lq.ResCommon\x12V\n\x16simV2ActivityFetchInfo\x12\x1d.lq.ReqSimV2ActivityFetchInfo\x1a\x1d.lq.ResSimV2ActivityFetchInfo\x12\\\n\x18simV2ActivityStartSeason\x12\x1f.lq.ReqSimV2ActivityStartSeason\x1a\x1f.lq.ResSimV2ActivityStartSeason\x12J\n\x12simV2ActivityTrain\x12\x19.lq.ReqSimV2ActivityTrain\x1a\x19.lq.ResSimV2ActivityTrain\x12\\\n\x18simV2ActivitySelectEvent\x12\x1f.lq.ReqSimV2ActivitySelectEvent\x1a\x1f.lq.ResSimV2ActivitySelectEvent\x12Y\n\x17simV2ActivityStartMatch\x12\x1e.lq.ReqSimV2ActivityStartMatch\x1a\x1e.lq.ResSimV2ActivityStartMatch\x12S\n\x15simV2ActivityEndMatch\x12\x1c.lq.ReqSimV2ActivityEndMatch\x1a\x1c.lq.ResSimV2ActivityEndMatch\x12@\n\x13simV2ActivityGiveUp\x12\x1a.lq.ReqSimV2ActivityGiveUp\x1a\r.lq.ResCommon\x12H\n\x17simV2ActivitySetUpgrade\x12\x1e.lq.ReqSimV2ActivitySetUpgrade\x1a\r.lq.ResCommon2\xf5\x06\n\x08\x46\x61stTest\x12,\n\x08\x61uthGame\x12\x0f.lq.ReqAuthGame\x1a\x0f.lq.ResAuthGame\x12,\n\tenterGame\x12\r.lq.ReqCommon\x1a\x10.lq.ResEnterGame\x12,\n\x08syncGame\x12\x0f.lq.ReqSyncGame\x1a\x0f.lq.ResSyncGame\x12.\n\x0e\x66inishSyncGame\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12-\n\rterminateGame\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x35\n\x0einputOperation\x12\x14.lq.ReqSelfOperation\x1a\r.lq.ResCommon\x12\x35\n\x10inputChiPengGang\x12\x12.lq.ReqChiPengGang\x1a\r.lq.ResCommon\x12/\n\x0f\x63onfirmNewRound\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x38\n\x0f\x62roadcastInGame\x12\x16.lq.ReqBroadcastInGame\x1a\r.lq.ResCommon\x12=\n\x12inputGameGMCommand\x12\x18.lq.ReqGMCommandInGaming\x1a\r.lq.ResCommon\x12=\n\x14\x66\x65tchGamePlayerState\x12\r.lq.ReqCommon\x1a\x16.lq.ResGamePlayerState\x12\x31\n\x11\x63heckNetworkDelay\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12,\n\x0c\x63learLeaving\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon\x12\x35\n\x0bvoteGameEnd\x12\x12.lq.ReqVoteGameEnd\x1a\x12.lq.ResGameEndVote\x12\x30\n\x0b\x61uthObserve\x12\x12.lq.ReqAuthObserve\x1a\r.lq.ResCommon\x12\x32\n\x0cstartObserve\x12\r.lq.ReqCommon\x1a\x13.lq.ResStartObserve\x12+\n\x0bstopObserve\x12\r.lq.ReqCommon\x1a\r.lq.ResCommon2\xcd\x01\n\x05Route\x12G\n\x11requestConnection\x12\x18.lq.ReqRequestConnection\x1a\x18.lq.ResRequestConnection\x12J\n\x12requestRouteChange\x12\x19.lq.ReqRequestRouteChange\x1a\x19.lq.ResRequestRouteChange\x12/\n\theartbeat\x12\x10.lq.ReqHeartbeat\x1a\x10.lq.ResHeartbeatB\x0fZ\r.;majprotocolb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'liqi_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\r.;majprotocol'
  _GAMEPLAYERSTATE._serialized_start=126213
  _GAMEPLAYERSTATE._serialized_end=126274
  _NOTIFYROOMGAMESTART._serialized_start=18
  _NOTIFYROOMGAMESTART._serialized_end=117
  _NOTIFYMATCHGAMESTART._serialized_start=119
  _NOTIFYMATCHGAMESTART._serialized_end=242
  _NOTIFYROOMPLAYERREADY._serialized_start=245
  _NOTIFYROOMPLAYERREADY._serialized_end=439
  _NOTIFYROOMPLAYERREADY_ACCOUNTREADYSTATE._serialized_start=385
  _NOTIFYROOMPLAYERREADY_ACCOUNTREADYSTATE._serialized_end=439
  _NOTIFYROOMPLAYERDRESSING._serialized_start=442
  _NOTIFYROOMPLAYERDRESSING._serialized_end=654
  _NOTIFYROOMPLAYERDRESSING_ACCOUNTDRESSINGSTATE._serialized_start=594
  _NOTIFYROOMPLAYERDRESSING_ACCOUNTDRESSINGSTATE._serialized_end=654
  _NOTIFYROOMPLAYERUPDATE._serialized_start=657
  _NOTIFYROOMPLAYERUPDATE._serialized_end=829
  _NOTIFYROOMKICKOUT._serialized_start=831
  _NOTIFYROOMKICKOUT._serialized_end=850
  _NOTIFYFRIENDSTATECHANGE._serialized_start=852
  _NOTIFYFRIENDSTATECHANGE._serialized_end=942
  _NOTIFYFRIENDVIEWCHANGE._serialized_start=944
  _NOTIFYFRIENDVIEWCHANGE._serialized_end=1021
  _NOTIFYFRIENDCHANGE._serialized_start=1023
  _NOTIFYFRIENDCHANGE._serialized_end=1105
  _NOTIFYNEWFRIENDAPPLY._serialized_start=1107
  _NOTIFYNEWFRIENDAPPLY._serialized_end=1189
  _NOTIFYCLIENTMESSAGE._serialized_start=1191
  _NOTIFYCLIENTMESSAGE._serialized_end=1279
  _NOTIFYACCOUNTUPDATE._serialized_start=1281
  _NOTIFYACCOUNTUPDATE._serialized_end=1337
  _NOTIFYANOTHERLOGIN._serialized_start=1339
  _NOTIFYANOTHERLOGIN._serialized_end=1359
  _NOTIFYACCOUNTLOGOUT._serialized_start=1361
  _NOTIFYACCOUNTLOGOUT._serialized_end=1382
  _NOTIFYANNOUNCEMENTUPDATE._serialized_start=1385
  _NOTIFYANNOUNCEMENTUPDATE._serialized_end=1535
  _NOTIFYANNOUNCEMENTUPDATE_ANNOUNCEMENTUPDATE._serialized_start=1483
  _NOTIFYANNOUNCEMENTUPDATE_ANNOUNCEMENTUPDATE._serialized_end=1535
  _NOTIFYNEWMAIL._serialized_start=1537
  _NOTIFYNEWMAIL._serialized_end=1576
  _NOTIFYDELETEMAIL._serialized_start=1578
  _NOTIFYDELETEMAIL._serialized_end=1618
  _NOTIFYREVIVECOINUPDATE._serialized_start=1620
  _NOTIFYREVIVECOINUPDATE._serialized_end=1664
  _NOTIFYDAILYTASKUPDATE._serialized_start=1666
  _NOTIFYDAILYTASKUPDATE._serialized_end=1780
  _NOTIFYACTIVITYTASKUPDATE._serialized_start=1782
  _NOTIFYACTIVITYTASKUPDATE._serialized_end=1846
  _NOTIFYACTIVITYPERIODTASKUPDATE._serialized_start=1848
  _NOTIFYACTIVITYPERIODTASKUPDATE._serialized_end=1918
  _NOTIFYACCOUNTRANDOMTASKUPDATE._serialized_start=1920
  _NOTIFYACCOUNTRANDOMTASKUPDATE._serialized_end=1989
  _NOTIFYACTIVITYSEGMENTTASKUPDATE._serialized_start=1991
  _NOTIFYACTIVITYSEGMENTTASKUPDATE._serialized_end=2069
  _NOTIFYACTIVITYUPDATE._serialized_start=2072
  _NOTIFYACTIVITYUPDATE._serialized_end=2643
  _NOTIFYACTIVITYUPDATE_FEEDACTIVITYDATA._serialized_start=2154
  _NOTIFYACTIVITYUPDATE_FEEDACTIVITYDATA._serialized_end=2643
  _NOTIFYACTIVITYUPDATE_FEEDACTIVITYDATA_COUNTWITHTIMEDATA._serialized_start=2467
  _NOTIFYACTIVITYUPDATE_FEEDACTIVITYDATA_COUNTWITHTIMEDATA._serialized_end=2527
  _NOTIFYACTIVITYUPDATE_FEEDACTIVITYDATA_GIFTBOXDATA._serialized_start=2529
  _NOTIFYACTIVITYUPDATE_FEEDACTIVITYDATA_GIFTBOXDATA._serialized_end=2643
  _NOTIFYACCOUNTCHALLENGETASKUPDATE._serialized_start=2646
  _NOTIFYACCOUNTCHALLENGETASKUPDATE._serialized_end=2821
  _NOTIFYNEWCOMMENT._serialized_start=2823
  _NOTIFYNEWCOMMENT._serialized_end=2841
  _NOTIFYROLLINGNOTICE._serialized_start=2843
  _NOTIFYROLLINGNOTICE._serialized_end=2864
  _NOTIFYMAINTAINNOTICE._serialized_start=2866
  _NOTIFYMAINTAINNOTICE._serialized_end=2888
  _NOTIFYGIFTSENDREFRESH._serialized_start=2890
  _NOTIFYGIFTSENDREFRESH._serialized_end=2913
  _NOTIFYSHOPUPDATE._serialized_start=2915
  _NOTIFYSHOPUPDATE._serialized_end=2966
  _NOTIFYINTERVALUPDATE._serialized_start=2968
  _NOTIFYINTERVALUPDATE._serialized_end=2990
  _NOTIFYVIPLEVELCHANGE._serialized_start=2993
  _NOTIFYVIPLEVELCHANGE._serialized_end=3176
  _NOTIFYSERVERSETTING._serialized_start=3178
  _NOTIFYSERVERSETTING._serialized_end=3237
  _NOTIFYPAYRESULT._serialized_start=3240
  _NOTIFYPAYRESULT._serialized_end=3460
  _NOTIFYPAYRESULT_RESOURCEMODIFY._serialized_start=3402
  _NOTIFYPAYRESULT_RESOURCEMODIFY._serialized_end=3460
  _NOTIFYCUSTOMCONTESTACCOUNTMSG._serialized_start=3462
  _NOTIFYCUSTOMCONTESTACCOUNTMSG._serialized_end=3583
  _NOTIFYCUSTOMCONTESTSYSTEMMSG._serialized_start=3586
  _NOTIFYCUSTOMCONTESTSYSTEMMSG._serialized_end=3763
  _NOTIFYMATCHTIMEOUT._serialized_start=3765
  _NOTIFYMATCHTIMEOUT._serialized_end=3798
  _NOTIFYMATCHFAILED._serialized_start=3800
  _NOTIFYMATCHFAILED._serialized_end=3832
  _NOTIFYCUSTOMCONTESTSTATE._serialized_start=3834
  _NOTIFYCUSTOMCONTESTSTATE._serialized_end=3894
  _NOTIFYACTIVITYCHANGE._serialized_start=3896
  _NOTIFYACTIVITYCHANGE._serialized_end=3980
  _NOTIFYAFKRESULT._serialized_start=3982
  _NOTIFYAFKRESULT._serialized_end=4054
  _NOTIFYLOGINQUEUEFINISHED._serialized_start=4056
  _NOTIFYLOGINQUEUEFINISHED._serialized_end=4082
  _NOTIFYGAMEFINISHREWARDV2._serialized_start=4085
  _NOTIFYGAMEFINISHREWARDV2._serialized_end=4781
  _NOTIFYGAMEFINISHREWARDV2_LEVELCHANGE._serialized_start=4434
  _NOTIFYGAMEFINISHREWARDV2_LEVELCHANGE._serialized_end=4528
  _NOTIFYGAMEFINISHREWARDV2_MATCHCHEST._serialized_start=4530
  _NOTIFYGAMEFINISHREWARDV2_MATCHCHEST._serialized_end=4643
  _NOTIFYGAMEFINISHREWARDV2_MAINCHARACTER._serialized_start=4645
  _NOTIFYGAMEFINISHREWARDV2_MAINCHARACTER._serialized_end=4701
  _NOTIFYGAMEFINISHREWARDV2_CHARACTERGIFT._serialized_start=4703
  _NOTIFYGAMEFINISHREWARDV2_CHARACTERGIFT._serialized_end=4781
  _NOTIFYACTIVITYREWARDV2._serialized_start=4784
  _NOTIFYACTIVITYREWARDV2._serialized_end=4948
  _NOTIFYACTIVITYREWARDV2_ACTIVITYREWARD._serialized_start=4878
  _NOTIFYACTIVITYREWARDV2_ACTIVITYREWARD._serialized_end=4948
  _NOTIFYACTIVITYPOINTV2._serialized_start=4951
  _NOTIFYACTIVITYPOINTV2._serialized_end=5093
  _NOTIFYACTIVITYPOINTV2_ACTIVITYPOINT._serialized_start=5042
  _NOTIFYACTIVITYPOINTV2_ACTIVITYPOINT._serialized_end=5093
  _NOTIFYLEADERBOARDPOINTV2._serialized_start=5096
  _NOTIFYLEADERBOARDPOINTV2._serialized_end=5256
  _NOTIFYLEADERBOARDPOINTV2_LEADERBOARDPOINT._serialized_start=5199
  _NOTIFYLEADERBOARDPOINTV2_LEADERBOARDPOINT._serialized_end=5256
  _NOTIFYSEERREPORT._serialized_start=5258
  _NOTIFYSEERREPORT._serialized_end=5307
  _NOTIFYCONNECTIONSHUTDOWN._serialized_start=5309
  _NOTIFYCONNECTIONSHUTDOWN._serialized_end=5369
  _ERROR._serialized_start=5371
  _ERROR._serialized_end=5452
  _WRAPPER._serialized_start=5454
  _WRAPPER._serialized_end=5491
  _NETWORKENDPOINT._serialized_start=5493
  _NETWORKENDPOINT._serialized_end=5557
  _REQCOMMON._serialized_start=5559
  _REQCOMMON._serialized_end=5570
  _RESCOMMON._serialized_start=5572
  _RESCOMMON._serialized_end=5609
  _RESACCOUNTUPDATE._serialized_start=5611
  _RESACCOUNTUPDATE._serialized_end=5690
  _ANTIADDICTION._serialized_start=5692
  _ANTIADDICTION._serialized_end=5732
  _HIGHESTHURECORD._serialized_start=5734
  _HIGHESTHURECORD._serialized_end=5861
  _ACCOUNTMAHJONGSTATISTIC._serialized_start=5864
  _ACCOUNTMAHJONGSTATISTIC._serialized_end=6686
  _ACCOUNTMAHJONGSTATISTIC_ROUNDSUMMARY._serialized_start=6302
  _ACCOUNTMAHJONGSTATISTIC_ROUNDSUMMARY._serialized_end=6402
  _ACCOUNTMAHJONGSTATISTIC_HUSUMMARY._serialized_start=6404
  _ACCOUNTMAHJONGSTATISTIC_HUSUMMARY._serialized_end=6481
  _ACCOUNTMAHJONGSTATISTIC_LIQI20SUMMARY._serialized_start=6483
  _ACCOUNTMAHJONGSTATISTIC_LIQI20SUMMARY._serialized_end=6573
  _ACCOUNTMAHJONGSTATISTIC_LIQI10SUMMARY._serialized_start=6575
  _ACCOUNTMAHJONGSTATISTIC_LIQI10SUMMARY._serialized_end=6637
  _ACCOUNTMAHJONGSTATISTIC_GAMERESULT._serialized_start=6639
  _ACCOUNTMAHJONGSTATISTIC_GAMERESULT._serialized_end=6686
  _ACCOUNTSTATISTICDATA._serialized_start=6689
  _ACCOUNTSTATISTICDATA._serialized_end=6827
  _ACCOUNTLEVEL._serialized_start=6829
  _ACCOUNTLEVEL._serialized_end=6870
  _VIEWSLOT._serialized_start=6872
  _VIEWSLOT._serialized_end=6949
  _FAVORITEHU._serialized_start=6951
  _FAVORITEHU._serialized_end=7042
  _ACCOUNT._serialized_start=7045
  _ACCOUNT._serialized_end=8132
  _ACCOUNT_PLATFORMDIAMOND._serialized_start=7857
  _ACCOUNT_PLATFORMDIAMOND._serialized_end=7901
  _ACCOUNT_PLATFORMSKINTICKET._serialized_start=7903
  _ACCOUNT_PLATFORMSKINTICKET._serialized_end=7950
  _ACCOUNT_CHALLENGELEVEL._serialized_start=7952
  _ACCOUNT_CHALLENGELEVEL._serialized_end=8013
  _ACCOUNT_ACHIEVEMENTCOUNT._serialized_start=8015
  _ACCOUNT_ACHIEVEMENTCOUNT._serialized_end=8062
  _ACCOUNT_BADGE._serialized_start=8064
  _ACCOUNT_BADGE._serialized_end=8132
  _ACCOUNTOWNERDATA._serialized_start=8134
  _ACCOUNTOWNERDATA._serialized_end=8179
  _ACCOUNTUPDATE._serialized_start=8182
  _ACCOUNTUPDATE._serialized_end=10363
  _ACCOUNTUPDATE_NUMERICALUPDATE._serialized_start=9190
  _ACCOUNTUPDATE_NUMERICALUPDATE._serialized_end=9234
  _ACCOUNTUPDATE_CHARACTERUPDATE._serialized_start=9236
  _ACCOUNTUPDATE_CHARACTERUPDATE._serialized_end=9355
  _ACCOUNTUPDATE_ACHIEVEMENTUPDATE._serialized_start=9357
  _ACCOUNTUPDATE_ACHIEVEMENTUPDATE._serialized_end=9445
  _ACCOUNTUPDATE_DAILYTASKUPDATE._serialized_start=9447
  _ACCOUNTUPDATE_DAILYTASKUPDATE._serialized_end=9521
  _ACCOUNTUPDATE_TITLEUPDATE._serialized_start=9523
  _ACCOUNTUPDATE_TITLEUPDATE._serialized_end=9579
  _ACCOUNTUPDATE_TASKUPDATE._serialized_start=9581
  _ACCOUNTUPDATE_TASKUPDATE._serialized_end=9650
  _ACCOUNTUPDATE_ACCOUNTCHALLENGEUPDATE._serialized_start=9653
  _ACCOUNTUPDATE_ACCOUNTCHALLENGEUPDATE._serialized_end=9837
  _ACCOUNTUPDATE_ACCOUNTABMATCHUPDATE._serialized_start=9840
  _ACCOUNTUPDATE_ACCOUNTABMATCHUPDATE._serialized_end=10093
  _ACCOUNTUPDATE_ACCOUNTABMATCHUPDATE_MATCHPOINT._serialized_start=10048
  _ACCOUNTUPDATE_ACCOUNTABMATCHUPDATE_MATCHPOINT._serialized_end=10093
  _ACCOUNTUPDATE_SEGMENTTASKUPDATE._serialized_start=10095
  _ACCOUNTUPDATE_SEGMENTTASKUPDATE._serialized_end=10178
  _ACCOUNTUPDATE_MONTHTICKETUPDATE._serialized_start=10180
  _ACCOUNTUPDATE_MONTHTICKETUPDATE._serialized_end=10240
  _ACCOUNTUPDATE_MAINCHARACTERUPDATE._serialized_start=10242
  _ACCOUNTUPDATE_MAINCHARACTERUPDATE._serialized_end=10302
  _ACCOUNTUPDATE_BADGEUPDATE._serialized_start=10304
  _ACCOUNTUPDATE_BADGEUPDATE._serialized_end=10363
  _GAMEMETADATA._serialized_start=10365
  _GAMEMETADATA._serialized_end=10434
  _ACCOUNTPLAYINGGAME._serialized_start=10436
  _ACCOUNTPLAYINGGAME._serialized_end=10525
  _RANDOMCHARACTER._serialized_start=10527
  _RANDOMCHARACTER._serialized_end=10583
  _ACCOUNTCACHEVIEW._serialized_start=10586
  _ACCOUNTCACHEVIEW._serialized_end=11004
  _PLAYERBASEVIEW._serialized_start=11007
  _PLAYERBASEVIEW._serialized_end=11221
  _PLAYERGAMEVIEW._serialized_start=11224
  _PLAYERGAMEVIEW._serialized_end=11482
  _GAMESETTING._serialized_start=11484
  _GAMESETTING._serialized_end=11519
  _GAMEMODE._serialized_start=11522
  _GAMEMODE._serialized_end=11718
  _GAMETESTINGENVIRONMENTSET._serialized_start=11720
  _GAMETESTINGENVIRONMENTSET._serialized_end=11809
  _GAMEDETAILRULE._serialized_start=11812
  _GAMEDETAILRULE._serialized_end=13465
  _ROOM._serialized_start=13468
  _ROOM._serialized_end=13791
  _GAMEENDRESULT._serialized_start=13794
  _GAMEENDRESULT._serialized_end=13987
  _GAMEENDRESULT_PLAYERITEM._serialized_start=13859
  _GAMEENDRESULT_PLAYERITEM._serialized_end=13987
  _GAMECONNECTINFO._serialized_start=13989
  _GAMECONNECTINFO._serialized_end=14066
  _ITEMGAINRECORD._serialized_start=14068
  _ITEMGAINRECORD._serialized_end=14116
  _ITEMGAINRECORDS._serialized_start=14118
  _ITEMGAINRECORDS._serialized_end=14218
  _FAKERANDOMRECORDS._serialized_start=14220
  _FAKERANDOMRECORDS._serialized_end=14323
  _ITEM._serialized_start=14325
  _ITEM._serialized_end=14363
  _BAG._serialized_start=14365
  _BAG._serialized_end=14443
  _BAGUPDATE._serialized_start=14445
  _BAGUPDATE._serialized_end=14543
  _REWARDSLOT._serialized_start=14545
  _REWARDSLOT._serialized_end=14584
  _OPENRESULT._serialized_start=14586
  _OPENRESULT._serialized_end=14663
  _REWARDPLUSRESULT._serialized_start=14666
  _REWARDPLUSRESULT._serialized_end=14817
  _REWARDPLUSRESULT_EXCHANGE._serialized_start=14762
  _REWARDPLUSRESULT_EXCHANGE._serialized_end=14817
  _EXECUTEREWARD._serialized_start=14819
  _EXECUTEREWARD._serialized_end=14922
  _EXECUTERESULT._serialized_start=14924
  _EXECUTERESULT._serialized_end=14966
  _I18NCONTEXT._serialized_start=14968
  _I18NCONTEXT._serialized_end=15012
  _MAIL._serialized_start=15015
  _MAIL._serialized_end=15308
  _ACHIEVEMENTPROGRESS._serialized_start=15310
  _ACHIEVEMENTPROGRESS._serialized_end=15419
  _BADGEACHIEVEPROGRESS._serialized_start=15421
  _BADGEACHIEVEPROGRESS._serialized_end=15521
  _ACCOUNTSTATISTICBYGAMEMODE._serialized_start=15524
  _ACCOUNTSTATISTICBYGAMEMODE._serialized_end=16059
  _ACCOUNTSTATISTICBYGAMEMODE_ROUNDENDDATA._serialized_start=15957
  _ACCOUNTSTATISTICBYGAMEMODE_ROUNDENDDATA._serialized_end=15998
  _ACCOUNTSTATISTICBYGAMEMODE_RANKSCORE._serialized_start=16000
  _ACCOUNTSTATISTICBYGAMEMODE_RANKSCORE._serialized_end=16059
  _ACCOUNTSTATISTICBYFAN._serialized_start=16061
  _ACCOUNTSTATISTICBYFAN._serialized_end=16113
  _ACCOUNTFANACHIEVED._serialized_start=16115
  _ACCOUNTFANACHIEVED._serialized_end=16223
  _ACCOUNTDETAILSTATISTIC._serialized_start=16226
  _ACCOUNTDETAILSTATISTIC._serialized_end=16409
  _ACCOUNTDETAILSTATISTICBYCATEGORY._serialized_start=16411
  _ACCOUNTDETAILSTATISTICBYCATEGORY._serialized_end=16517
  _ACCOUNTDETAILSTATISTICV2._serialized_start=16520
  _ACCOUNTDETAILSTATISTICV2._serialized_end=17881
  _ACCOUNTDETAILSTATISTICV2_RANKSTATISTIC._serialized_start=17034
  _ACCOUNTDETAILSTATISTICV2_RANKSTATISTIC._serialized_end=17479
  _ACCOUNTDETAILSTATISTICV2_RANKSTATISTIC_RANKDATA._serialized_start=17236
  _ACCOUNTDETAILSTATISTICV2_RANKSTATISTIC_RANKDATA._serialized_end=17479
  _ACCOUNTDETAILSTATISTICV2_RANKSTATISTIC_RANKDATA_RANKLEVELDATA._serialized_start=17397
  _ACCOUNTDETAILSTATISTICV2_RANKSTATISTIC_RANKDATA_RANKLEVELDATA._serialized_end=17479
  _ACCOUNTDETAILSTATISTICV2_CUSTOMIZEDCONTESTSTATISTIC._serialized_start=17482
  _ACCOUNTDETAILSTATISTICV2_CUSTOMIZEDCONTESTSTATISTIC._serialized_end=17644
  _ACCOUNTDETAILSTATISTICV2_CHALLENGESTATISTIC._serialized_start=17647
  _ACCOUNTDETAILSTATISTICV2_CHALLENGESTATISTIC._serialized_end=17881
  _ACCOUNTDETAILSTATISTICV2_CHALLENGESTATISTIC_SEASONDATA._serialized_start=17803
  _ACCOUNTDETAILSTATISTICV2_CHALLENGESTATISTIC_SEASONDATA._serialized_end=17881
  _ACCOUNTSHILIAN._serialized_start=17883
  _ACCOUNTSHILIAN._serialized_end=17928
  _CLIENTDEVICEINFO._serialized_start=17931
  _CLIENTDEVICEINFO._serialized_end=18211
  _CLIENTVERSIONINFO._serialized_start=18213
  _CLIENTVERSIONINFO._serialized_end=18267
  _ANNOUNCEMENT._serialized_start=18269
  _ANNOUNCEMENT._serialized_end=18349
  _TASKPROGRESS._serialized_start=18351
  _TASKPROGRESS._serialized_end=18469
  _GAMECONFIG._serialized_start=18471
  _GAMECONFIG._serialized_end=18561
  _RPGSTATE._serialized_start=18563
  _RPGSTATE._serialized_end=18643
  _RPGACTIVITY._serialized_start=18646
  _RPGACTIVITY._serialized_end=18854
  _ACTIVITYARENADATA._serialized_start=18857
  _ACTIVITYARENADATA._serialized_end=19057
  _FEEDACTIVITYDATA._serialized_start=19060
  _FEEDACTIVITYDATA._serialized_end=19486
  _FEEDACTIVITYDATA_COUNTWITHTIMEDATA._serialized_start=2467
  _FEEDACTIVITYDATA_COUNTWITHTIMEDATA._serialized_end=2527
  _FEEDACTIVITYDATA_GIFTBOXDATA._serialized_start=2529
  _FEEDACTIVITYDATA_GIFTBOXDATA._serialized_end=2643
  _SEGMENTTASKPROGRESS._serialized_start=19489
  _SEGMENTTASKPROGRESS._serialized_end=19637
  _MINEACTIVITYDATA._serialized_start=19639
  _MINEACTIVITYDATA._serialized_end=19728
  _ACCOUNTACTIVITYUPDATE._serialized_start=19731
  _ACCOUNTACTIVITYUPDATE._serialized_end=20452
  _ACTIVITYCOMBININGWORKBENCH._serialized_start=20454
  _ACTIVITYCOMBININGWORKBENCH._serialized_end=20513
  _ACTIVITYCOMBININGMENUDATA._serialized_start=20516
  _ACTIVITYCOMBININGMENUDATA._serialized_end=20738
  _ACTIVITYCOMBININGMENUDATA_MENUREQUIRE._serialized_start=20695
  _ACTIVITYCOMBININGMENUDATA_MENUREQUIRE._serialized_end=20738
  _ACTIVITYCOMBININGORDERDATA._serialized_start=20741
  _ACTIVITYCOMBININGORDERDATA._serialized_end=20876
  _ACTIVITYCOMBININGLQDATA._serialized_start=20879
  _ACTIVITYCOMBININGLQDATA._serialized_end=21128
  _ACTIVITYCOMBININGPOOLDATA._serialized_start=21130
  _ACTIVITYCOMBININGPOOLDATA._serialized_end=21187
  _ACTIVITYCOMBININGDATA._serialized_start=21190
  _ACTIVITYCOMBININGDATA._serialized_end=21684
  _ACTIVITYCOMBININGDATA_BONUSDATA._serialized_start=21637
  _ACTIVITYCOMBININGDATA_BONUSDATA._serialized_end=21684
  _VILLAGEREWARD._serialized_start=21686
  _VILLAGEREWARD._serialized_end=21728
  _VILLAGEBUILDINGDATA._serialized_start=21730
  _VILLAGEBUILDINGDATA._serialized_end=21815
  _VILLAGETRIPDATA._serialized_start=21818
  _VILLAGETRIPDATA._serialized_end=21960
  _VILLAGETASKDATA._serialized_start=21962
  _VILLAGETASKDATA._serialized_end=22016
  _VILLAGETARGETINFO._serialized_start=22018
  _VILLAGETARGETINFO._serialized_end=22126
  _ACTIVITYVILLAGEDATA._serialized_start=22129
  _ACTIVITYVILLAGEDATA._serialized_end=22301
  _TIMECOUNTERDATA._serialized_start=22303
  _TIMECOUNTERDATA._serialized_end=22356
  _SIGNEDTIMECOUNTERDATA._serialized_start=22358
  _SIGNEDTIMECOUNTERDATA._serialized_end=22417
  _FESTIVALPROPOSALDATA._serialized_start=22419
  _FESTIVALPROPOSALDATA._serialized_end=22487
  _ACTIVITYFESTIVALDATA._serialized_start=22490
  _ACTIVITYFESTIVALDATA._serialized_end=22664
  _SIMULATIONV2DATA._serialized_start=22667
  _SIMULATIONV2DATA._serialized_end=22857
  _ISLANDBAGITEMDATA._serialized_start=22859
  _ISLANDBAGITEMDATA._serialized_end=22952
  _ISLANDBAGDATA._serialized_start=22954
  _ISLANDBAGDATA._serialized_end=23035
  _ISLANDGOODSDATA._serialized_start=23037
  _ISLANDGOODSDATA._serialized_end=23108
  _ISLANDZONEDATA._serialized_start=23110
  _ISLANDZONEDATA._serialized_end=23232
  _ACTIVITYISLANDDATA._serialized_start=23234
  _ACTIVITYISLANDDATA._serialized_end=23357
  _AMULETEFFECTDATA._serialized_start=23359
  _AMULETEFFECTDATA._serialized_end=23417
  _AMULETBUFFDATA._serialized_start=23419
  _AMULETBUFFDATA._serialized_end=23462
  _AMULETGAMESHOPGOODS._serialized_start=23464
  _AMULETGAMESHOPGOODS._serialized_end=23544
  _AMULETACTIVITYTINGINFO._serialized_start=23546
  _AMULETACTIVITYTINGINFO._serialized_end=23616
  _AMULETSHOWDESKTOPTILEDATA._serialized_start=23618
  _AMULETSHOWDESKTOPTILEDATA._serialized_end=23670
  _AMULETGAMEOPERATION._serialized_start=23673
  _AMULETGAMEOPERATION._serialized_end=23804
  _AMULETGAMEOPERATION_GANGTILES._serialized_start=23778
  _AMULETGAMEOPERATION_GANGTILES._serialized_end=23804
  _AMULETGAMESHOPDATA._serialized_start=23807
  _AMULETGAMESHOPDATA._serialized_end=23962
  _AMULETGAMEUPDATEDATA._serialized_start=23965
  _AMULETGAMEUPDATEDATA._serialized_end=24644
  _AMULETGAMERECORDDATA._serialized_start=24646
  _AMULETGAMERECORDDATA._serialized_end=24742
  _AMULETGAMETILESCOREDATA._serialized_start=24744
  _AMULETGAMETILESCOREDATA._serialized_end=24798
  _AMULETGAMEDATA._serialized_start=24801
  _AMULETGAMEDATA._serialized_end=26003
  _ACTIVITYAMULETUPDATEDATA._serialized_start=26005
  _ACTIVITYAMULETUPDATEDATA._serialized_end=26119
  _AMULETSKILLDATA._serialized_start=26121
  _AMULETSKILLDATA._serialized_end=26165
  _ACTIVITYAMULETUPGRADEDATA._serialized_start=26167
  _ACTIVITYAMULETUPGRADEDATA._serialized_end=26230
  _ACTIVITYAMULETRECORD._serialized_start=26232
  _ACTIVITYAMULETRECORD._serialized_end=26299
  _ACTIVITYAMULETHURECORD._serialized_start=26301
  _ACTIVITYAMULETHURECORD._serialized_end=26380
  _ACTIVITYAMULETILLUSTRATEDBOOKDATA._serialized_start=26383
  _ACTIVITYAMULETILLUSTRATEDBOOKDATA._serialized_end=26516
  _ACTIVITYAMULETTASKDATA._serialized_start=26518
  _ACTIVITYAMULETTASKDATA._serialized_end=26578
  _ACTIVITYAMULETDATA._serialized_start=26581
  _ACTIVITYAMULETDATA._serialized_end=26828
  _ACTIVITYFEEDDATA._serialized_start=26831
  _ACTIVITYFEEDDATA._serialized_end=27279
  _ACTIVITYFEEDDATA_COUNTWITHTIMEDATA._serialized_start=2467
  _ACTIVITYFEEDDATA_COUNTWITHTIMEDATA._serialized_end=2527
  _ACTIVITYFEEDDATA_GIFTBOXDATA._serialized_start=2529
  _ACTIVITYFEEDDATA_GIFTBOXDATA._serialized_end=2643
  _UNLOCKEDSTORYDATA._serialized_start=27282
  _UNLOCKEDSTORYDATA._serialized_end=27423
  _ACTIVITYSTORYDATA._serialized_start=27425
  _ACTIVITYSTORYDATA._serialized_end=27512
  _ACTIVITYCHOOSEUPDATA._serialized_start=27514
  _ACTIVITYCHOOSEUPDATA._serialized_end=27610
  _ACTIVITYFRIENDGIFTDATA._serialized_start=27613
  _ACTIVITYFRIENDGIFTDATA._serialized_end=28075
  _ACTIVITYFRIENDGIFTDATA_COUNTWITHTIMEDATA._serialized_start=27875
  _ACTIVITYFRIENDGIFTDATA_COUNTWITHTIMEDATA._serialized_end=27959
  _ACTIVITYFRIENDGIFTDATA_GIFTBOXDATA._serialized_start=2529
  _ACTIVITYFRIENDGIFTDATA_GIFTBOXDATA._serialized_end=2643
  _ACTIVITYUPGRADEDATA._serialized_start=28078
  _ACTIVITYUPGRADEDATA._serialized_end=28243
  _ACTIVITYUPGRADEDATA_LEVELGROUP._serialized_start=28198
  _ACTIVITYUPGRADEDATA_LEVELGROUP._serialized_end=28243
  _GACHARECORD._serialized_start=28245
  _GACHARECORD._serialized_end=28285
  _ACTIVITYGACHADATA._serialized_start=28287
  _ACTIVITYGACHADATA._serialized_end=28360
  _ACTIVITYGACHAUPDATEDATA._serialized_start=28362
  _ACTIVITYGACHAUPDATEDATA._serialized_end=28463
  _ACTIVITYSIMULATIONGAMERECORDMESSAGE._serialized_start=28465
  _ACTIVITYSIMULATIONGAMERECORDMESSAGE._serialized_end=28543
  _ACTIVITYSIMULATIONGAMERECORD._serialized_start=28546
  _ACTIVITYSIMULATIONGAMERECORD._serialized_end=28715
  _ACTIVITYSIMULATIONDAILYCONTEST._serialized_start=28718
  _ACTIVITYSIMULATIONDAILYCONTEST._serialized_end=28849
  _ACTIVITYSIMULATIONTRAINRECORD._serialized_start=28851
  _ACTIVITYSIMULATIONTRAINRECORD._serialized_end=28953
  _ACTIVITYSIMULATIONDATA._serialized_start=28956
  _ACTIVITYSIMULATIONDATA._serialized_end=29162
  _ACTIVITYSPOTDATA._serialized_start=29165
  _ACTIVITYSPOTDATA._serialized_end=29342
  _ACTIVITYSPOTDATA_SPOTDATA._serialized_start=29252
  _ACTIVITYSPOTDATA_SPOTDATA._serialized_end=29342
  _ACCOUNTACTIVESTATE._serialized_start=29345
  _ACCOUNTACTIVESTATE._serialized_end=29486
  _FRIEND._serialized_start=29488
  _FRIEND._serialized_end=29585
  _POINT._serialized_start=29587
  _POINT._serialized_end=29616
  _MINEREWARD._serialized_start=29618
  _MINEREWARD._serialized_end=29693
  _GAMELIVEUNIT._serialized_start=29695
  _GAMELIVEUNIT._serialized_end=29774
  _GAMELIVESEGMENT._serialized_start=29776
  _GAMELIVESEGMENT._serialized_end=29828
  _GAMELIVESEGMENTURI._serialized_start=29830
  _GAMELIVESEGMENTURI._serialized_end=29891
  _GAMELIVEHEAD._serialized_start=29894
  _GAMELIVEHEAD._serialized_end=30035
  _GAMENEWROUNDSTATE._serialized_start=30037
  _GAMENEWROUNDSTATE._serialized_end=30077
  _GAMEENDACTION._serialized_start=30079
  _GAMEENDACTION._serialized_end=30109
  _GAMENOOPACTION._serialized_start=30111
  _GAMENOOPACTION._serialized_end=30127
  _COMMENTITEM._serialized_start=30129
  _COMMENTITEM._serialized_end=30256
  _ROLLINGNOTICE._serialized_start=30259
  _ROLLINGNOTICE._serialized_end=30396
  _MAINTAINNOTICE._serialized_start=30398
  _MAINTAINNOTICE._serialized_end=30437
  _BILLINGGOODS._serialized_start=30439
  _BILLINGGOODS._serialized_end=30552
  _BILLSHORTCUT._serialized_start=30554
  _BILLSHORTCUT._serialized_end=30615
  _BILLINGPRODUCT._serialized_start=30617
  _BILLINGPRODUCT._serialized_end=30734
  _CHARACTER._serialized_start=30737
  _CHARACTER._serialized_end=30901
  _BUYRECORD._serialized_start=30903
  _BUYRECORD._serialized_end=30941
  _ZHPSHOP._serialized_start=30944
  _ZHPSHOP._serialized_end=31146
  _ZHPSHOP_REFRESHCOUNT._serialized_start=31102
  _ZHPSHOP_REFRESHCOUNT._serialized_end=31146
  _MONTHTICKETINFO._serialized_start=31148
  _MONTHTICKETINFO._serialized_end=31218
  _SHOPINFO._serialized_start=31220
  _SHOPINFO._serialized_end=31319
  _CHANGENICKNAMERECORD._serialized_start=31321
  _CHANGENICKNAMERECORD._serialized_end=31383
  _SERVERSETTINGS._serialized_start=31386
  _SERVERSETTINGS._serialized_end=31544
  _NICKNAMESETTING._serialized_start=31546
  _NICKNAMESETTING._serialized_end=31598
  _PAYMENTSETTINGV2._serialized_start=31601
  _PAYMENTSETTINGV2._serialized_end=32094
  _PAYMENTSETTINGV2_PAYMENTMAINTAIN._serialized_start=31712
  _PAYMENTSETTINGV2_PAYMENTMAINTAIN._serialized_end=31846
  _PAYMENTSETTINGV2_PAYMENTSETTINGUNIT._serialized_start=31849
  _PAYMENTSETTINGV2_PAYMENTSETTINGUNIT._serialized_end=32094
  _PAYMENTSETTING._serialized_start=32097
  _PAYMENTSETTING._serialized_end=32448
  _PAYMENTSETTING_WECHATDATA._serialized_start=32285
  _PAYMENTSETTING_WECHATDATA._serialized_end=32377
  _PAYMENTSETTING_ALIPAYDATA._serialized_start=32379
  _PAYMENTSETTING_ALIPAYDATA._serialized_end=32448
  _ACCOUNTSETTING._serialized_start=32450
  _ACCOUNTSETTING._serialized_end=32494
  _CHESTDATA._serialized_start=32496
  _CHESTDATA._serialized_end=32600
  _CHESTDATAV2._serialized_start=32602
  _CHESTDATAV2._serialized_end=32718
  _FAITHDATA._serialized_start=32720
  _FAITHDATA._serialized_end=32820
  _CUSTOMIZEDCONTESTBASE._serialized_start=32823
  _CUSTOMIZEDCONTESTBASE._serialized_end=33107
  _CUSTOMIZEDCONTESTEXTEND._serialized_start=33109
  _CUSTOMIZEDCONTESTEXTEND._serialized_end=33176
  _CUSTOMIZEDCONTESTABSTRACT._serialized_start=33179
  _CUSTOMIZEDCONTESTABSTRACT._serialized_end=33423
  _CUSTOMIZEDCONTESTDETAIL._serialized_start=33426
  _CUSTOMIZEDCONTESTDETAIL._serialized_end=33888
  _CUSTOMIZEDCONTESTPLAYERREPORT._serialized_start=33890
  _CUSTOMIZEDCONTESTPLAYERREPORT._serialized_end=34015
  _RECORDGAME._serialized_start=34018
  _RECORDGAME._serialized_end=34534
  _RECORDGAME_ACCOUNTINFO._serialized_start=34265
  _RECORDGAME_ACCOUNTINFO._serialized_end=34534
  _RECORDLISTENTRY._serialized_start=34537
  _RECORDLISTENTRY._serialized_end=34716
  _RECORDPLAYERRESULT._serialized_start=34719
  _RECORDPLAYERRESULT._serialized_end=35023
  _CUSTOMIZEDCONTESTGAMESTART._serialized_start=35026
  _CUSTOMIZEDCONTESTGAMESTART._serialized_end=35154
  _CUSTOMIZEDCONTESTGAMESTART_ITEM._serialized_start=35110
  _CUSTOMIZEDCONTESTGAMESTART_ITEM._serialized_end=35154
  _CUSTOMIZEDCONTESTGAMEEND._serialized_start=35157
  _CUSTOMIZEDCONTESTGAMEEND._serialized_end=35302
  _CUSTOMIZEDCONTESTGAMEEND_ITEM._serialized_start=35237
  _CUSTOMIZEDCONTESTGAMEEND_ITEM._serialized_end=35302
  _ACTIVITY._serialized_start=35304
  _ACTIVITY._serialized_end=35387
  _EXCHANGERECORD._serialized_start=35389
  _EXCHANGERECORD._serialized_end=35441
  _ACTIVITYACCUMULATEDPOINTDATA._serialized_start=35443
  _ACTIVITYACCUMULATEDPOINTDATA._serialized_end=35537
  _ACTIVITYRANKPOINTDATA._serialized_start=35539
  _ACTIVITYRANKPOINTDATA._serialized_end=35647
  _GAMEROUNDHUDATA._serialized_start=35650
  _GAMEROUNDHUDATA._serialized_end=36091
  _GAMEROUNDHUDATA_HUPAI._serialized_start=35995
  _GAMEROUNDHUDATA_HUPAI._serialized_end=36044
  _GAMEROUNDHUDATA_FAN._serialized_start=36046
  _GAMEROUNDHUDATA_FAN._serialized_end=36091
  _GAMEROUNDPLAYERFANGCHONGINFO._serialized_start=36093
  _GAMEROUNDPLAYERFANGCHONGINFO._serialized_end=36175
  _GAMEROUNDPLAYERRESULT._serialized_start=36178
  _GAMEROUNDPLAYERRESULT._serialized_end=36458
  _GAMEROUNDPLAYER._serialized_start=36460
  _GAMEROUNDPLAYER._serialized_end=36549
  _GAMEROUNDSNAPSHOT._serialized_start=36551
  _GAMEROUNDSNAPSHOT._serialized_end=36633
  _GAMEFINALSNAPSHOT._serialized_start=36636
  _GAMEFINALSNAPSHOT._serialized_end=37591
  _GAMEFINALSNAPSHOT_CALCULATEPARAM._serialized_start=37166
  _GAMEFINALSNAPSHOT_CALCULATEPARAM._serialized_end=37249
  _GAMEFINALSNAPSHOT_GAMESEAT._serialized_start=37252
  _GAMEFINALSNAPSHOT_GAMESEAT._serialized_end=37388
  _GAMEFINALSNAPSHOT_FINALPLAYER._serialized_start=37391
  _GAMEFINALSNAPSHOT_FINALPLAYER._serialized_end=37520
  _GAMEFINALSNAPSHOT_AFKINFO._serialized_start=37522
  _GAMEFINALSNAPSHOT_AFKINFO._serialized_end=37591
  _RECORDCOLLECTEDDATA._serialized_start=37593
  _RECORDCOLLECTEDDATA._serialized_end=37683
  _CONTESTDETAILRULE._serialized_start=37686
  _CONTESTDETAILRULE._serialized_end=38984
  _CONTESTDETAILRULEV2._serialized_start=38987
  _CONTESTDETAILRULEV2._serialized_end=39166
  _CONTESTDETAILRULEV2_EXTRARULE._serialized_start=39107
  _CONTESTDETAILRULEV2_EXTRARULE._serialized_end=39166
  _GAMERULESETTING._serialized_start=39169
  _GAMERULESETTING._serialized_end=39340
  _RECORDTINGPAIINFO._serialized_start=39343
  _RECORDTINGPAIINFO._serialized_end=39516
  _RECORDNOTILEPLAYERINFO._serialized_start=39518
  _RECORDNOTILEPLAYERINFO._serialized_end=39627
  _RECORDHULEINFO._serialized_start=39630
  _RECORDHULEINFO._serialized_end=40089
  _RECORDHULEINFO_RECORDFANINFO._serialized_start=40049
  _RECORDHULEINFO_RECORDFANINFO._serialized_end=40089
  _RECORDHULESINFO._serialized_start=40091
  _RECORDHULESINFO._serialized_end=40157
  _RECORDLIUJUINFO._serialized_start=40159
  _RECORDLIUJUINFO._serialized_end=40204
  _RECORDNOTILEINFO._serialized_start=40206
  _RECORDNOTILEINFO._serialized_end=40291
  _RECORDLIQIINFO._serialized_start=40293
  _RECORDLIQIINFO._serialized_end=40407
  _RECORDGANGINFO._serialized_start=40409
  _RECORDGANGINFO._serialized_end=40496
  _RECORDBABEIINFO._serialized_start=40498
  _RECORDBABEIINFO._serialized_end=40581
  _RECORDPEIPAIINFO._serialized_start=40583
  _RECORDPEIPAIINFO._serialized_end=40662
  _RECORDROUNDINFO._serialized_start=40665
  _RECORDROUNDINFO._serialized_end=41044
  _RECORDANALYSISEDDATA._serialized_start=41046
  _RECORDANALYSISEDDATA._serialized_end=41110
  _VOTEDATA._serialized_start=41112
  _VOTEDATA._serialized_end=41172
  _ACTIVITYBUFFDATA._serialized_start=41174
  _ACTIVITYBUFFDATA._serialized_end=41260
  _ACCOUNTRESOURCESNAPSHOT._serialized_start=41263
  _ACCOUNTRESOURCESNAPSHOT._serialized_end=41797
  _ACCOUNTRESOURCESNAPSHOT_BAGITEMSNAPSHOT._serialized_start=41568
  _ACCOUNTRESOURCESNAPSHOT_BAGITEMSNAPSHOT._serialized_end=41656
  _ACCOUNTRESOURCESNAPSHOT_CURRENCYSNAPSHOT._serialized_start=41658
  _ACCOUNTRESOURCESNAPSHOT_CURRENCYSNAPSHOT._serialized_end=41721
  _ACCOUNTRESOURCESNAPSHOT_TITLESNAPSHOT._serialized_start=41723
  _ACCOUNTRESOURCESNAPSHOT_TITLESNAPSHOT._serialized_end=41758
  _ACCOUNTRESOURCESNAPSHOT_USEDTITLESNAPSHOT._serialized_start=41760
  _ACCOUNTRESOURCESNAPSHOT_USEDTITLESNAPSHOT._serialized_end=41797
  _ACCOUNTCHARACTERSNAPSHOT._serialized_start=41800
  _ACCOUNTCHARACTERSNAPSHOT._serialized_end=42272
  _ACCOUNTCHARACTERSNAPSHOT_MAINCHARACTERSNAPSHOT._serialized_start=42151
  _ACCOUNTCHARACTERSNAPSHOT_MAINCHARACTERSNAPSHOT._serialized_end=42196
  _ACCOUNTCHARACTERSNAPSHOT_SKINSSNAPSHOT._serialized_start=42198
  _ACCOUNTCHARACTERSNAPSHOT_SKINSSNAPSHOT._serialized_end=42232
  _ACCOUNTCHARACTERSNAPSHOT_HIDDENCHARACTER._serialized_start=42234
  _ACCOUNTCHARACTERSNAPSHOT_HIDDENCHARACTER._serialized_end=42272
  _ACCOUNTMAILRECORD._serialized_start=42275
  _ACCOUNTMAILRECORD._serialized_end=42596
  _ACCOUNTMAILRECORD_MAILSNAPSHOT._serialized_start=42439
  _ACCOUNTMAILRECORD_MAILSNAPSHOT._serialized_end=42596
  _ACCOUNTACHIEVEMENTSNAPSHOT._serialized_start=42599
  _ACCOUNTACHIEVEMENTSNAPSHOT._serialized_end=42905
  _ACCOUNTACHIEVEMENTSNAPSHOT_REWARDEDGROUPSNAPSHOT._serialized_start=42822
  _ACCOUNTACHIEVEMENTSNAPSHOT_REWARDEDGROUPSNAPSHOT._serialized_end=42866
  _ACCOUNTACHIEVEMENTSNAPSHOT_ACHIEVEMENTVERSION._serialized_start=42868
  _ACCOUNTACHIEVEMENTSNAPSHOT_ACHIEVEMENTVERSION._serialized_end=42905
  _ACCOUNTMISCSNAPSHOT._serialized_start=42908
  _ACCOUNTMISCSNAPSHOT._serialized_end=43909
  _ACCOUNTMISCSNAPSHOT_ACCOUNTVIPREWARDSNAPSHOT._serialized_start=43342
  _ACCOUNTMISCSNAPSHOT_ACCOUNTVIPREWARDSNAPSHOT._serialized_end=43386
  _ACCOUNTMISCSNAPSHOT_MONTHTICKETINFO._serialized_start=43388
  _ACCOUNTMISCSNAPSHOT_MONTHTICKETINFO._serialized_end=43502
  _ACCOUNTMISCSNAPSHOT_ACCOUNTMONTHTICKETSNAPSHOT._serialized_start=43504
  _ACCOUNTMISCSNAPSHOT_ACCOUNTMONTHTICKETSNAPSHOT._serialized_end=43590
  _ACCOUNTMISCSNAPSHOT_ACCOUNTVIP._serialized_start=43592
  _ACCOUNTMISCSNAPSHOT_ACCOUNTVIP._serialized_end=43617
  _ACCOUNTMISCSNAPSHOT_ACCOUNTRECHARGEINFO._serialized_start=43620
  _ACCOUNTMISCSNAPSHOT_ACCOUNTRECHARGEINFO._serialized_end=43792
  _ACCOUNTMISCSNAPSHOT_ACCOUNTRECHARGEINFO_RECHARGERECORD._serialized_start=43738
  _ACCOUNTMISCSNAPSHOT_ACCOUNTRECHARGEINFO_RECHARGERECORD._serialized_end=43792
  _ACCOUNTMISCSNAPSHOT_ACCOUNTMONTHTICKETSNAPSHOTV2._serialized_start=43794
  _ACCOUNTMISCSNAPSHOT_ACCOUNTMONTHTICKETSNAPSHOTV2._serialized_end=43909
  _ACCOUNTGIFTCODERECORD._serialized_start=43911
  _ACCOUNTGIFTCODERECORD._serialized_end=43958
  _ACCSN._serialized_start=43961
  _ACCSN._serialized_end=44239
  _ACCSNDA._serialized_start=44241
  _ACCSNDA._serialized_end=44302
  _TRANSPARENTDATA._serialized_start=44304
  _TRANSPARENTDATA._serialized_end=44405
  _AMULETTILE._serialized_start=44407
  _AMULETTILE._serialized_end=44445
  _AMULETFAN._serialized_start=44447
  _AMULETFAN._serialized_end=44513
  _AMULETREPLACE._serialized_start=44515
  _AMULETREPLACE._serialized_end=44556
  _AMULETMINGINFO._serialized_start=44558
  _AMULETMINGINFO._serialized_end=44607
  _AMULETACTIVITYHOOKEFFECT._serialized_start=44610
  _AMULETACTIVITYHOOKEFFECT._serialized_end=45261
  _AMULETACTIVITYHOOKEFFECT_AMULETACTIVITYADDHOOKEFFECT._serialized_start=45207
  _AMULETACTIVITYHOOKEFFECT_AMULETACTIVITYADDHOOKEFFECT._serialized_end=45261
  _AMULETHULEINFO._serialized_start=45263
  _AMULETHULEINFO._serialized_end=45368
  _AMULETHULEOPERATERESULT._serialized_start=45371
  _AMULETHULEOPERATERESULT._serialized_end=45522
  _AMULETGANGOPERATERESULT._serialized_start=45524
  _AMULETGANGOPERATERESULT._serialized_end=45618
  _AMULETDEALTILERESULT._serialized_start=45620
  _AMULETDEALTILERESULT._serialized_end=45707
  _AMULETDISCARDTILERESULT._serialized_start=45709
  _AMULETDISCARDTILERESULT._serialized_end=45799
  _AMULETSTARTGAMERESULT._serialized_start=45801
  _AMULETSTARTGAMERESULT._serialized_end=45875
  _AMULETROUNDRESULT._serialized_start=45878
  _AMULETROUNDRESULT._serialized_end=46045
  _AMULETUPGRADERESULT._serialized_start=46048
  _AMULETUPGRADERESULT._serialized_end=46265
  _QUESTIONNAIREREWARD._serialized_start=46267
  _QUESTIONNAIREREWARD._serialized_end=46324
  _QUESTIONNAIREDETAIL._serialized_start=46327
  _QUESTIONNAIREDETAIL._serialized_end=46655
  _QUESTIONNAIREQUESTION._serialized_start=46658
  _QUESTIONNAIREQUESTION._serialized_end=47390
  _QUESTIONNAIREQUESTION_QUESTIONOPTION._serialized_start=47005
  _QUESTIONNAIREQUESTION_QUESTIONOPTION._serialized_end=47072
  _QUESTIONNAIREQUESTION_NEXTQUESTIONDATA._serialized_start=47075
  _QUESTIONNAIREQUESTION_NEXTQUESTIONDATA._serialized_end=47390
  _QUESTIONNAIREQUESTION_NEXTQUESTIONDATA_QUESTIONCONDITION._serialized_start=47212
  _QUESTIONNAIREQUESTION_NEXTQUESTIONDATA_QUESTIONCONDITION._serialized_end=47280
  _QUESTIONNAIREQUESTION_NEXTQUESTIONDATA_QUESTIONCONDITIONWRAPPER._serialized_start=47282
  _QUESTIONNAIREQUESTION_NEXTQUESTIONDATA_QUESTIONCONDITIONWRAPPER._serialized_end=47390
  _QUESTIONNAIREBRIEF._serialized_start=47393
  _QUESTIONNAIREBRIEF._serialized_end=47596
  _SEERREPORT._serialized_start=47598
  _SEERREPORT._serialized_end=47686
  _SEEREVENT._serialized_start=47688
  _SEEREVENT._serialized_end=47780
  _SEERRECOMMEND._serialized_start=47782
  _SEERRECOMMEND._serialized_end=47852
  _SEERPREDICTION._serialized_start=47854
  _SEERPREDICTION._serialized_end=47901
  _SEERROUND._serialized_start=47903
  _SEERROUND._serialized_end=47992
  _SEERSCORE._serialized_start=47994
  _SEERSCORE._serialized_end=48035
  _SEERBRIEF._serialized_start=48037
  _SEERBRIEF._serialized_end=48157
  _SIMULATIONV2SEASONDATA._serialized_start=48160
  _SIMULATIONV2SEASONDATA._serialized_end=48528
  _SIMULATIONV2PLAYERRECORD._serialized_start=48530
  _SIMULATIONV2PLAYERRECORD._serialized_end=48625
  _SIMULATIONV2MATCHRECORD._serialized_start=48627
  _SIMULATIONV2MATCHRECORD._serialized_end=48714
  _SIMULATIONV2EVENTHISTORY._serialized_start=48716
  _SIMULATIONV2EVENTHISTORY._serialized_end=48769
  _SIMULATIONV2EVENT._serialized_start=48772
  _SIMULATIONV2EVENT._serialized_end=49082
  _SIMULATIONV2EVENT_SIMULATIONV2EVENTSELECTION._serialized_start=48896
  _SIMULATIONV2EVENT_SIMULATIONV2EVENTSELECTION._serialized_end=49082
  _SIMULATIONV2EVENT_SIMULATIONV2EVENTSELECTION_SIMULATIONV2EVENTRESULT._serialized_start=49029
  _SIMULATIONV2EVENT_SIMULATIONV2EVENTSELECTION_SIMULATIONV2EVENTRESULT._serialized_end=49082
  _SIMULATIONV2ABILITY._serialized_start=49084
  _SIMULATIONV2ABILITY._serialized_end=49170
  _SIMULATIONV2BUFF._serialized_start=49172
  _SIMULATIONV2BUFF._serialized_end=49232
  _SIMULATIONV2EFFECT._serialized_start=49234
  _SIMULATIONV2EFFECT._serialized_end=49266
  _SIMULATIONV2MATCHINFO._serialized_start=49268
  _SIMULATIONV2MATCHINFO._serialized_end=49361
  _SIMULATIONV2RECORD._serialized_start=49363
  _SIMULATIONV2RECORD._serialized_end=49477
  _SIMULATIONV2MATCHHISTORY._serialized_start=49480
  _SIMULATIONV2MATCHHISTORY._serialized_end=50478
  _SIMULATIONV2MATCHHISTORY_ROUNDSTARTARGS._serialized_start=50023
  _SIMULATIONV2MATCHHISTORY_ROUNDSTARTARGS._serialized_end=50138
  _SIMULATIONV2MATCHHISTORY_RIICHIARGS._serialized_start=50140
  _SIMULATIONV2MATCHHISTORY_RIICHIARGS._serialized_end=50166
  _SIMULATIONV2MATCHHISTORY_FULUARGS._serialized_start=50168
  _SIMULATIONV2MATCHHISTORY_FULUARGS._serialized_end=50220
  _SIMULATIONV2MATCHHISTORY_HULEARGS._serialized_start=50222
  _SIMULATIONV2MATCHHISTORY_HULEARGS._serialized_end=50330
  _SIMULATIONV2MATCHHISTORY_PUSHTINGARGS._serialized_start=50332
  _SIMULATIONV2MATCHHISTORY_PUSHTINGARGS._serialized_end=50374
  _SIMULATIONV2MATCHHISTORY_FINDTINGARGS._serialized_start=50376
  _SIMULATIONV2MATCHHISTORY_FINDTINGARGS._serialized_end=50420
  _SIMULATIONV2MATCHHISTORY_LIUJUARGS._serialized_start=50422
  _SIMULATIONV2MATCHHISTORY_LIUJUARGS._serialized_end=50447
  _SIMULATIONV2MATCHHISTORY_STORYARGS._serialized_start=50449
  _SIMULATIONV2MATCHHISTORY_STORYARGS._serialized_end=50478
  _SIMULATIONV2MATCH._serialized_start=50481
  _SIMULATIONV2MATCH._serialized_end=51075
  _SIMULATIONV2MATCH_SIMULATIONV2PLAYER._serialized_start=50874
  _SIMULATIONV2MATCH_SIMULATIONV2PLAYER._serialized_end=51075
  _SIMULATIONACTIONDATA._serialized_start=51078
  _SIMULATIONACTIONDATA._serialized_end=51764
  _SIMULATIONACTIONDATA_ACTIONRIICHIDATA._serialized_start=51415
  _SIMULATIONACTIONDATA_ACTIONRIICHIDATA._serialized_end=51447
  _SIMULATIONACTIONDATA_ACTIONHULEDATA._serialized_start=51450
  _SIMULATIONACTIONDATA_ACTIONHULEDATA._serialized_end=51645
  _SIMULATIONACTIONDATA_ACTIONHULEDATA_HULEINFO._serialized_start=51532
  _SIMULATIONACTIONDATA_ACTIONHULEDATA_HULEINFO._serialized_end=51645
  _SIMULATIONACTIONDATA_ACTIONFULUDATA._serialized_start=51647
  _SIMULATIONACTIONDATA_ACTIONFULUDATA._serialized_end=51677
  _SIMULATIONACTIONDATA_ACTIONDISCARDDATA._serialized_start=51679
  _SIMULATIONACTIONDATA_ACTIONDISCARDDATA._serialized_end=51728
  _SIMULATIONACTIONDATA_ACTIONDEALTILEDATA._serialized_start=51730
  _SIMULATIONACTIONDATA_ACTIONDEALTILEDATA._serialized_end=51764
  _RESCONNECTIONINFO._serialized_start=51766
  _RESCONNECTIONINFO._serialized_end=51857
  _RESFETCHQUEUEINFO._serialized_start=51859
  _RESFETCHQUEUEINFO._serialized_end=51934
  _REQOPENIDCHECK._serialized_start=51936
  _REQOPENIDCHECK._serialized_end=51981
  _REQSIGNUPACCOUNT._serialized_start=51984
  _REQSIGNUPACCOUNT._serialized_end=52147
  _RESSIGNUPACCOUNT._serialized_start=52149
  _RESSIGNUPACCOUNT._serialized_end=52193
  _REQLOGIN._serialized_start=52196
  _REQLOGIN._serialized_end=52494
  _RESLOGIN._serialized_start=52497
  _RESLOGIN._serialized_end=52794
  _REQPREPARELOGIN._serialized_start=52796
  _REQPREPARELOGIN._serialized_end=52849
  _RESFASTLOGIN._serialized_start=52851
  _RESFASTLOGIN._serialized_end=52955
  _REQEMAILLOGIN._serialized_start=52958
  _REQEMAILLOGIN._serialized_end=53161
  _REQBINDACCOUNT._serialized_start=53163
  _REQBINDACCOUNT._serialized_end=53214
  _REQCREATEPHONEVERIFYCODE._serialized_start=53216
  _REQCREATEPHONEVERIFYCODE._serialized_end=53272
  _REQCREATEEMAILVERIFYCODE._serialized_start=53274
  _REQCREATEEMAILVERIFYCODE._serialized_end=53330
  _REQVERIFYCODEFORSECURE._serialized_start=53332
  _REQVERIFYCODEFORSECURE._serialized_end=53389
  _RESVERFIYCODEFORSECURE._serialized_start=53391
  _RESVERFIYCODEFORSECURE._serialized_end=53463
  _REQBINDPHONENUMBER._serialized_start=53465
  _REQBINDPHONENUMBER._serialized_end=53560
  _REQUNBINDPHONENUMBER._serialized_start=53562
  _REQUNBINDPHONENUMBER._serialized_end=53631
  _RESFETCHPHONELOGINBIND._serialized_start=53633
  _RESFETCHPHONELOGINBIND._serialized_end=53704
  _REQCREATEPHONELOGINBIND._serialized_start=53706
  _REQCREATEPHONELOGINBIND._serialized_end=53749
  _REQBINDEMAIL._serialized_start=53751
  _REQBINDEMAIL._serialized_end=53812
  _REQMODIFYPASSWORD._serialized_start=53814
  _REQMODIFYPASSWORD._serialized_end=53899
  _REQOAUTH2AUTH._serialized_start=53901
  _REQOAUTH2AUTH._serialized_end=53988
  _RESOAUTH2AUTH._serialized_start=53990
  _RESOAUTH2AUTH._serialized_end=54053
  _REQOAUTH2CHECK._serialized_start=54055
  _REQOAUTH2CHECK._serialized_end=54107
  _RESOAUTH2CHECK._serialized_start=54109
  _RESOAUTH2CHECK._serialized_end=54172
  _REQOAUTH2SIGNUP._serialized_start=54175
  _REQOAUTH2SIGNUP._serialized_end=54395
  _RESOAUTH2SIGNUP._serialized_start=54397
  _RESOAUTH2SIGNUP._serialized_end=54440
  _REQOAUTH2LOGIN._serialized_start=54443
  _REQOAUTH2LOGIN._serialized_end=54734
  _REQDMMPRELOGIN._serialized_start=54736
  _REQDMMPRELOGIN._serialized_end=54772
  _RESDMMPRELOGIN._serialized_start=54774
  _RESDMMPRELOGIN._serialized_end=54835
  _REQLOGOUT._serialized_start=54837
  _REQLOGOUT._serialized_end=54848
  _RESLOGOUT._serialized_start=54850
  _RESLOGOUT._serialized_end=54887
  _REQHEATBEAT._serialized_start=54889
  _REQHEATBEAT._serialized_end=54932
  _REQSEARCHACCOUNTBYEIDLOBBY._serialized_start=54934
  _REQSEARCHACCOUNTBYEIDLOBBY._serialized_end=54975
  _RESSEARCHACCOUNTBYEIDLOBBY._serialized_start=54977
  _RESSEARCHACCOUNTBYEIDLOBBY._serialized_end=55051
  _REQLOGINBEAT._serialized_start=55053
  _REQLOGINBEAT._serialized_end=55085
  _REQJOINMATCHQUEUE._serialized_start=55087
  _REQJOINMATCHQUEUE._serialized_end=55157
  _REQCANCELMATCHQUEUE._serialized_start=55159
  _REQCANCELMATCHQUEUE._serialized_end=55200
  _REQACCOUNTINFO._serialized_start=55202
  _REQACCOUNTINFO._serialized_end=55238
  _RESACCOUNTINFO._serialized_start=55240
  _RESACCOUNTINFO._serialized_end=55336
  _REQCREATENICKNAME._serialized_start=55338
  _REQCREATENICKNAME._serialized_end=55411
  _REQMODIFYNICKNAME._serialized_start=55413
  _REQMODIFYNICKNAME._serialized_end=55471
  _REQMODIFYBIRTHDAY._serialized_start=55473
  _REQMODIFYBIRTHDAY._serialized_end=55510
  _RESSELFROOM._serialized_start=55512
  _RESSELFROOM._serialized_end=55575
  _RESFETCHGAMINGINFO._serialized_start=55577
  _RESFETCHGAMINGINFO._serialized_end=55663
  _REQCREATEROOM._serialized_start=55666
  _REQCREATEROOM._serialized_end=55801
  _RESCREATEROOM._serialized_start=55803
  _RESCREATEROOM._serialized_end=55868
  _REQJOINROOM._serialized_start=55870
  _REQJOINROOM._serialized_end=55931
  _RESJOINROOM._serialized_start=55933
  _RESJOINROOM._serialized_end=55996
  _REQROOMREADY._serialized_start=55998
  _REQROOMREADY._serialized_end=56027
  _REQROOMDRESSING._serialized_start=56029
  _REQROOMDRESSING._serialized_end=56064
  _REQROOMSTART._serialized_start=56066
  _REQROOMSTART._serialized_end=56080
  _REQROOMKICKPLAYER._serialized_start=56082
  _REQROOMKICKPLAYER._serialized_end=56113
  _REQMODIFYROOM._serialized_start=56115
  _REQMODIFYROOM._serialized_end=56151
  _REQADDROOMROBOT._serialized_start=56153
  _REQADDROOMROBOT._serialized_end=56188
  _REQCHANGEAVATAR._serialized_start=56190
  _REQCHANGEAVATAR._serialized_end=56226
  _REQACCOUNTSTATISTICINFO._serialized_start=56228
  _REQACCOUNTSTATISTICINFO._serialized_end=56273
  _RESACCOUNTSTATISTICINFO._serialized_start=56276
  _RESACCOUNTSTATISTICINFO._serialized_end=56428
  _RESACCOUNTCHALLENGERANKINFO._serialized_start=56431
  _RESACCOUNTCHALLENGERANKINFO._serialized_end=56616
  _RESACCOUNTCHALLENGERANKINFO_CHALLENGERANK._serialized_start=56556
  _RESACCOUNTCHALLENGERANKINFO_CHALLENGERANK._serialized_end=56616
  _RESACCOUNTCHARACTERINFO._serialized_start=56618
  _RESACCOUNTCHARACTERINFO._serialized_end=56690
  _REQSHOPPURCHASE._serialized_start=56692
  _REQSHOPPURCHASE._serialized_end=56735
  _RESSHOPPURCHASE._serialized_start=56737
  _RESSHOPPURCHASE._serialized_end=56815
  _REQGAMERECORD._serialized_start=56817
  _REQGAMERECORD._serialized_end=56882
  _RESGAMERECORD._serialized_start=56884
  _RESGAMERECORD._serialized_end=56987
  _REQGAMERECORDLIST._serialized_start=56989
  _REQGAMERECORDLIST._serialized_end=57052
  _RESGAMERECORDLIST._serialized_start=57054
  _RESGAMERECORDLIST._serialized_end=57157
  _REQGAMERECORDLISTV2._serialized_start=57160
  _REQGAMERECORDLISTV2._serialized_end=57303
  _RESGAMERECORDLISTV2._serialized_start=57306
  _RESGAMERECORDLISTV2._serialized_end=57448
  _REQNEXTGAMERECORDLIST._serialized_start=57450
  _REQNEXTGAMERECORDLIST._serialized_end=57506
  _RESNEXTGAMERECORDLIST._serialized_start=57509
  _RESNEXTGAMERECORDLIST._serialized_end=57658
  _RESCOLLECTEDGAMERECORDLIST._serialized_start=57661
  _RESCOLLECTEDGAMERECORDLIST._serialized_end=57791
  _REQGAMERECORDSDETAIL._serialized_start=57793
  _REQGAMERECORDSDETAIL._serialized_end=57834
  _RESGAMERECORDSDETAIL._serialized_start=57836
  _RESGAMERECORDSDETAIL._serialized_end=57921
  _REQGAMERECORDSDETAILV2._serialized_start=57923
  _REQGAMERECORDSDETAILV2._serialized_end=57966
  _RESGAMERECORDSDETAILV2._serialized_start=57968
  _RESGAMERECORDSDETAILV2._serialized_end=58056
  _REQADDCOLLECTEDGAMERECORD._serialized_start=58058
  _REQADDCOLLECTEDGAMERECORD._serialized_end=58154
  _RESADDCOLLECTEDGAMERECORD._serialized_start=58156
  _RESADDCOLLECTEDGAMERECORD._serialized_end=58209
  _REQREMOVECOLLECTEDGAMERECORD._serialized_start=58211
  _REQREMOVECOLLECTEDGAMERECORD._serialized_end=58255
  _RESREMOVECOLLECTEDGAMERECORD._serialized_start=58257
  _RESREMOVECOLLECTEDGAMERECORD._serialized_end=58313
  _REQCHANGECOLLECTEDGAMERECORDREMARKS._serialized_start=58315
  _REQCHANGECOLLECTEDGAMERECORDREMARKS._serialized_end=58383
  _RESCHANGECOLLECTEDGAMERECORDREMARKS._serialized_start=58385
  _RESCHANGECOLLECTEDGAMERECORDREMARKS._serialized_end=58448
  _REQLEVELLEADERBOARD._serialized_start=58450
  _REQLEVELLEADERBOARD._serialized_end=58485
  _RESLEVELLEADERBOARD._serialized_start=58488
  _RESLEVELLEADERBOARD._serialized_end=58660
  _RESLEVELLEADERBOARD_ITEM._serialized_start=58601
  _RESLEVELLEADERBOARD_ITEM._serialized_end=58660
  _REQCHALLANGELEADERBOARD._serialized_start=58662
  _REQCHALLANGELEADERBOARD._serialized_end=58703
  _RESCHALLENGELEADERBOARD._serialized_start=58706
  _RESCHALLENGELEADERBOARD._serialized_end=58886
  _RESCHALLENGELEADERBOARD_ITEM._serialized_start=58827
  _RESCHALLENGELEADERBOARD_ITEM._serialized_end=58886
  _REQMUTICHALLENGELEVEL._serialized_start=58888
  _REQMUTICHALLENGELEVEL._serialized_end=58952
  _RESMUTICHALLENGELEVEL._serialized_start=58955
  _RESMUTICHALLENGELEVEL._serialized_end=59094
  _RESMUTICHALLENGELEVEL_ITEM._serialized_start=58827
  _RESMUTICHALLENGELEVEL_ITEM._serialized_end=58868
  _REQMULTIACCOUNTID._serialized_start=59096
  _REQMULTIACCOUNTID._serialized_end=59140
  _RESMULTIACCOUNTBRIEF._serialized_start=59142
  _RESMULTIACCOUNTBRIEF._serialized_end=59227
  _RESFRIENDLIST._serialized_start=59229
  _RESFRIENDLIST._serialized_end=59347
  _RESFRIENDAPPLYLIST._serialized_start=59350
  _RESFRIENDAPPLYLIST._serialized_end=59504
  _RESFRIENDAPPLYLIST_FRIENDAPPLY._serialized_start=59451
  _RESFRIENDAPPLYLIST_FRIENDAPPLY._serialized_end=59504
  _REQAPPLYFRIEND._serialized_start=59506
  _REQAPPLYFRIEND._serialized_end=59541
  _REQHANDLEFRIENDAPPLY._serialized_start=59543
  _REQHANDLEFRIENDAPPLY._serialized_end=59600
  _REQREMOVEFRIEND._serialized_start=59602
  _REQREMOVEFRIEND._serialized_end=59638
  _REQSEARCHACCOUNTBYPATTERN._serialized_start=59640
  _REQSEARCHACCOUNTBYPATTERN._serialized_end=59705
  _RESSEARCHACCOUNTBYPATTERN._serialized_start=59707
  _RESSEARCHACCOUNTBYPATTERN._serialized_end=59824
  _REQACCOUNTLIST._serialized_start=59826
  _REQACCOUNTLIST._serialized_end=59867
  _RESACCOUNTSTATES._serialized_start=59869
  _RESACCOUNTSTATES._serialized_end=59953
  _REQSEARCHACCOUNTBYID._serialized_start=59955
  _REQSEARCHACCOUNTBYID._serialized_end=59997
  _RESSEARCHACCOUNTBYID._serialized_start=59999
  _RESSEARCHACCOUNTBYID._serialized_end=60083
  _RESBAGINFO._serialized_start=60085
  _RESBAGINFO._serialized_end=60145
  _REQUSEBAGITEM._serialized_start=60147
  _REQUSEBAGITEM._serialized_end=60179
  _REQOPENMANUALITEM._serialized_start=60181
  _REQOPENMANUALITEM._serialized_end=60251
  _REQOPENRANDOMREWARDITEM._serialized_start=60253
  _REQOPENRANDOMREWARDITEM._serialized_end=60310
  _RESOPENRANDOMREWARDITEM._serialized_start=60312
  _RESOPENRANDOMREWARDITEM._serialized_end=60396
  _REQOPENALLREWARDITEM._serialized_start=60398
  _REQOPENALLREWARDITEM._serialized_end=60437
  _RESOPENALLREWARDITEM._serialized_start=60439
  _RESOPENALLREWARDITEM._serialized_end=60520
  _REQCOMPOSESHARD._serialized_start=60522
  _REQCOMPOSESHARD._serialized_end=60556
  _REQFETCHANNOUNCEMENT._serialized_start=60558
  _REQFETCHANNOUNCEMENT._serialized_end=60612
  _RESANNOUNCEMENT._serialized_start=60614
  _RESANNOUNCEMENT._serialized_end=60731
  _RESMAILINFO._serialized_start=60733
  _RESMAILINFO._serialized_end=60797
  _REQREADMAIL._serialized_start=60799
  _REQREADMAIL._serialized_end=60829
  _REQDELETEMAIL._serialized_start=60831
  _REQDELETEMAIL._serialized_end=60863
  _REQTAKEATTACHMENT._serialized_start=60865
  _REQTAKEATTACHMENT._serialized_end=60901
  _REQRECEIVEACHIEVEMENTGROUPREWARD._serialized_start=60903
  _REQRECEIVEACHIEVEMENTGROUPREWARD._serialized_end=60955
  _RESRECEIVEACHIEVEMENTGROUPREWARD._serialized_start=60957
  _RESRECEIVEACHIEVEMENTGROUPREWARD._serialized_end=61060
  _REQRECEIVEACHIEVEMENTREWARD._serialized_start=61062
  _REQRECEIVEACHIEVEMENTREWARD._serialized_end=61115
  _RESRECEIVEACHIEVEMENTREWARD._serialized_start=61117
  _RESRECEIVEACHIEVEMENTREWARD._serialized_end=61215
  _RESFETCHACHIEVEMENTRATE._serialized_start=61218
  _RESFETCHACHIEVEMENTRATE._serialized_end=61373
  _RESFETCHACHIEVEMENTRATE_ACHIEVEMENTRATE._serialized_start=61330
  _RESFETCHACHIEVEMENTRATE_ACHIEVEMENTRATE._serialized_end=61373
  _RESACHIEVEMENT._serialized_start=61375
  _RESACHIEVEMENT._serialized_end=61486
  _RESTITLELIST._serialized_start=61488
  _RESTITLELIST._serialized_end=61548
  _REQUSETITLE._serialized_start=61550
  _REQUSETITLE._serialized_end=61578
  _REQBUYSHILIAN._serialized_start=61580
  _REQBUYSHILIAN._serialized_end=61609
  _REQUPDATECLIENTVALUE._serialized_start=61611
  _REQUPDATECLIENTVALUE._serialized_end=61661
  _RESCLIENTVALUE._serialized_start=61664
  _RESCLIENTVALUE._serialized_end=61809
  _RESCLIENTVALUE_VALUE._serialized_start=61774
  _RESCLIENTVALUE_VALUE._serialized_end=61809
  _REQCLIENTMESSAGE._serialized_start=61811
  _REQCLIENTMESSAGE._serialized_end=61865
  _REQCURRENTMATCHINFO._serialized_start=61867
  _REQCURRENTMATCHINFO._serialized_end=61907
  _RESCURRENTMATCHINFO._serialized_start=61910
  _RESCURRENTMATCHINFO._serialized_end=62076
  _RESCURRENTMATCHINFO_CURRENTMATCHINFO._serialized_start=62018
  _RESCURRENTMATCHINFO_CURRENTMATCHINFO._serialized_end=62076
  _REQUSERCOMPLAIN._serialized_start=62079
  _REQUSERCOMPLAIN._serialized_end=62304
  _REQUSERCOMPLAIN_GAMEROUNDINFO._serialized_start=62222
  _REQUSERCOMPLAIN_GAMEROUNDINFO._serialized_end=62304
  _REQREADANNOUNCEMENT._serialized_start=62306
  _REQREADANNOUNCEMENT._serialized_end=62379
  _RESREVIVECOININFO._serialized_start=62381
  _RESREVIVECOININFO._serialized_end=62446
  _RESDAILYTASK._serialized_start=62449
  _RESDAILYTASK._serialized_end=62607
  _REQREFRESHDAILYTASK._serialized_start=62609
  _REQREFRESHDAILYTASK._serialized_end=62647
  _RESREFRESHDAILYTASK._serialized_start=62649
  _RESREFRESHDAILYTASK._serialized_end=62755
  _REQUSEGIFTCODE._serialized_start=62757
  _REQUSEGIFTCODE._serialized_end=62787
  _RESUSEGIFTCODE._serialized_start=62789
  _RESUSEGIFTCODE._serialized_end=62864
  _RESUSESPECIALGIFTCODE._serialized_start=62866
  _RESUSESPECIALGIFTCODE._serialized_end=62951
  _REQSENDCLIENTMESSAGE._serialized_start=62953
  _REQSENDCLIENTMESSAGE._serialized_end=63025
  _REQGAMELIVEINFO._serialized_start=63027
  _REQGAMELIVEINFO._serialized_end=63063
  _RESGAMELIVEINFO._serialized_start=63066
  _RESGAMELIVEINFO._serialized_end=63241
  _REQGAMELIVELEFTSEGMENT._serialized_start=63243
  _REQGAMELIVELEFTSEGMENT._serialized_end=63311
  _RESGAMELIVELEFTSEGMENT._serialized_start=63314
  _RESGAMELIVELEFTSEGMENT._serialized_end=63484
  _REQGAMELIVELIST._serialized_start=63486
  _REQGAMELIVELIST._serialized_end=63522
  _RESGAMELIVELIST._serialized_start=63524
  _RESGAMELIVELIST._serialized_end=63604
  _RESCOMMENTSETTING._serialized_start=63606
  _RESCOMMENTSETTING._serialized_end=63674
  _REQUPDATECOMMENTSETTING._serialized_start=63676
  _REQUPDATECOMMENTSETTING._serialized_end=63724
  _REQFETCHCOMMENTLIST._serialized_start=63726
  _REQFETCHCOMMENTLIST._serialized_end=63766
  _RESFETCHCOMMENTLIST._serialized_start=63768
  _RESFETCHCOMMENTLIST._serialized_end=63885
  _REQFETCHCOMMENTCONTENT._serialized_start=63887
  _REQFETCHCOMMENTCONTENT._serialized_end=63955
  _RESFETCHCOMMENTCONTENT._serialized_start=63957
  _RESFETCHCOMMENTCONTENT._serialized_end=64042
  _REQLEAVECOMMENT._serialized_start=64044
  _REQLEAVECOMMENT._serialized_end=64097
  _REQDELETECOMMENT._serialized_start=64099
  _REQDELETECOMMENT._serialized_end=64157
  _REQUPDATEREADCOMMENT._serialized_start=64159
  _REQUPDATEREADCOMMENT._serialized_end=64198
  _RESFETCHROLLINGNOTICE._serialized_start=64200
  _RESFETCHROLLINGNOTICE._serialized_end=64284
  _RESFETCHMAINTAINNOTICE._serialized_start=64286
  _RESFETCHMAINTAINNOTICE._serialized_end=64372
  _REQFETCHROLLINGNOTICE._serialized_start=64374
  _REQFETCHROLLINGNOTICE._serialized_end=64411
  _RESSERVERTIME._serialized_start=64413
  _RESSERVERTIME._serialized_end=64475
  _REQPLATFORMBILLINGPRODUCTS._serialized_start=64477
  _REQPLATFORMBILLINGPRODUCTS._serialized_end=64525
  _RESPLATFORMBILLINGPRODUCTS._serialized_start=64527
  _RESPLATFORMBILLINGPRODUCTS._serialized_end=64619
  _REQCREATEBILLINGORDER._serialized_start=64622
  _REQCREATEBILLINGORDER._serialized_end=64761
  _RESCREATEBILLINGORDER._serialized_start=64763
  _RESCREATEBILLINGORDER._serialized_end=64830
  _REQSOLVEGOOGLEPLAYORDER._serialized_start=64832
  _REQSOLVEGOOGLEPLAYORDER._serialized_end=64916
  _REQSOLVEGOOGLEPLAYORDERV3._serialized_start=64918
  _REQSOLVEGOOGLEPLAYORDERV3._serialized_end=65022
  _REQCANCELGOOGLEPLAYORDER._serialized_start=65024
  _REQCANCELGOOGLEPLAYORDER._serialized_end=65068
  _REQCREATEWECHATNATIVEORDER._serialized_start=65071
  _REQCREATEWECHATNATIVEORDER._serialized_end=65209
  _RESCREATEWECHATNATIVEORDER._serialized_start=65211
  _RESCREATEWECHATNATIVEORDER._serialized_end=65306
  _REQCREATEWECHATAPPORDER._serialized_start=65309
  _REQCREATEWECHATAPPORDER._serialized_end=65444
  _RESCREATEWECHATAPPORDER._serialized_start=65447
  _RESCREATEWECHATAPPORDER._serialized_end=65720
  _RESCREATEWECHATAPPORDER_CALLWECHATAPPPARAM._serialized_start=65580
  _RESCREATEWECHATAPPORDER_CALLWECHATAPPPARAM._serialized_end=65720
  _REQCREATEALIPAYORDER._serialized_start=65723
  _REQCREATEALIPAYORDER._serialized_end=65882
  _RESCREATEALIPAYORDER._serialized_start=65884
  _RESCREATEALIPAYORDER._serialized_end=65952
  _REQCREATEALIPAYSCANORDER._serialized_start=65954
  _REQCREATEALIPAYSCANORDER._serialized_end=66070
  _RESCREATEALIPAYSCANORDER._serialized_start=66072
  _RESCREATEALIPAYSCANORDER._serialized_end=66182
  _REQCREATEALIPAYAPPORDER._serialized_start=66184
  _REQCREATEALIPAYAPPORDER._serialized_end=66299
  _RESCREATEALIPAYAPPORDER._serialized_start=66301
  _RESCREATEALIPAYAPPORDER._serialized_end=66372
  _REQCREATEJPCREDITCARDORDER._serialized_start=66375
  _REQCREATEJPCREDITCARDORDER._serialized_end=66535
  _RESCREATEJPCREDITCARDORDER._serialized_start=66537
  _RESCREATEJPCREDITCARDORDER._serialized_end=66609
  _REQCREATEJPPAYPALORDER._serialized_start=66612
  _REQCREATEJPPAYPALORDER._serialized_end=66768
  _RESCREATEJPPAYPALORDER._serialized_start=66770
  _RESCREATEJPPAYPALORDER._serialized_end=66838
  _REQCREATEJPAUORDER._serialized_start=66841
  _REQCREATEJPAUORDER._serialized_end=66993
  _RESCREATEJPAUORDER._serialized_start=66995
  _RESCREATEJPAUORDER._serialized_end=67059
  _REQCREATEJPDOCOMOORDER._serialized_start=67062
  _REQCREATEJPDOCOMOORDER._serialized_end=67218
  _RESCREATEJPDOCOMOORDER._serialized_start=67220
  _RESCREATEJPDOCOMOORDER._serialized_end=67288
  _REQCREATEJPWEBMONEYORDER._serialized_start=67291
  _REQCREATEJPWEBMONEYORDER._serialized_end=67449
  _RESCREATEJPWEBMONEYORDER._serialized_start=67451
  _RESCREATEJPWEBMONEYORDER._serialized_end=67521
  _REQCREATEJPSOFTBANKORDER._serialized_start=67524
  _REQCREATEJPSOFTBANKORDER._serialized_end=67682
  _RESCREATEJPSOFTBANKORDER._serialized_start=67684
  _RESCREATEJPSOFTBANKORDER._serialized_end=67754
  _REQCREATEJPPAYPAYORDER._serialized_start=67757
  _REQCREATEJPPAYPAYORDER._serialized_end=67913
  _RESCREATEJPPAYPAYORDER._serialized_start=67915
  _RESCREATEJPPAYPAYORDER._serialized_end=67983
  _REQFETCHJPCOMMONCREDITCARDORDER._serialized_start=67985
  _REQFETCHJPCOMMONCREDITCARDORDER._serialized_end=68056
  _RESFETCHJPCOMMONCREDITCARDORDER._serialized_start=68058
  _RESFETCHJPCOMMONCREDITCARDORDER._serialized_end=68117
  _REQCREATEJPGMOORDER._serialized_start=68120
  _REQCREATEJPGMOORDER._serialized_end=68273
  _RESCREATEJPGMOORDER._serialized_start=68275
  _RESCREATEJPGMOORDER._serialized_end=68340
  _REQCREATEYOSTARORDER._serialized_start=68343
  _REQCREATEYOSTARORDER._serialized_end=68475
  _RESCREATEYOSTARORDER._serialized_start=68477
  _RESCREATEYOSTARORDER._serialized_end=68543
  _REQCREATEENPAYPALORDER._serialized_start=68546
  _REQCREATEENPAYPALORDER._serialized_end=68702
  _RESCREATEENPAYPALORDER._serialized_start=68704
  _RESCREATEENPAYPALORDER._serialized_end=68772
  _REQCREATEENJCBORDER._serialized_start=68775
  _REQCREATEENJCBORDER._serialized_end=68928
  _RESCREATEENJCBORDER._serialized_start=68930
  _RESCREATEENJCBORDER._serialized_end=68995
  _REQCREATEENMASTERCARDORDER._serialized_start=68998
  _REQCREATEENMASTERCARDORDER._serialized_end=69158
  _RESCREATEENMASTERCARDORDER._serialized_start=69160
  _RESCREATEENMASTERCARDORDER._serialized_end=69232
  _REQCREATEENVISAORDER._serialized_start=69235
  _REQCREATEENVISAORDER._serialized_end=69389
  _RESCREATEENVISAORDER._serialized_start=69391
  _RESCREATEENVISAORDER._serialized_end=69457
  _REQCREATEENALIPAYORDER._serialized_start=69460
  _REQCREATEENALIPAYORDER._serialized_end=69616
  _RESCREATEENALIPAYORDER._serialized_start=69618
  _RESCREATEENALIPAYORDER._serialized_end=69686
  _REQCREATEKRPAYPALORDER._serialized_start=69689
  _REQCREATEKRPAYPALORDER._serialized_end=69845
  _RESCREATEKRPAYPALORDER._serialized_start=69847
  _RESCREATEKRPAYPALORDER._serialized_end=69915
  _REQCREATEKRJCBORDER._serialized_start=69918
  _REQCREATEKRJCBORDER._serialized_end=70071
  _RESCREATEKRJCBORDER._serialized_start=70073
  _RESCREATEKRJCBORDER._serialized_end=70138
  _REQCREATEKRMASTERCARDORDER._serialized_start=70141
  _REQCREATEKRMASTERCARDORDER._serialized_end=70301
  _RESCREATEKRMASTERCARDORDER._serialized_start=70303
  _RESCREATEKRMASTERCARDORDER._serialized_end=70375
  _REQCREATEKRVISAORDER._serialized_start=70378
  _REQCREATEKRVISAORDER._serialized_end=70532
  _RESCREATEKRVISAORDER._serialized_start=70534
  _RESCREATEKRVISAORDER._serialized_end=70600
  _REQCREATEKRALIPAYORDER._serialized_start=70603
  _REQCREATEKRALIPAYORDER._serialized_end=70759
  _RESCREATEKRALIPAYORDER._serialized_start=70761
  _RESCREATEKRALIPAYORDER._serialized_end=70829
  _REQCREATEDMMORDER._serialized_start=70831
  _REQCREATEDMMORDER._serialized_end=70940
  _RESCREATEDMMORDER._serialized_start=70943
  _RESCREATEDMMORDER._serialized_end=71130
  _REQCREATEIAPORDER._serialized_start=71133
  _REQCREATEIAPORDER._serialized_end=71287
  _RESCREATEIAPORDER._serialized_start=71289
  _RESCREATEIAPORDER._serialized_end=71352
  _REQVERIFICATIONIAPORDER._serialized_start=71354
  _REQVERIFICATIONIAPORDER._serialized_end=71463
  _RESVERIFICATIONIAPORDER._serialized_start=71465
  _RESVERIFICATIONIAPORDER._serialized_end=71516
  _REQCREATESTEAMORDER._serialized_start=71519
  _REQCREATESTEAMORDER._serialized_end=71689
  _RESCREATESTEAMORDER._serialized_start=71691
  _RESCREATESTEAMORDER._serialized_end=71783
  _RESRANDOMCHARACTER._serialized_start=71785
  _RESRANDOMCHARACTER._serialized_end=71883
  _REQRANDOMCHARACTER._serialized_start=71885
  _REQRANDOMCHARACTER._serialized_end=71957
  _REQVERIFYSTEAMORDER._serialized_start=71959
  _REQVERIFYSTEAMORDER._serialized_end=72018
  _REQCREATEMYCARDORDER._serialized_start=72021
  _REQCREATEMYCARDORDER._serialized_end=72156
  _RESCREATEMYCARDORDER._serialized_start=72158
  _RESCREATEMYCARDORDER._serialized_end=72243
  _REQVERIFYMYCARDORDER._serialized_start=72245
  _REQVERIFYMYCARDORDER._serialized_end=72305
  _REQCREATEPAYPALORDER._serialized_start=72308
  _REQCREATEPAYPALORDER._serialized_end=72443
  _RESCREATEPAYPALORDER._serialized_start=72445
  _RESCREATEPAYPALORDER._serialized_end=72524
  _REQCREATEXSOLLAORDER._serialized_start=72527
  _REQCREATEXSOLLAORDER._serialized_end=72706
  _RESCREATEXSOLLAORDER._serialized_start=72708
  _RESCREATEXSOLLAORDER._serialized_end=72787
  _REQDELIVERAA32ORDER._serialized_start=72789
  _REQDELIVERAA32ORDER._serialized_end=72865
  _REQOPENCHEST._serialized_start=72867
  _REQOPENCHEST._serialized_end=72965
  _RESOPENCHEST._serialized_start=72968
  _RESOPENCHEST._serialized_end=73206
  _RESOPENCHEST_CHESTREPLACECOUNTDATA._serialized_start=73156
  _RESOPENCHEST_CHESTREPLACECOUNTDATA._serialized_end=73206
  _REQBUYFROMCHESTSHOP._serialized_start=73208
  _REQBUYFROMCHESTSHOP._serialized_end=73262
  _RESBUYFROMCHESTSHOP._serialized_start=73264
  _RESBUYFROMCHESTSHOP._serialized_end=73373
  _RESDAILYSIGNININFO._serialized_start=73375
  _RESDAILYSIGNININFO._serialized_end=73443
  _REQDOACTIVITYSIGNIN._serialized_start=73445
  _REQDOACTIVITYSIGNIN._serialized_end=73487
  _RESDOACTIVITYSIGNIN._serialized_start=73490
  _RESDOACTIVITYSIGNIN._serialized_end=73663
  _RESDOACTIVITYSIGNIN_REWARDDATA._serialized_start=73615
  _RESDOACTIVITYSIGNIN_REWARDDATA._serialized_end=73663
  _RESCHARACTERINFO._serialized_start=73666
  _RESCHARACTERINFO._serialized_end=73970
  _REQUPDATECHARACTERSORT._serialized_start=73972
  _REQUPDATECHARACTERSORT._serialized_end=74057
  _REQCHANGEMAINCHARACTER._serialized_start=74059
  _REQCHANGEMAINCHARACTER._serialized_end=74105
  _REQCHANGECHARACTERSKIN._serialized_start=74107
  _REQCHANGECHARACTERSKIN._serialized_end=74167
  _REQCHANGECHARACTERVIEW._serialized_start=74169
  _REQCHANGECHARACTERVIEW._serialized_end=74246
  _REQSETHIDDENCHARACTER._serialized_start=74248
  _REQSETHIDDENCHARACTER._serialized_end=74291
  _RESSETHIDDENCHARACTER._serialized_start=74293
  _RESSETHIDDENCHARACTER._serialized_end=74369
  _REQSENDGIFTTOCHARACTER._serialized_start=74372
  _REQSENDGIFTTOCHARACTER._serialized_end=74506
  _REQSENDGIFTTOCHARACTER_GIFT._serialized_start=74468
  _REQSENDGIFTTOCHARACTER_GIFT._serialized_end=74506
  _RESSENDGIFTTOCHARACTER._serialized_start=74508
  _RESSENDGIFTTOCHARACTER._serialized_end=74586
  _REQSELLITEM._serialized_start=74588
  _REQSELLITEM._serialized_end=74678
  _REQSELLITEM_ITEM._serialized_start=74640
  _REQSELLITEM_ITEM._serialized_end=74678
  _RESCOMMONVIEW._serialized_start=74680
  _RESCOMMONVIEW._serialized_end=74797
  _RESCOMMONVIEW_SLOT._serialized_start=74762
  _RESCOMMONVIEW_SLOT._serialized_end=74797
  _REQCHANGECOMMONVIEW._serialized_start=74799
  _REQCHANGECOMMONVIEW._serialized_end=74849
  _REQSAVECOMMONVIEWS._serialized_start=74851
  _REQSAVECOMMONVIEWS._serialized_end=74950
  _REQCOMMONVIEWS._serialized_start=74952
  _REQCOMMONVIEWS._serialized_end=74983
  _RESCOMMONVIEWS._serialized_start=74985
  _RESCOMMONVIEWS._serialized_end=75070
  _RESALLCOMMONVIEWS._serialized_start=75073
  _RESALLCOMMONVIEWS._serialized_end=75243
  _RESALLCOMMONVIEWS_VIEWS._serialized_start=75177
  _RESALLCOMMONVIEWS_VIEWS._serialized_end=75243
  _REQUSECOMMONVIEW._serialized_start=75245
  _REQUSECOMMONVIEW._serialized_end=75278
  _REQUPGRADECHARACTER._serialized_start=75280
  _REQUPGRADECHARACTER._serialized_end=75323
  _RESUPGRADECHARACTER._serialized_start=75325
  _RESUPGRADECHARACTER._serialized_end=75406
  _REQFINISHEDENDING._serialized_start=75408
  _REQFINISHEDENDING._serialized_end=75486
  _REQGMCOMMAND._serialized_start=75488
  _REQGMCOMMAND._serialized_end=75519
  _RESSHOPINFO._serialized_start=75521
  _RESSHOPINFO._serialized_end=75593
  _REQBUYFROMSHOP._serialized_start=75596
  _REQBUYFROMSHOP._serialized_end=75768
  _REQBUYFROMSHOP_ITEM._serialized_start=75735
  _REQBUYFROMSHOP_ITEM._serialized_end=75768
  _RESBUYFROMSHOP._serialized_start=75770
  _RESBUYFROMSHOP._serialized_end=75845
  _REQBUYFROMZHP._serialized_start=75847
  _REQBUYFROMZHP._serialized_end=75895
  _REQPAYMONTHTICKET._serialized_start=75897
  _REQPAYMONTHTICKET._serialized_end=75935
  _RESPAYMONTHTICKET._serialized_start=75937
  _RESPAYMONTHTICKET._serialized_end=76027
  _REQRESHZHPSHOP._serialized_start=76029
  _REQRESHZHPSHOP._serialized_end=76089
  _RESREFRESHZHPSHOP._serialized_start=76091
  _RESREFRESHZHPSHOP._serialized_end=76162
  _RESMONTHTICKETINFO._serialized_start=76164
  _RESMONTHTICKETINFO._serialized_end=76258
  _REQEXCHANGECURRENCY._serialized_start=76260
  _REQEXCHANGECURRENCY._serialized_end=76308
  _RESSERVERSETTINGS._serialized_start=76310
  _RESSERVERSETTINGS._serialized_end=76393
  _RESACCOUNTSETTINGS._serialized_start=76395
  _RESACCOUNTSETTINGS._serialized_end=76479
  _REQUPDATEACCOUNTSETTINGS._serialized_start=76481
  _REQUPDATEACCOUNTSETTINGS._serialized_end=76544
  _RESMODNICKNAMETIME._serialized_start=76546
  _RESMODNICKNAMETIME._serialized_end=76615
  _RESMISC._serialized_start=76618
  _RESMISC._serialized_end=76857
  _RESMISC_MISCFAITHDATA._serialized_start=76809
  _RESMISC_MISCFAITHDATA._serialized_end=76857
  _REQMODIFYSIGNATURE._serialized_start=76859
  _REQMODIFYSIGNATURE._serialized_end=76898
  _RESIDCARDINFO._serialized_start=76900
  _RESIDCARDINFO._serialized_end=76977
  _REQUPDATEIDCARDINFO._serialized_start=76979
  _REQUPDATEIDCARDINFO._serialized_end=77035
  _RESVIPREWARD._serialized_start=77037
  _RESVIPREWARD._serialized_end=77104
  _RESFETCHREFUNDORDER._serialized_start=77107
  _RESFETCHREFUNDORDER._serialized_end=77351
  _RESFETCHREFUNDORDER_ORDERINFO._serialized_start=77265
  _RESFETCHREFUNDORDER_ORDERINFO._serialized_end=77351
  _REQGAINVIPREWARD._serialized_start=77353
  _REQGAINVIPREWARD._serialized_end=77390
  _REQFETCHCUSTOMIZEDCONTESTLIST._serialized_start=77392
  _REQFETCHCUSTOMIZEDCONTESTLIST._serialized_end=77467
  _RESFETCHCUSTOMIZEDCONTESTLIST._serialized_start=77470
  _RESFETCHCUSTOMIZEDCONTESTLIST._serialized_end=77624
  _REQFETCHCUSTOMIZEDCONTESTAUTHINFO._serialized_start=77626
  _REQFETCHCUSTOMIZEDCONTESTAUTHINFO._serialized_end=77680
  _RESFETCHCUSTOMIZEDCONTESTAUTHINFO._serialized_start=77682
  _RESFETCHCUSTOMIZEDCONTESTAUTHINFO._serialized_end=77767
  _REQENTERCUSTOMIZEDCONTEST._serialized_start=77769
  _REQENTERCUSTOMIZEDCONTEST._serialized_end=77829
  _RESENTERCUSTOMIZEDCONTEST._serialized_start=77832
  _RESENTERCUSTOMIZEDCONTEST._serialized_end=78047
  _REQFETCHCUSTOMIZEDCONTESTONLINEINFO._serialized_start=78049
  _REQFETCHCUSTOMIZEDCONTESTONLINEINFO._serialized_end=78105
  _RESFETCHCUSTOMIZEDCONTESTONLINEINFO._serialized_start=78107
  _RESFETCHCUSTOMIZEDCONTESTONLINEINFO._serialized_end=78193
  _REQFETCHCUSTOMIZEDCONTESTBYCONTESTID._serialized_start=78195
  _REQFETCHCUSTOMIZEDCONTESTBYCONTESTID._serialized_end=78267
  _RESFETCHCUSTOMIZEDCONTESTBYCONTESTID._serialized_start=78269
  _RESFETCHCUSTOMIZEDCONTESTBYCONTESTID._serialized_end=78386
  _REQSIGNUPCUSTOMIZEDCONTEST._serialized_start=78388
  _REQSIGNUPCUSTOMIZEDCONTEST._serialized_end=78466
  _RESSIGNUPCUSTOMIZEDCONTEST._serialized_start=78468
  _RESSIGNUPCUSTOMIZEDCONTEST._serialized_end=78537
  _REQSTARTCUSTOMIZEDCONTEST._serialized_start=78539
  _REQSTARTCUSTOMIZEDCONTEST._serialized_end=78616
  _REQSTOPCUSTOMIZEDCONTEST._serialized_start=78618
  _REQSTOPCUSTOMIZEDCONTEST._serialized_end=78663
  _REQJOINCUSTOMIZEDCONTESTCHATROOM._serialized_start=78665
  _REQJOINCUSTOMIZEDCONTESTCHATROOM._serialized_end=78718
  _RESJOINCUSTOMIZEDCONTESTCHATROOM._serialized_start=78720
  _RESJOINCUSTOMIZEDCONTESTCHATROOM._serialized_end=78795
  _REQSAYCHATMESSAGE._serialized_start=78797
  _REQSAYCHATMESSAGE._serialized_end=78852
  _REQFETCHCUSTOMIZEDCONTESTGAMELIVELIST._serialized_start=78854
  _REQFETCHCUSTOMIZEDCONTESTGAMELIVELIST._serialized_end=78912
  _RESFETCHCUSTOMIZEDCONTESTGAMELIVELIST._serialized_start=78914
  _RESFETCHCUSTOMIZEDCONTESTGAMELIVELIST._serialized_end=79016
  _REQFETCHCUSTOMIZEDCONTESTGAMERECORDS._serialized_start=79018
  _REQFETCHCUSTOMIZEDCONTESTGAMERECORDS._serialized_end=79114
  _RESFETCHCUSTOMIZEDCONTESTGAMERECORDS._serialized_start=79116
  _RESFETCHCUSTOMIZEDCONTESTGAMERECORDS._serialized_end=79237
  _REQTARGETCUSTOMIZEDCONTEST._serialized_start=79239
  _REQTARGETCUSTOMIZEDCONTEST._serialized_end=79286
  _RESACTIVITYLIST._serialized_start=79288
  _RESACTIVITYLIST._serialized_end=79365
  _RESACCOUNTACTIVITYDATA._serialized_start=79368
  _RESACCOUNTACTIVITYDATA._serialized_end=81316
  _RESACCOUNTACTIVITYDATA_ACTIVITYSIGNINDATA._serialized_start=80838
  _RESACCOUNTACTIVITYDATA_ACTIVITYSIGNINDATA._serialized_end=80929
  _RESACCOUNTACTIVITYDATA_BUFFDATA._serialized_start=80931
  _RESACCOUNTACTIVITYDATA_BUFFDATA._serialized_end=80987
  _RESACCOUNTACTIVITYDATA_ACTIVITYRICHMANDATA._serialized_start=80990
  _RESACCOUNTACTIVITYDATA_ACTIVITYRICHMANDATA._serialized_end=81181
  _RESACCOUNTACTIVITYDATA_CHESTUPDATA._serialized_start=81183
  _RESACCOUNTACTIVITYDATA_CHESTUPDATA._serialized_end=81223
  _RESACCOUNTACTIVITYDATA_ACTIVITYSNSDATA._serialized_start=81225
  _RESACCOUNTACTIVITYDATA_ACTIVITYSNSDATA._serialized_end=81316
  _SNSBLOG._serialized_start=81318
  _SNSBLOG._serialized_end=81358
  _SNSREPLY._serialized_start=81360
  _SNSREPLY._serialized_end=81402
  _REQEXCHANGEACTIVITYITEM._serialized_start=81404
  _REQEXCHANGEACTIVITYITEM._serialized_end=81465
  _RESEXCHANGEACTIVITYITEM._serialized_start=81467
  _RESEXCHANGEACTIVITYITEM._serialized_end=81561
  _REQCOMPLETEACTIVITYTASK._serialized_start=81563
  _REQCOMPLETEACTIVITYTASK._serialized_end=81605
  _REQCOMPLETEACTIVITYTASKBATCH._serialized_start=81607
  _REQCOMPLETEACTIVITYTASKBATCH._serialized_end=81656
  _REQCOMPLETEPERIODACTIVITYTASKBATCH._serialized_start=81658
  _REQCOMPLETEPERIODACTIVITYTASKBATCH._serialized_end=81713
  _REQRECEIVEACTIVITYFLIPTASK._serialized_start=81715
  _REQRECEIVEACTIVITYFLIPTASK._serialized_end=81760
  _RESRECEIVEACTIVITYFLIPTASK._serialized_start=81762
  _RESRECEIVEACTIVITYFLIPTASK._serialized_end=81831
  _REQCOMPLETESEGMENTTASKREWARD._serialized_start=81833
  _REQCOMPLETESEGMENTTASKREWARD._serialized_end=81895
  _RESCOMPLETESEGMENTTASKREWARD._serialized_start=81897
  _RESCOMPLETESEGMENTTASKREWARD._serialized_end=81989
  _REQFETCHACTIVITYFLIPINFO._serialized_start=81991
  _REQFETCHACTIVITYFLIPINFO._serialized_end=82038
  _RESFETCHACTIVITYFLIPINFO._serialized_start=82040
  _RESFETCHACTIVITYFLIPINFO._serialized_end=82124
  _REQGAINACCUMULATEDPOINTACTIVITYREWARD._serialized_start=82126
  _REQGAINACCUMULATEDPOINTACTIVITYREWARD._serialized_end=82205
  _REQGAINMULTIPOINTACTIVITYREWARD._serialized_start=82207
  _REQGAINMULTIPOINTACTIVITYREWARD._serialized_end=82285
  _REQFETCHRANKPOINTLEADERBOARD._serialized_start=82287
  _REQFETCHRANKPOINTLEADERBOARD._serialized_end=82341
  _RESFETCHRANKPOINTLEADERBOARD._serialized_start=82344
  _RESFETCHRANKPOINTLEADERBOARD._serialized_end=82572
  _RESFETCHRANKPOINTLEADERBOARD_ITEM._serialized_start=82483
  _RESFETCHRANKPOINTLEADERBOARD_ITEM._serialized_end=82572
  _REQGAINRANKPOINTREWARD._serialized_start=82574
  _REQGAINRANKPOINTREWARD._serialized_end=82643
  _REQRICHMANNEXTMOVE._serialized_start=82645
  _REQRICHMANNEXTMOVE._serialized_end=82686
  _RESRICHMANNEXTMOVE._serialized_start=82689
  _RESRICHMANNEXTMOVE._serialized_end=83221
  _RESRICHMANNEXTMOVE_REWARDDATA._serialized_start=82981
  _RESRICHMANNEXTMOVE_REWARDDATA._serialized_end=83065
  _RESRICHMANNEXTMOVE_PATHDATA._serialized_start=83067
  _RESRICHMANNEXTMOVE_PATHDATA._serialized_end=83163
  _RESRICHMANNEXTMOVE_BUFFDATA._serialized_start=80931
  _RESRICHMANNEXTMOVE_BUFFDATA._serialized_end=80987
  _REQRICHMANSPECIALMOVE._serialized_start=83223
  _REQRICHMANSPECIALMOVE._serialized_end=83281
  _REQRICHMANCHESTINFO._serialized_start=83283
  _REQRICHMANCHESTINFO._serialized_end=83325
  _RESRICHMANCHESTINFO._serialized_start=83328
  _RESRICHMANCHESTINFO._serialized_end=83463
  _RESRICHMANCHESTINFO_ITEMDATA._serialized_start=83426
  _RESRICHMANCHESTINFO_ITEMDATA._serialized_end=83463
  _REQCREATEGAMEOBSERVEAUTH._serialized_start=83465
  _REQCREATEGAMEOBSERVEAUTH._serialized_end=83510
  _RESCREATEGAMEOBSERVEAUTH._serialized_start=83512
  _RESCREATEGAMEOBSERVEAUTH._serialized_end=83597
  _REQREFRESHGAMEOBSERVEAUTH._serialized_start=83599
  _REQREFRESHGAMEOBSERVEAUTH._serialized_end=83641
  _RESREFRESHGAMEOBSERVEAUTH._serialized_start=83643
  _RESREFRESHGAMEOBSERVEAUTH._serialized_end=83709
  _RESACTIVITYBUFF._serialized_start=83711
  _RESACTIVITYBUFF._serialized_end=83795
  _REQUPGRADEACTIVITYBUFF._serialized_start=83797
  _REQUPGRADEACTIVITYBUFF._serialized_end=83838
  _REQUPGRADEACTIVITYLEVEL._serialized_start=83840
  _REQUPGRADEACTIVITYLEVEL._serialized_end=83916
  _RESUPGRADEACTIVITYLEVEL._serialized_start=83918
  _RESUPGRADEACTIVITYLEVEL._serialized_end=84005
  _REQRECEIVEUPGRADEACTIVITYREWARD._serialized_start=84007
  _REQRECEIVEUPGRADEACTIVITYREWARD._serialized_end=84061
  _RESRECEIVEUPGRADEACTIVITYREWARD._serialized_start=84063
  _RESRECEIVEUPGRADEACTIVITYREWARD._serialized_end=84158
  _REQRECEIVEALLACTIVITYGIFT._serialized_start=84160
  _REQRECEIVEALLACTIVITYGIFT._serialized_end=84208
  _RESRECEIVEALLACTIVITYGIFT._serialized_start=84211
  _RESRECEIVEALLACTIVITYGIFT._serialized_end=84455
  _RESRECEIVEALLACTIVITYGIFT_RECEIVEREWARDS._serialized_start=84370
  _RESRECEIVEALLACTIVITYGIFT_RECEIVEREWARDS._serialized_end=84455
  _RESUPGRADECHALLENGE._serialized_start=84458
  _RESUPGRADECHALLENGE._serialized_end=84624
  _RESREFRESHCHALLENGE._serialized_start=84627
  _RESREFRESHCHALLENGE._serialized_end=84793
  _RESFETCHCHALLENGEINFO._serialized_start=84796
  _RESFETCHCHALLENGEINFO._serialized_end=84989
  _REQFORCECOMPLETECHALLENGETASK._serialized_start=84991
  _REQFORCECOMPLETECHALLENGETASK._serialized_end=85039
  _RESFETCHABMATCH._serialized_start=85042
  _RESFETCHABMATCH._serialized_end=85297
  _RESFETCHABMATCH_MATCHPOINT._serialized_start=10048
  _RESFETCHABMATCH_MATCHPOINT._serialized_end=10093
  _REQSTARTUNIFIEDMATCH._serialized_start=85299
  _REQSTARTUNIFIEDMATCH._serialized_end=85371
  _REQCANCELUNIFIEDMATCH._serialized_start=85373
  _REQCANCELUNIFIEDMATCH._serialized_end=85415
  _RESCHALLENGESEASONINFO._serialized_start=85418
  _RESCHALLENGESEASONINFO._serialized_end=85630
  _RESCHALLENGESEASONINFO_CHALLENGEINFO._serialized_start=85543
  _RESCHALLENGESEASONINFO_CHALLENGEINFO._serialized_end=85630
  _REQRECEIVECHALLENGERANKREWARD._serialized_start=85632
  _REQRECEIVECHALLENGERANKREWARD._serialized_end=85682
  _RESRECEIVECHALLENGERANKREWARD._serialized_start=85685
  _RESRECEIVECHALLENGERANKREWARD._serialized_end=85847
  _RESRECEIVECHALLENGERANKREWARD_REWARD._serialized_start=85803
  _RESRECEIVECHALLENGERANKREWARD_REWARD._serialized_end=85847
  _REQBUYINABMATCH._serialized_start=85849
  _REQBUYINABMATCH._serialized_end=85884
  _REQGAMEPOINTRANK._serialized_start=85886
  _REQGAMEPOINTRANK._serialized_end=85925
  _RESGAMEPOINTRANK._serialized_start=85928
  _RESGAMEPOINTRANK._serialized_end=86083
  _RESGAMEPOINTRANK_RANKINFO._serialized_start=86038
  _RESGAMEPOINTRANK_RANKINFO._serialized_end=86083
  _RESFETCHSELFGAMEPOINTRANK._serialized_start=86085
  _RESFETCHSELFGAMEPOINTRANK._serialized_end=86157
  _REQREADSNS._serialized_start=86159
  _REQREADSNS._serialized_end=86183
  _RESREADSNS._serialized_start=86185
  _RESREADSNS._serialized_end=86257
  _REQREPLYSNS._serialized_start=86259
  _REQREPLYSNS._serialized_end=86284
  _RESREPLYSNS._serialized_start=86286
  _RESREPLYSNS._serialized_end=86358
  _REQLIKESNS._serialized_start=86360
  _REQLIKESNS._serialized_end=86384
  _RESLIKESNS._serialized_start=86386
  _RESLIKESNS._serialized_end=86442
  _REQDIGMINE._serialized_start=86444
  _REQDIGMINE._serialized_end=86503
  _RESDIGMINE._serialized_start=86505
  _RESDIGMINE._serialized_end=86604
  _REQFETCHLASTPRIVACY._serialized_start=86606
  _REQFETCHLASTPRIVACY._serialized_end=86641
  _RESFETCHLASTPRIVACY._serialized_start=86644
  _RESFETCHLASTPRIVACY._serialized_end=86791
  _RESFETCHLASTPRIVACY_PRIVACYINFO._serialized_start=86747
  _RESFETCHLASTPRIVACY_PRIVACYINFO._serialized_end=86791
  _REQCHECKPRIVACY._serialized_start=86794
  _REQCHECKPRIVACY._serialized_end=86923
  _REQCHECKPRIVACY_VERSIONS._serialized_start=86882
  _REQCHECKPRIVACY_VERSIONS._serialized_end=86923
  _REQFETCHRPGBATTLEHISTORY._serialized_start=86925
  _REQFETCHRPGBATTLEHISTORY._serialized_end=86972
  _RESFETCHRPGBATTLEHISTORY._serialized_start=86975
  _RESFETCHRPGBATTLEHISTORY._serialized_end=87437
  _RESFETCHRPGBATTLEHISTORY_BATTLERESULT._serialized_start=87168
  _RESFETCHRPGBATTLEHISTORY_BATTLERESULT._serialized_end=87437
  _RESFETCHRPGBATTLEHISTORYV2._serialized_start=87440
  _RESFETCHRPGBATTLEHISTORYV2._serialized_end=87891
  _RESFETCHRPGBATTLEHISTORYV2_BATTLERESULTV2._serialized_start=87716
  _RESFETCHRPGBATTLEHISTORYV2_BATTLERESULTV2._serialized_end=87891
  _REQBUYARENATICKET._serialized_start=87893
  _REQBUYARENATICKET._serialized_end=87933
  _REQARENAREWARD._serialized_start=87935
  _REQARENAREWARD._serialized_end=87972
  _REQENTERARENA._serialized_start=87974
  _REQENTERARENA._serialized_end=88010
  _RESARENAREWARD._serialized_start=88013
  _RESARENAREWARD._serialized_end=88142
  _RESARENAREWARD_REWARDITEM._serialized_start=88103
  _RESARENAREWARD_REWARDITEM._serialized_end=88142
  _REQRECEIVERPGREWARDS._serialized_start=88144
  _REQRECEIVERPGREWARDS._serialized_end=88187
  _REQRECEIVERPGREWARD._serialized_start=88189
  _REQRECEIVERPGREWARD._serialized_end=88252
  _RESRECEIVERPGREWARDS._serialized_start=88255
  _RESRECEIVERPGREWARDS._serialized_end=88396
  _RESRECEIVERPGREWARDS_REWARDITEM._serialized_start=88103
  _RESRECEIVERPGREWARDS_REWARDITEM._serialized_end=88142
  _REQFETCHOBTOKEN._serialized_start=88398
  _REQFETCHOBTOKEN._serialized_end=88429
  _RESFETCHOBTOKEN._serialized_start=88431
  _RESFETCHOBTOKEN._serialized_end=88545
  _REQRECEIVECHARACTERREWARDS._serialized_start=88547
  _REQRECEIVECHARACTERREWARDS._serialized_end=88612
  _RESRECEIVECHARACTERREWARDS._serialized_start=88615
  _RESRECEIVECHARACTERREWARDS._serialized_end=88768
  _RESRECEIVECHARACTERREWARDS_REWARDITEM._serialized_start=88103
  _RESRECEIVECHARACTERREWARDS_REWARDITEM._serialized_end=88142
  _REQFEEDACTIVITYFEED._serialized_start=88770
  _REQFEEDACTIVITYFEED._serialized_end=88827
  _RESFEEDACTIVITYFEED._serialized_start=88830
  _RESFEEDACTIVITYFEED._serialized_end=88989
  _RESFEEDACTIVITYFEED_REWARDITEM._serialized_start=88103
  _RESFEEDACTIVITYFEED_REWARDITEM._serialized_end=88142
  _REQSENDACTIVITYGIFTTOFRIEND._serialized_start=88991
  _REQSENDACTIVITYGIFTTOFRIEND._serialized_end=89077
  _RESSENDACTIVITYGIFTTOFRIEND._serialized_start=89079
  _RESSENDACTIVITYGIFTTOFRIEND._serialized_end=89159
  _REQRECEIVEACTIVITYGIFT._serialized_start=89161
  _REQRECEIVEACTIVITYGIFT._serialized_end=89218
  _REQFETCHFRIENDGIFTACTIVITYDATA._serialized_start=89220
  _REQFETCHFRIENDGIFTACTIVITYDATA._serialized_end=89295
  _RESFETCHFRIENDGIFTACTIVITYDATA._serialized_start=89298
  _RESFETCHFRIENDGIFTACTIVITYDATA._serialized_end=89585
  _RESFETCHFRIENDGIFTACTIVITYDATA_ITEMCOUNTDATA._serialized_start=89419
  _RESFETCHFRIENDGIFTACTIVITYDATA_ITEMCOUNTDATA._serialized_end=89463
  _RESFETCHFRIENDGIFTACTIVITYDATA_FRIENDDATA._serialized_start=89465
  _RESFETCHFRIENDGIFTACTIVITYDATA_FRIENDDATA._serialized_end=89585
  _REQOPENPRECHESTITEM._serialized_start=89587
  _REQOPENPRECHESTITEM._serialized_end=89642
  _RESOPENPRECHESTITEM._serialized_start=89644
  _RESOPENPRECHESTITEM._serialized_end=89724
  _REQFETCHVOTEACTIVITY._serialized_start=89726
  _REQFETCHVOTEACTIVITY._serialized_end=89769
  _RESFETCHVOTEACTIVITY._serialized_start=89771
  _RESFETCHVOTEACTIVITY._serialized_end=89859
  _REQVOTEACTIVITY._serialized_start=89861
  _REQVOTEACTIVITY._serialized_end=89913
  _RESVOTEACTIVITY._serialized_start=89915
  _RESVOTEACTIVITY._serialized_end=89994
  _REQUNLOCKACTIVITYSPOT._serialized_start=89996
  _REQUNLOCKACTIVITYSPOT._serialized_end=90038
  _REQUNLOCKACTIVITYSPOTENDING._serialized_start=90040
  _REQUNLOCKACTIVITYSPOTENDING._serialized_end=90107
  _REQRECEIVEACTIVITYSPOTREWARD._serialized_start=90109
  _REQRECEIVEACTIVITYSPOTREWARD._serialized_end=90158
  _RESRECEIVEACTIVITYSPOTREWARD._serialized_start=90161
  _RESRECEIVEACTIVITYSPOTREWARD._serialized_end=90318
  _RESRECEIVEACTIVITYSPOTREWARD_REWARDITEM._serialized_start=88103
  _RESRECEIVEACTIVITYSPOTREWARD_REWARDITEM._serialized_end=88142
  _REQLOGREPORT._serialized_start=90320
  _REQLOGREPORT._serialized_end=90367
  _REQBINDOAUTH2._serialized_start=90369
  _REQBINDOAUTH2._serialized_end=90413
  _REQFETCHOAUTH2._serialized_start=90415
  _REQFETCHOAUTH2._serialized_end=90445
  _RESFETCHOAUTH2._serialized_start=90447
  _RESFETCHOAUTH2._serialized_end=90505
  _RESDELETEACCOUNT._serialized_start=90507
  _RESDELETEACCOUNT._serialized_end=90572
  _REQSETLOADINGIMAGE._serialized_start=90574
  _REQSETLOADINGIMAGE._serialized_end=90610
  _RESFETCHSHOPINTERVAL._serialized_start=90613
  _RESFETCHSHOPINTERVAL._serialized_end=90768
  _RESFETCHSHOPINTERVAL_SHOPINTERVAL._serialized_start=90718
  _RESFETCHSHOPINTERVAL_SHOPINTERVAL._serialized_end=90768
  _RESFETCHACTIVITYINTERVAL._serialized_start=90771
  _RESFETCHACTIVITYINTERVAL._serialized_end=90945
  _RESFETCHACTIVITYINTERVAL_ACTIVITYINTERVAL._serialized_start=90888
  _RESFETCHACTIVITYINTERVAL_ACTIVITYINTERVAL._serialized_end=90945
  _RESFETCHRECENTFRIEND._serialized_start=90947
  _RESFETCHRECENTFRIEND._serialized_end=91017
  _REQOPENGACHA._serialized_start=91019
  _REQOPENGACHA._serialized_end=91069
  _RESOPENGACHA._serialized_start=91072
  _RESOPENGACHA._serialized_end=91240
  _REQTASKREQUEST._serialized_start=91242
  _REQTASKREQUEST._serialized_end=91274
  _REQSIMULATIONACTIVITYTRAIN._serialized_start=91276
  _REQSIMULATIONACTIVITYTRAIN._serialized_end=91339
  _RESSIMULATIONACTIVITYTRAIN._serialized_start=91341
  _RESSIMULATIONACTIVITYTRAIN._serialized_end=91437
  _REQFETCHSIMULATIONGAMERECORD._serialized_start=91439
  _REQFETCHSIMULATIONGAMERECORD._serialized_end=91509
  _RESFETCHSIMULATIONGAMERECORD._serialized_start=91511
  _RESFETCHSIMULATIONGAMERECORD._serialized_end=91626
  _REQSTARTSIMULATIONACTIVITYGAME._serialized_start=91628
  _REQSTARTSIMULATIONACTIVITYGAME._serialized_end=91681
  _RESSTARTSIMULATIONACTIVITYGAME._serialized_start=91683
  _RESSTARTSIMULATIONACTIVITYGAME._serialized_end=91792
  _REQFETCHSIMULATIONGAMERANK._serialized_start=91794
  _REQFETCHSIMULATIONGAMERANK._serialized_end=91856
  _RESFETCHSIMULATIONGAMERANK._serialized_start=91859
  _RESFETCHSIMULATIONGAMERANK._serialized_end=92014
  _RESFETCHSIMULATIONGAMERANK_RANKINFO._serialized_start=91970
  _RESFETCHSIMULATIONGAMERANK_RANKINFO._serialized_end=92014
  _REQGENERATECOMBININGCRAFT._serialized_start=92016
  _REQGENERATECOMBININGCRAFT._serialized_end=92080
  _RESGENERATECOMBININGCRAFT._serialized_start=92082
  _RESGENERATECOMBININGCRAFT._serialized_end=92166
  _REQMOVECOMBININGCRAFT._serialized_start=92168
  _REQMOVECOMBININGCRAFT._serialized_end=92238
  _RESMOVECOMBININGCRAFT._serialized_start=92241
  _RESMOVECOMBININGCRAFT._serialized_end=92435
  _RESMOVECOMBININGCRAFT_BONUSDATA._serialized_start=92393
  _RESMOVECOMBININGCRAFT_BONUSDATA._serialized_end=92435
  _REQCOMBININGRECYCLECRAFT._serialized_start=92437
  _REQCOMBININGRECYCLECRAFT._serialized_end=92497
  _RESCOMBININGRECYCLECRAFT._serialized_start=92499
  _RESCOMBININGRECYCLECRAFT._serialized_end=92592
  _REQRECOVERCOMBININGRECYCLE._serialized_start=92594
  _REQRECOVERCOMBININGRECYCLE._serialized_end=92643
  _RESRECOVERCOMBININGRECYCLE._serialized_start=92645
  _RESRECOVERCOMBININGRECYCLE._serialized_end=92730
  _REQFINISHCOMBININGORDER._serialized_start=92732
  _REQFINISHCOMBININGORDER._serialized_end=92816
  _RESFINISHCOMBININGORDER._serialized_start=92818
  _RESFINISHCOMBININGORDER._serialized_end=92910
  _RESFETCHINFO._serialized_start=92913
  _RESFETCHINFO._serialized_end=94436
  _RESFETCHSEERINFO._serialized_start=94438
  _RESFETCHSEERINFO._serialized_end=94545
  _RESFETCHSERVERMAINTENANCEINFO._serialized_start=94548
  _RESFETCHSERVERMAINTENANCEINFO._serialized_end=94735
  _RESFETCHSERVERMAINTENANCEINFO_SERVERFUNCTIONMAINTENANCEINFO._serialized_start=94676
  _RESFETCHSERVERMAINTENANCEINFO_SERVERFUNCTIONMAINTENANCEINFO._serialized_end=94735
  _REQUPGRADEVILLAGEBUILDING._serialized_start=94737
  _REQUPGRADEVILLAGEBUILDING._serialized_end=94806
  _REQRECEIVEVILLAGEBUILDINGREWARD._serialized_start=94808
  _REQRECEIVEVILLAGEBUILDINGREWARD._serialized_end=94916
  _RESRECEIVEVILLAGEBUILDINGREWARD._serialized_start=94918
  _RESRECEIVEVILLAGEBUILDINGREWARD._serialized_end=95018
  _REQSTARTVILLAGETRIP._serialized_start=95020
  _REQSTARTVILLAGETRIP._serialized_end=95076
  _REQRECEIVEVILLAGETRIPREWARD._serialized_start=95078
  _REQRECEIVEVILLAGETRIPREWARD._serialized_end=95178
  _RESRECEIVEVILLAGETRIPREWARD._serialized_start=95180
  _RESRECEIVEVILLAGETRIPREWARD._serialized_end=95276
  _REQCOMPLETEVILLAGETASK._serialized_start=95278
  _REQCOMPLETEVILLAGETASK._serialized_end=95340
  _RESCOMPLETEVILLAGETASK._serialized_start=95342
  _RESCOMPLETEVILLAGETASK._serialized_end=95433
  _REQGETFRIENDVILLAGEDATA._serialized_start=95435
  _REQGETFRIENDVILLAGEDATA._serialized_end=95503
  _RESGETFRIENDVILLAGEDATA._serialized_start=95506
  _RESGETFRIENDVILLAGEDATA._serialized_end=95674
  _RESGETFRIENDVILLAGEDATA_FRIENDVILLAGEDATA._serialized_start=95620
  _RESGETFRIENDVILLAGEDATA_FRIENDVILLAGEDATA._serialized_end=95674
  _REQSETVILLAGEWORKER._serialized_start=95676
  _REQSETVILLAGEWORKER._serialized_end=95759
  _RESSETVILLAGEWORKER._serialized_start=95761
  _RESSETVILLAGEWORKER._serialized_end=95872
  _REQNEXTROUNDVILLAGE._serialized_start=95874
  _REQNEXTROUNDVILLAGE._serialized_end=95916
  _RESNEXTROUNDVILLAGE._serialized_start=95918
  _RESNEXTROUNDVILLAGE._serialized_end=96013
  _REQRESOLVEFESTIVALACTIVITYPROPOSAL._serialized_start=96015
  _REQRESOLVEFESTIVALACTIVITYPROPOSAL._serialized_end=96100
  _RESRESOLVEFESTIVALACTIVITYPROPOSAL._serialized_start=96103
  _RESRESOLVEFESTIVALACTIVITYPROPOSAL._serialized_end=96260
  _REQRESOLVEFESTIVALACTIVITYEVENT._serialized_start=96262
  _REQRESOLVEFESTIVALACTIVITYEVENT._serialized_end=96344
  _RESRESOLVEFESTIVALACTIVITYEVENT._serialized_start=96347
  _RESRESOLVEFESTIVALACTIVITYEVENT._serialized_end=96504
  _REQBUYFESTIVALPROPOSAL._serialized_start=96506
  _REQBUYFESTIVALPROPOSAL._serialized_end=96551
  _RESBUYFESTIVALPROPOSAL._serialized_start=96553
  _RESBUYFESTIVALPROPOSAL._serialized_end=96651
  _REQISLANDACTIVITYMOVE._serialized_start=96653
  _REQISLANDACTIVITYMOVE._serialized_end=96714
  _REQISLANDACTIVITYBUY._serialized_start=96717
  _REQISLANDACTIVITYBUY._serialized_end=96900
  _REQISLANDACTIVITYBUY_BUYITEMS._serialized_start=96812
  _REQISLANDACTIVITYBUY_BUYITEMS._serialized_end=96900
  _REQISLANDACTIVITYSELL._serialized_start=96903
  _REQISLANDACTIVITYSELL._serialized_end=97053
  _REQISLANDACTIVITYSELL_SELLITEM._serialized_start=97000
  _REQISLANDACTIVITYSELL_SELLITEM._serialized_end=97053
  _REQISLANDACTIVITYTIDYBAG._serialized_start=97056
  _REQISLANDACTIVITYTIDYBAG._serialized_end=97317
  _REQISLANDACTIVITYTIDYBAG_BAGDATA._serialized_start=97162
  _REQISLANDACTIVITYTIDYBAG_BAGDATA._serialized_end=97317
  _REQISLANDACTIVITYTIDYBAG_BAGDATA_ITEMDATA._serialized_start=97266
  _REQISLANDACTIVITYTIDYBAG_BAGDATA_ITEMDATA._serialized_end=97317
  _REQISLANDACTIVITYUNLOCKBAGGRID._serialized_start=97319
  _REQISLANDACTIVITYUNLOCKBAGGRID._serialized_end=97401
  _CONTESTSETTING._serialized_start=97404
  _CONTESTSETTING._serialized_end=97561
  _CONTESTSETTING_LEVELLIMIT._serialized_start=97520
  _CONTESTSETTING_LEVELLIMIT._serialized_end=97561
  _REQCREATECUSTOMIZEDCONTEST._serialized_start=97564
  _REQCREATECUSTOMIZEDCONTEST._serialized_end=97788
  _RESCREATECUSTOMIZEDCONTEST._serialized_start=97790
  _RESCREATECUSTOMIZEDCONTEST._serialized_end=97863
  _REQFETCHMANAGERCUSTOMIZEDCONTESTLIST._serialized_start=97865
  _REQFETCHMANAGERCUSTOMIZEDCONTESTLIST._serialized_end=97917
  _RESFETCHMANAGERCUSTOMIZEDCONTESTLIST._serialized_start=97919
  _RESFETCHMANAGERCUSTOMIZEDCONTESTLIST._serialized_end=98028
  _REQFETCHMANAGERCUSTOMIZEDCONTEST._serialized_start=98030
  _REQFETCHMANAGERCUSTOMIZEDCONTEST._serialized_end=98083
  _RESFETCHMANAGERCUSTOMIZEDCONTEST._serialized_start=98086
  _RESFETCHMANAGERCUSTOMIZEDCONTEST._serialized_end=98386
  _REQUPDATEMANAGERCUSTOMIZEDCONTEST._serialized_start=98389
  _REQUPDATEMANAGERCUSTOMIZEDCONTEST._serialized_end=98639
  _REQFETCHCONTESTPLAYERRANK._serialized_start=98641
  _REQFETCHCONTESTPLAYERRANK._serialized_end=98718
  _RESFETCHCONTESTPLAYERRANK._serialized_start=98721
  _RESFETCHCONTESTPLAYERRANK._serialized_end=99578
  _RESFETCHCONTESTPLAYERRANK_CONTESTPLAYERACCOUNTDATA._serialized_start=98911
  _RESFETCHCONTESTPLAYERRANK_CONTESTPLAYERACCOUNTDATA._serialized_end=99358
  _RESFETCHCONTESTPLAYERRANK_CONTESTPLAYERACCOUNTDATA_CONTESTGAMERESULT._serialized_start=99172
  _RESFETCHCONTESTPLAYERRANK_CONTESTPLAYERACCOUNTDATA_CONTESTGAMERESULT._serialized_end=99226
  _RESFETCHCONTESTPLAYERRANK_CONTESTPLAYERACCOUNTDATA_CONTESTSERIESGAMERESULT._serialized_start=99229
  _RESFETCHCONTESTPLAYERRANK_CONTESTPLAYERACCOUNTDATA_CONTESTSERIESGAMERESULT._serialized_end=99358
  _RESFETCHCONTESTPLAYERRANK_SEASONRANK._serialized_start=99360
  _RESFETCHCONTESTPLAYERRANK_SEASONRANK._serialized_end=99480
  _RESFETCHCONTESTPLAYERRANK_PLAYERDATA._serialized_start=99482
  _RESFETCHCONTESTPLAYERRANK_PLAYERDATA._serialized_end=99578
  _REQFETCHREADYPLAYERLIST._serialized_start=99580
  _REQFETCHREADYPLAYERLIST._serialized_end=99624
  _RESFETCHREADYPLAYERLIST._serialized_start=99627
  _RESFETCHREADYPLAYERLIST._serialized_end=99776
  _RESFETCHREADYPLAYERLIST_PLAYER._serialized_start=99730
  _RESFETCHREADYPLAYERLIST_PLAYER._serialized_end=99776
  _REQCREATEGAMEPLAN._serialized_start=99778
  _REQCREATEGAMEPLAN._serialized_end=99904
  _RESGENERATECONTESTMANAGERLOGINCODE._serialized_start=99906
  _RESGENERATECONTESTMANAGERLOGINCODE._serialized_end=99982
  _REQAMULETACTIVITYFETCHINFO._serialized_start=99984
  _REQAMULETACTIVITYFETCHINFO._serialized_end=100033
  _RESAMULETACTIVITYFETCHINFO._serialized_start=100035
  _RESAMULETACTIVITYFETCHINFO._serialized_end=100127
  _REQAMULETACTIVITYFETCHBRIEF._serialized_start=100129
  _REQAMULETACTIVITYFETCHBRIEF._serialized_end=100179
  _RESAMULETACTIVITYFETCHBRIEF._serialized_start=100182
  _RESAMULETACTIVITYFETCHBRIEF._serialized_end=100392
  _REQAMULETACTIVITYSTARTGAME._serialized_start=100394
  _REQAMULETACTIVITYSTARTGAME._serialized_end=100443
  _RESAMULETACTIVITYSTARTGAME._serialized_start=100445
  _RESAMULETACTIVITYSTARTGAME._serialized_end=100533
  _REQAMULETACTIVITYOPERATE._serialized_start=100535
  _REQAMULETACTIVITYOPERATE._serialized_end=100610
  _RESAMULETACTIVITYOPERATE._serialized_start=100613
  _RESAMULETACTIVITYOPERATE._serialized_end=101042
  _REQAMULETACTIVITYCHANGEHANDS._serialized_start=101044
  _REQAMULETACTIVITYCHANGEHANDS._serialized_end=101110
  _RESAMULETACTIVITYCHANGEHANDS._serialized_start=101113
  _RESAMULETACTIVITYCHANGEHANDS._serialized_end=101308
  _REQAMULETACTIVITYUPGRADE._serialized_start=101310
  _REQAMULETACTIVITYUPGRADE._serialized_end=101357
  _RESAMULETACTIVITYUPGRADE._serialized_start=101360
  _RESAMULETACTIVITYUPGRADE._serialized_end=101497
  _REQAMULETACTIVITYSELECTPACK._serialized_start=101499
  _REQAMULETACTIVITYSELECTPACK._serialized_end=101561
  _RESAMULETACTIVITYSELECTPACK._serialized_start=101564
  _RESAMULETACTIVITYSELECTPACK._serialized_end=101700
  _REQAMULETACTIVITYBUY._serialized_start=101702
  _REQAMULETACTIVITYBUY._serialized_end=101757
  _RESAMULETACTIVITYBUY._serialized_start=101760
  _RESAMULETACTIVITYBUY._serialized_end=101947
  _REQAMULETACTIVITYSELLEFFECT._serialized_start=101949
  _REQAMULETACTIVITYSELLEFFECT._serialized_end=102011
  _RESAMULETACTIVITYSELLEFFECT._serialized_start=102014
  _RESAMULETACTIVITYSELLEFFECT._serialized_end=102296
  _REQAMULETACTIVITYEFFECTSORT._serialized_start=102298
  _REQAMULETACTIVITYEFFECTSORT._serialized_end=102367
  _REQAMULETACTIVITYGIVEUP._serialized_start=102369
  _REQAMULETACTIVITYGIVEUP._serialized_end=102415
  _REQAMULETACTIVITYREFRESHSHOP._serialized_start=102417
  _REQAMULETACTIVITYREFRESHSHOP._serialized_end=102468
  _RESAMULETACTIVITYREFRESHSHOP._serialized_start=102471
  _RESAMULETACTIVITYREFRESHSHOP._serialized_end=102622
  _REQAMULETACTIVITYSELECTFREEEFFECT._serialized_start=102624
  _REQAMULETACTIVITYSELECTFREEEFFECT._serialized_end=102701
  _RESAMULETACTIVITYSELECTFREEEFFECT._serialized_start=102704
  _RESAMULETACTIVITYSELECTFREEEFFECT._serialized_end=102894
  _REQAMULETACTIVITYUPGRADESHOPBUFF._serialized_start=102896
  _REQAMULETACTIVITYUPGRADESHOPBUFF._serialized_end=102963
  _RESAMULETACTIVITYUPGRADESHOPBUFF._serialized_start=102966
  _RESAMULETACTIVITYUPGRADESHOPBUFF._serialized_end=103148
  _REQAMULETACTIVITYENDSHOPPING._serialized_start=103150
  _REQAMULETACTIVITYENDSHOPPING._serialized_end=103201
  _RESAMULETACTIVITYENDSHOPPING._serialized_start=103203
  _RESAMULETACTIVITYENDSHOPPING._serialized_end=103306
  _REQAMULETACTIVITYSETSKILLLEVEL._serialized_start=103308
  _REQAMULETACTIVITYSETSKILLLEVEL._serialized_end=103397
  _RESAMULETACTIVITYMAINTAININFO._serialized_start=103399
  _RESAMULETACTIVITYMAINTAININFO._serialized_end=103470
  _REQAMULETACTIVITYSELECTREWARDPACK._serialized_start=103472
  _REQAMULETACTIVITYSELECTREWARDPACK._serialized_end=103540
  _RESAMULETACTIVITYSELECTREWARDPACK._serialized_start=103543
  _RESAMULETACTIVITYSELECTREWARDPACK._serialized_end=103689
  _REQAMULETACTIVITYRECEIVETASKREWARD._serialized_start=103691
  _REQAMULETACTIVITYRECEIVETASKREWARD._serialized_end=103767
  _RESAMULETACTIVITYRECEIVETASKREWARD._serialized_start=103769
  _RESAMULETACTIVITYRECEIVETASKREWARD._serialized_end=103873
  _REQSTORYACTIVITYUNLOCK._serialized_start=103875
  _REQSTORYACTIVITYUNLOCK._serialized_end=103938
  _REQSTORYACTIVITYUNLOCKENDING._serialized_start=103940
  _REQSTORYACTIVITYUNLOCKENDING._serialized_end=104028
  _REQSTORYACTIVITYRECEIVEENDINGREWARD._serialized_start=104030
  _REQSTORYACTIVITYRECEIVEENDINGREWARD._serialized_end=104125
  _RESSTORYREWARD._serialized_start=104127
  _RESSTORYREWARD._serialized_end=104210
  _REQSTORYACTIVITYRECEIVEFINISHREWARD._serialized_start=104212
  _REQSTORYACTIVITYRECEIVEFINISHREWARD._serialized_end=104288
  _REQSTORYACTIVITYRECEIVEALLFINISHREWARD._serialized_start=104290
  _REQSTORYACTIVITYRECEIVEALLFINISHREWARD._serialized_end=104369
  _REQSTORYACTIVITYUNLOCKENDINGANDRECEIVE._serialized_start=104371
  _REQSTORYACTIVITYUNLOCKENDINGANDRECEIVE._serialized_end=104469
  _RESSTORYACTIVITYUNLOCKENDINGANDRECEIVE._serialized_start=104472
  _RESSTORYACTIVITYUNLOCKENDINGANDRECEIVE._serialized_end=104668
  _REQFETCHACTIVITYRANK._serialized_start=104670
  _REQFETCHACTIVITYRANK._serialized_end=104735
  _RESFETCHACTIVITYRANK._serialized_start=104738
  _RESFETCHACTIVITYRANK._serialized_end=104984
  _RESFETCHACTIVITYRANK_ACTIVITYRANKITEM._serialized_start=104903
  _RESFETCHACTIVITYRANK_ACTIVITYRANKITEM._serialized_end=104984
  _REQFETCHQUESTIONNAIRELIST._serialized_start=104986
  _REQFETCHQUESTIONNAIRELIST._serialized_end=105044
  _RESFETCHQUESTIONNAIRELIST._serialized_start=105046
  _RESFETCHQUESTIONNAIRELIST._serialized_end=105160
  _REQFETCHQUESTIONNAIREDETAIL._serialized_start=105162
  _REQFETCHQUESTIONNAIREDETAIL._serialized_end=105234
  _RESFETCHQUESTIONNAIREDETAIL._serialized_start=105236
  _RESFETCHQUESTIONNAIREDETAIL._serialized_end=105332
  _REQSETVERIFIEDHIDDEN._serialized_start=105334
  _REQSETVERIFIEDHIDDEN._serialized_end=105381
  _REQSUBMITQUESTIONNAIRE._serialized_start=105384
  _REQSUBMITQUESTIONNAIRE._serialized_end=105788
  _REQSUBMITQUESTIONNAIRE_QUESTIONNAIREANSWER._serialized_start=105592
  _REQSUBMITQUESTIONNAIRE_QUESTIONNAIREANSWER._serialized_end=105788
  _REQSUBMITQUESTIONNAIRE_QUESTIONNAIREANSWER_QUESTIONNAIREANSWERVALUE._serialized_start=105725
  _REQSUBMITQUESTIONNAIRE_QUESTIONNAIREANSWER_QUESTIONNAIREANSWERVALUE._serialized_end=105788
  _REQSETFRIENDROOMRANDOMBOTCHAR._serialized_start=105790
  _REQSETFRIENDROOMRANDOMBOTCHAR._serialized_end=105850
  _REQFETCHACCOUNTGAMEHURECORDS._serialized_start=105852
  _REQFETCHACCOUNTGAMEHURECORDS._serialized_end=105928
  _RESFETCHACCOUNTGAMEHURECORDS._serialized_start=105931
  _RESFETCHACCOUNTGAMEHURECORDS._serialized_end=106189
  _RESFETCHACCOUNTGAMEHURECORDS_GAMEHURECORDS._serialized_start=106055
  _RESFETCHACCOUNTGAMEHURECORDS_GAMEHURECORDS._serialized_end=106189
  _REQFETCHACCOUNTINFOEXTRA._serialized_start=106191
  _REQFETCHACCOUNTINFOEXTRA._serialized_end=106269
  _RESFETCHACCOUNTINFOEXTRA._serialized_start=106272
  _RESFETCHACCOUNTINFOEXTRA._serialized_end=107099
  _RESFETCHACCOUNTINFOEXTRA_ACCOUNTINFOGAMERECORD._serialized_start=106552
  _RESFETCHACCOUNTINFOEXTRA_ACCOUNTINFOGAMERECORD._serialized_end=106996
  _RESFETCHACCOUNTINFOEXTRA_ACCOUNTINFOGAMERECORD_ACCOUNTGAMERESULT._serialized_start=106782
  _RESFETCHACCOUNTINFOEXTRA_ACCOUNTINFOGAMERECORD_ACCOUNTGAMERESULT._serialized_end=106996
  _RESFETCHACCOUNTINFOEXTRA_GAMEHUTYPEDETAIL._serialized_start=106998
  _RESFETCHACCOUNTINFOEXTRA_GAMEHUTYPEDETAIL._serialized_end=107045
  _RESFETCHACCOUNTINFOEXTRA_ACCOUNTGAMERANKDETAIL._serialized_start=107047
  _RESFETCHACCOUNTINFOEXTRA_ACCOUNTGAMERANKDETAIL._serialized_end=107099
  _REQSETACCOUNTFAVORITEHU._serialized_start=107101
  _REQSETACCOUNTFAVORITEHU._serialized_end=107226
  _REQFETCHSEERREPORT._serialized_start=107228
  _REQFETCHSEERREPORT._serialized_end=107262
  _RESFETCHSEERREPORT._serialized_start=107264
  _RESFETCHSEERREPORT._serialized_end=107342
  _REQCREATESEERREPORT._serialized_start=107344
  _REQCREATESEERREPORT._serialized_end=107379
  _RESCREATESEERREPORT._serialized_start=107381
  _RESCREATESEERREPORT._serialized_end=107464
  _RESFETCHSEERREPORTLIST._serialized_start=107466
  _RESFETCHSEERREPORTLIST._serialized_end=107557
  _REQSELECTCHESTCHOOSEUP._serialized_start=107559
  _REQSELECTCHESTCHOOSEUP._serialized_end=107641
  _REQGENERATEANNUALREPORTTOKEN._serialized_start=107643
  _REQGENERATEANNUALREPORTTOKEN._serialized_end=107687
  _RESGENERATEANNUALREPORTTOKEN._serialized_start=107689
  _RESGENERATEANNUALREPORTTOKEN._serialized_end=107773
  _RESFETCHANNUALREPORTINFO._serialized_start=107775
  _RESFETCHANNUALREPORTINFO._serialized_end=107865
  _REQREMARKFRIEND._serialized_start=107867
  _REQREMARKFRIEND._serialized_end=107920
  _REQSIMV2ACTIVITYFETCHINFO._serialized_start=107922
  _REQSIMV2ACTIVITYFETCHINFO._serialized_end=107970
  _RESSIMV2ACTIVITYFETCHINFO._serialized_start=107972
  _RESSIMV2ACTIVITYFETCHINFO._serialized_end=108061
  _REQSIMV2ACTIVITYSTARTSEASON._serialized_start=108063
  _REQSIMV2ACTIVITYSTARTSEASON._serialized_end=108113
  _RESSIMV2ACTIVITYSTARTSEASON._serialized_start=108115
  _RESSIMV2ACTIVITYSTARTSEASON._serialized_end=108214
  _REQSIMV2ACTIVITYTRAIN._serialized_start=108216
  _REQSIMV2ACTIVITYTRAIN._serialized_end=108291
  _RESSIMV2ACTIVITYTRAIN._serialized_start=108294
  _RESSIMV2ACTIVITYTRAIN._serialized_end=108561
  _REQSIMV2ACTIVITYSELECTEVENT._serialized_start=108563
  _REQSIMV2ACTIVITYSELECTEVENT._serialized_end=108635
  _RESSIMV2ACTIVITYSELECTEVENT._serialized_start=108638
  _RESSIMV2ACTIVITYSELECTEVENT._serialized_end=108974
  _REQSIMV2ACTIVITYSTARTMATCH._serialized_start=108976
  _REQSIMV2ACTIVITYSTARTMATCH._serialized_end=109025
  _RESSIMV2ACTIVITYSTARTMATCH._serialized_start=109028
  _RESSIMV2ACTIVITYSTARTMATCH._serialized_end=109225
  _REQSIMV2ACTIVITYENDMATCH._serialized_start=109227
  _REQSIMV2ACTIVITYENDMATCH._serialized_end=109274
  _RESSIMV2ACTIVITYENDMATCH._serialized_start=109277
  _RESSIMV2ACTIVITYENDMATCH._serialized_end=109688
  _RESSIMV2ACTIVITYENDMATCH_SIMULATIONV2MATCHREWARD._serialized_start=109633
  _RESSIMV2ACTIVITYENDMATCH_SIMULATIONV2MATCHREWARD._serialized_end=109688
  _REQSIMV2ACTIVITYGIVEUP._serialized_start=109690
  _REQSIMV2ACTIVITYGIVEUP._serialized_end=109735
  _REQSIMV2ACTIVITYSETUPGRADE._serialized_start=109737
  _REQSIMV2ACTIVITYSETUPGRADE._serialized_end=109828
  _REQAUTHGAME._serialized_start=109830
  _REQAUTHGAME._serialized_end=109940
  _RESAUTHGAME._serialized_start=109943
  _RESAUTHGAME._serialized_end=110157
  _GAMERESTORE._serialized_start=110160
  _GAMERESTORE._serialized_end=110344
  _RESENTERGAME._serialized_start=110346
  _RESENTERGAME._serialized_end=110455
  _REQSYNCGAME._serialized_start=110457
  _REQSYNCGAME._serialized_end=110502
  _RESSYNCGAME._serialized_start=110504
  _RESSYNCGAME._serialized_end=110612
  _REQSELFOPERATION._serialized_start=110615
  _REQSELFOPERATION._serialized_end=110815
  _REQCHIPENGGANG._serialized_start=110817
  _REQCHIPENGGANG._serialized_end=110905
  _REQBROADCASTINGAME._serialized_start=110907
  _REQBROADCASTINGAME._serialized_end=110965
  _REQGMCOMMANDINGAMING._serialized_start=110967
  _REQGMCOMMANDINGAMING._serialized_end=111008
  _RESGAMEPLAYERSTATE._serialized_start=111010
  _RESGAMEPLAYERSTATE._serialized_end=111097
  _REQVOTEGAMEEND._serialized_start=111099
  _REQVOTEGAMEEND._serialized_end=111128
  _RESGAMEENDVOTE._serialized_start=111130
  _RESGAMEENDVOTE._serialized_end=111215
  _REQAUTHOBSERVE._serialized_start=111217
  _REQAUTHOBSERVE._serialized_end=111248
  _RESSTARTOBSERVE._serialized_start=111250
  _RESSTARTOBSERVE._serialized_end=111336
  _NOTIFYNEWGAME._serialized_start=111338
  _NOTIFYNEWGAME._serialized_end=111393
  _NOTIFYPLAYERLOADGAMEREADY._serialized_start=111395
  _NOTIFYPLAYERLOADGAMEREADY._serialized_end=111445
  _NOTIFYGAMEBROADCAST._serialized_start=111447
  _NOTIFYGAMEBROADCAST._serialized_end=111499
  _NOTIFYGAMEENDRESULT._serialized_start=111501
  _NOTIFYGAMEENDRESULT._serialized_end=111557
  _NOTIFYGAMETERMINATE._serialized_start=111559
  _NOTIFYGAMETERMINATE._serialized_end=111596
  _NOTIFYPLAYERCONNECTIONSTATE._serialized_start=111598
  _NOTIFYPLAYERCONNECTIONSTATE._serialized_end=111677
  _NOTIFYACCOUNTLEVELCHANGE._serialized_start=111679
  _NOTIFYACCOUNTLEVELCHANGE._serialized_end=111786
  _NOTIFYGAMEFINISHREWARD._serialized_start=111789
  _NOTIFYGAMEFINISHREWARD._serialized_end=112475
  _NOTIFYGAMEFINISHREWARD_LEVELCHANGE._serialized_start=4434
  _NOTIFYGAMEFINISHREWARD_LEVELCHANGE._serialized_end=4528
  _NOTIFYGAMEFINISHREWARD_MATCHCHEST._serialized_start=4530
  _NOTIFYGAMEFINISHREWARD_MATCHCHEST._serialized_end=4643
  _NOTIFYGAMEFINISHREWARD_MAINCHARACTER._serialized_start=4645
  _NOTIFYGAMEFINISHREWARD_MAINCHARACTER._serialized_end=4701
  _NOTIFYGAMEFINISHREWARD_CHARACTERGIFT._serialized_start=4703
  _NOTIFYGAMEFINISHREWARD_CHARACTERGIFT._serialized_end=4781
  _NOTIFYACTIVITYREWARD._serialized_start=112478
  _NOTIFYACTIVITYREWARD._serialized_end=112638
  _NOTIFYACTIVITYREWARD_ACTIVITYREWARD._serialized_start=4878
  _NOTIFYACTIVITYREWARD_ACTIVITYREWARD._serialized_end=4948
  _NOTIFYACTIVITYPOINT._serialized_start=112641
  _NOTIFYACTIVITYPOINT._serialized_end=112779
  _NOTIFYACTIVITYPOINT_ACTIVITYPOINT._serialized_start=5042
  _NOTIFYACTIVITYPOINT_ACTIVITYPOINT._serialized_end=5093
  _NOTIFYLEADERBOARDPOINT._serialized_start=112782
  _NOTIFYLEADERBOARDPOINT._serialized_end=112938
  _NOTIFYLEADERBOARDPOINT_LEADERBOARDPOINT._serialized_start=5199
  _NOTIFYLEADERBOARDPOINT_LEADERBOARDPOINT._serialized_end=5256
  _NOTIFYGAMEPAUSE._serialized_start=112940
  _NOTIFYGAMEPAUSE._serialized_end=112973
  _NOTIFYENDGAMEVOTE._serialized_start=112976
  _NOTIFYENDGAMEVOTE._serialized_end=113136
  _NOTIFYENDGAMEVOTE_VOTERESULT._serialized_start=113091
  _NOTIFYENDGAMEVOTE_VOTERESULT._serialized_end=113136
  _NOTIFYOBSERVEDATA._serialized_start=113138
  _NOTIFYOBSERVEDATA._serialized_end=113189
  _ACTIONMJSTART._serialized_start=113191
  _ACTIONMJSTART._serialized_end=113206
  _NEWROUNDOPENEDTILES._serialized_start=113208
  _NEWROUNDOPENEDTILES._serialized_end=113273
  _MUYUINFO._serialized_start=113275
  _MUYUINFO._serialized_end=113345
  _CHUANMAGANG._serialized_start=113348
  _CHUANMAGANG._serialized_end=113486
  _YONGCHANGINFO._serialized_start=113488
  _YONGCHANGINFO._serialized_end=113605
  _ACTIONNEWCARD._serialized_start=113607
  _ACTIONNEWCARD._serialized_end=113643
  _RECORDNEWCARD._serialized_start=113645
  _RECORDNEWCARD._serialized_end=113681
  _ACTIONNEWROUND._serialized_start=113684
  _ACTIONNEWROUND._serialized_end=114175
  _RECORDNEWROUND._serialized_start=114178
  _RECORDNEWROUND._serialized_end=114811
  _RECORDNEWROUND_TINGPAI._serialized_start=114752
  _RECORDNEWROUND_TINGPAI._serialized_end=114811
  _GAMESNAPSHOT._serialized_start=114814
  _GAMESNAPSHOT._serialized_end=115223
  _GAMESNAPSHOT_PLAYERSNAPSHOT._serialized_start=115034
  _GAMESNAPSHOT_PLAYERSNAPSHOT._serialized_end=115223
  _GAMESNAPSHOT_PLAYERSNAPSHOT_FULU._serialized_start=115175
  _GAMESNAPSHOT_PLAYERSNAPSHOT_FULU._serialized_end=115223
  _ACTIONPROTOTYPE._serialized_start=115225
  _ACTIONPROTOTYPE._serialized_end=115284
  _GAMEDETAILRECORDS._serialized_start=115286
  _GAMEDETAILRECORDS._serialized_end=115385
  _GAMESELFOPERATION._serialized_start=115388
  _GAMESELFOPERATION._serialized_end=115589
  _GAMECHIPENGGANG._serialized_start=115591
  _GAMECHIPENGGANG._serialized_end=115680
  _GAMEVOTEGAMEEND._serialized_start=115682
  _GAMEVOTEGAMEEND._serialized_end=115712
  _GAMEUSERINPUT._serialized_start=115715
  _GAMEUSERINPUT._serialized_end=115882
  _GAMEUSEREVENT._serialized_start=115884
  _GAMEUSEREVENT._serialized_end=115927
  _GAMEACTION._serialized_start=115930
  _GAMEACTION._serialized_end=116086
  _OPTIONALOPERATION._serialized_start=116088
  _OPTIONALOPERATION._serialized_end=116210
  _OPTIONALOPERATIONLIST._serialized_start=116212
  _OPTIONALOPERATIONLIST._serialized_end=116334
  _LIQISUCCESS._serialized_start=116336
  _LIQISUCCESS._serialized_end=116446
  _FANINFO._serialized_start=116448
  _FANINFO._serialized_end=116496
  _HULEINFO._serialized_start=116499
  _HULEINFO._serialized_end=116983
  _TINGPAIINFO._serialized_start=116986
  _TINGPAIINFO._serialized_end=117153
  _TINGPAIDISCARDINFO._serialized_start=117155
  _TINGPAIDISCARDINFO._serialized_end=117239
  _HUNZHIYIJIBUFFINFO._serialized_start=117241
  _HUNZHIYIJIBUFFINFO._serialized_end=117322
  _GAMEEND._serialized_start=117324
  _GAMEEND._serialized_end=117349
  _ACTIONSELECTGAP._serialized_start=117352
  _ACTIONSELECTGAP._serialized_end=117513
  _RECORDSELECTGAP._serialized_start=117516
  _RECORDSELECTGAP._serialized_end=117705
  _RECORDSELECTGAP_TINGPAI._serialized_start=114752
  _RECORDSELECTGAP_TINGPAI._serialized_end=114811
  _ACTIONCHANGETILE._serialized_start=117708
  _ACTIONCHANGETILE._serialized_end=117973
  _RECORDCHANGETILE._serialized_start=117976
  _RECORDCHANGETILE._serialized_end=118391
  _RECORDCHANGETILE_TINGPAI._serialized_start=114752
  _RECORDCHANGETILE_TINGPAI._serialized_end=114811
  _RECORDCHANGETILE_CHANGETILE._serialized_start=118293
  _RECORDCHANGETILE_CHANGETILE._serialized_end=118391
  _ACTIONREVEALTILE._serialized_start=118394
  _ACTIONREVEALTILE._serialized_end=118623
  _RECORDREVEALTILE._serialized_start=118626
  _RECORDREVEALTILE._serialized_end=118856
  _ACTIONUNVEILTILE._serialized_start=118858
  _ACTIONUNVEILTILE._serialized_end=118970
  _RECORDUNVEILTILE._serialized_start=118972
  _RECORDUNVEILTILE._serialized_end=119084
  _ACTIONLOCKTILE._serialized_start=119087
  _ACTIONLOCKTILE._serialized_end=119299
  _RECORDLOCKTILE._serialized_start=119302
  _RECORDLOCKTILE._serialized_end=119515
  _ACTIONDISCARDTILE._serialized_start=119518
  _ACTIONDISCARDTILE._serialized_end=119953
  _RECORDDISCARDTILE._serialized_start=119956
  _RECORDDISCARDTILE._serialized_end=120340
  _ACTIONDEALTILE._serialized_start=120343
  _ACTIONDEALTILE._serialized_end=120684
  _RECORDDEALTILE._serialized_start=120687
  _RECORDDEALTILE._serialized_end=120986
  _ACTIONFILLAWAITINGTILES._serialized_start=120989
  _ACTIONFILLAWAITINGTILES._serialized_end=121140
  _RECORDFILLAWAITINGTILES._serialized_start=121143
  _RECORDFILLAWAITINGTILES._serialized_end=121294
  _ACTIONCHIPENGGANG._serialized_start=121297
  _ACTIONCHIPENGGANG._serialized_end=121684
  _RECORDCHIPENGGANG._serialized_start=121687
  _RECORDCHIPENGGANG._serialized_end=122032
  _ACTIONGANGRESULT._serialized_start=122034
  _ACTIONGANGRESULT._serialized_end=122089
  _RECORDGANGRESULT._serialized_start=122091
  _RECORDGANGRESULT._serialized_end=122146
  _ACTIONGANGRESULTEND._serialized_start=122148
  _ACTIONGANGRESULTEND._serialized_end=122206
  _RECORDGANGRESULTEND._serialized_start=122208
  _RECORDGANGRESULTEND._serialized_end=122266
  _ACTIONANGANGADDGANG._serialized_start=122269
  _ACTIONANGANGADDGANG._serialized_end=122475
  _RECORDANGANGADDGANG._serialized_start=122478
  _RECORDANGANGADDGANG._serialized_end=122632
  _ACTIONBABEI._serialized_start=122635
  _ACTIONBABEI._serialized_end=122839
  _RECORDBABEI._serialized_start=122842
  _RECORDBABEI._serialized_end=122994
  _ACTIONHULE._serialized_start=122997
  _ACTIONHULE._serialized_end=123259
  _RECORDHULE._serialized_start=123262
  _RECORDHULE._serialized_end=123524
  _HUINFOXUEZHANMID._serialized_start=123527
  _HUINFOXUEZHANMID._serialized_end=123725
  _ACTIONHULEXUEZHANMID._serialized_start=123728
  _ACTIONHULEXUEZHANMID._serialized_end=123937
  _RECORDHULEXUEZHANMID._serialized_start=123940
  _RECORDHULEXUEZHANMID._serialized_end=124149
  _ACTIONHULEXUEZHANEND._serialized_start=124152
  _ACTIONHULEXUEZHANEND._serialized_end=124401
  _RECORDHULEXUEZHANEND._serialized_start=124404
  _RECORDHULEXUEZHANEND._serialized_end=124653
  _ACTIONLIUJU._serialized_start=124656
  _ACTIONLIUJU._serialized_end=124862
  _RECORDLIUJU._serialized_start=124865
  _RECORDLIUJU._serialized_end=125071
  _NOTILEPLAYERINFO._serialized_start=125073
  _NOTILEPLAYERINFO._serialized_end=125176
  _NOTILESCOREINFO._serialized_start=125179
  _NOTILESCOREINFO._serialized_end=125340
  _ACTIONNOTILE._serialized_start=125343
  _ACTIONNOTILE._serialized_end=125537
  _RECORDNOTILE._serialized_start=125540
  _RECORDNOTILE._serialized_end=125734
  _PLAYERLEAVING._serialized_start=125736
  _PLAYERLEAVING._serialized_end=125765
  _REQREQUESTCONNECTION._serialized_start=125767
  _REQREQUESTCONNECTION._serialized_end=125840
  _RESREQUESTCONNECTION._serialized_start=125842
  _RESREQUESTCONNECTION._serialized_end=125925
  _REQREQUESTROUTECHANGE._serialized_start=125927
  _REQREQUESTROUTECHANGE._serialized_end=125998
  _RESREQUESTROUTECHANGE._serialized_start=126000
  _RESREQUESTROUTECHANGE._serialized_end=126065
  _REQHEARTBEAT._serialized_start=126067
  _REQHEARTBEAT._serialized_end=126169
  _RESHEARTBEAT._serialized_start=126171
  _RESHEARTBEAT._serialized_end=126211
  _LOBBY._serialized_start=126278
  _LOBBY._serialized_end=153645
  _FASTTEST._serialized_start=153648
  _FASTTEST._serialized_end=154533
  _ROUTE._serialized_start=154536
  _ROUTE._serialized_end=154741
# @@protoc_insertion_point(module_scope)

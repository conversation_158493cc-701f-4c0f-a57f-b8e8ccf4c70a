# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sheets.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0csheets.proto\"\x9d\x02\n\x10\x41\x62MatchMatchInfo\x12\n\n\x02id\x18\x01 \x01(\r\x12\x1c\n\x14\x61\x62_match_activity_id\x18\x02 \x01(\r\x12\x19\n\x11match_activity_id\x18\x03 \x01(\r\x12\x17\n\x0f\x64\x65sktop_id_list\x18\x04 \x01(\t\x12\x12\n\nconsume_id\x18\x05 \x01(\r\x12\x18\n\x10\x62uy_in_condition\x18\x06 \x01(\t\x12\x18\n\x10mail_template_id\x18\x07 \x01(\r\x12\x17\n\x0fmax_match_count\x18\x08 \x01(\r\x12\x11\n\treward_id\x18\t \x01(\r\x12\x10\n\x08point_id\x18\n \x01(\r\x12\x13\n\x0bmatch_level\x18\x0b \x01(\r\x12\x10\n\x08priority\x18\x0c \x01(\r\"P\n\x0c\x41\x62MatchPoint\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04rank\x18\x02 \x01(\r\x12\x17\n\x0f\x64\x65sktop_id_list\x18\x03 \x01(\t\x12\r\n\x05point\x18\x04 \x01(\r\"l\n\x10\x41\x62MatchRewardSeq\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0bpoint_lower\x18\x02 \x01(\r\x12\x13\n\x0bpoint_upper\x18\x03 \x01(\r\x12\x0e\n\x06reward\x18\x04 \x01(\t\x12\x12\n\nchest_mark\x18\x05 \x01(\r\"Y\n\x11\x41\x62MatchConsumeSeq\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0bmatch_count\x18\x02 \x01(\r\x12\x0f\n\x07item_id\x18\x03 \x01(\r\x12\x12\n\nitem_count\x18\x04 \x01(\r\"\xef\x02\n\x16\x41\x63hievementAchievement\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x07 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x08 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0b \x01(\t\x12\x0c\n\x04rare\x18\x0c \x01(\r\x12\x0e\n\x06locked\x18\r \x01(\r\x12\x10\n\x08group_id\x18\x0e \x01(\r\x12\x0c\n\x04sort\x18\x0f \x01(\r\x12\x12\n\nsegment_id\x18\x10 \x01(\r\x12\x11\n\tbase_task\x18\x11 \x01(\r\x12\x0e\n\x06reward\x18\x12 \x01(\t\x12\x0e\n\x06hidden\x18\x13 \x01(\r\x12\x12\n\ndeprecated\x18\x14 \x01(\r\"\xd5\x01\n\x1b\x41\x63hievementAchievementGroup\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x0b\n\x03img\x18\x07 \x01(\t\x12\x0e\n\x06reward\x18\x08 \x01(\t\x12\x12\n\npercentage\x18\t \x01(\r\x12\x12\n\ndeprecated\x18\n \x01(\r\x12\x0c\n\x04sort\x18\x0b \x01(\r\"\x9a\x01\n\x10\x41\x63tivityActivity\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\t \x01(\t\x12\x13\n\x0bneed_popout\x18\n \x01(\r\"\xb8\x01\n\x0c\x41\x63tivityTask\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x0b\n\x03\x64\x61y\x18\x03 \x01(\r\x12\x14\n\x0c\x62\x61se_task_id\x18\x04 \x01(\r\x12\x11\n\treward_id\x18\x05 \x01(\r\x12\x14\n\x0creward_count\x18\x06 \x01(\r\x12\x15\n\rhidden_reward\x18\x07 \x01(\t\x12\x10\n\x08limit_id\x18\x08 \x01(\r\x12\x12\n\ndeprecated\x18\t \x01(\r\"\xd0\x01\n\x10\x41\x63tivityExchange\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x11\n\treward_id\x18\x03 \x01(\r\x12\x14\n\x0creward_count\x18\x04 \x01(\r\x12\x12\n\nconsume_id\x18\x05 \x01(\r\x12\x15\n\rconsume_count\x18\x06 \x01(\r\x12\x16\n\x0e\x65xchange_limit\x18\x07 \x01(\x05\x12\x15\n\ritem_limit_id\x18\x08 \x01(\r\x12\x18\n\x10item_limit_count\x18\t \x01(\r\"\xe3\x01\n\x0f\x41\x63tivityChestUp\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08\x63hest_id\x18\x02 \x01(\r\x12\x14\n\x0ctitle_str_id\x18\x04 \x01(\r\x12\x0e\n\x06str_id\x18\x05 \x01(\t\x12\x14\n\x0c\x63hara_str_id\x18\x06 \x01(\r\x12\x13\n\x0bitem_str_id\x18\x07 \x01(\r\x12\x0b\n\x03img\x18\x08 \x01(\t\x12\x11\n\ttitle_img\x18\t \x01(\t\x12\x0f\n\x07typeset\x18\n \x01(\r\x12\x15\n\rup_items_type\x18\x0b \x01(\r\x12\x10\n\x08up_items\x18\x0c \x03(\r\"\xaf\x01\n\x10\x41\x63tivityGameTask\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x14\n\x0c\x62\x61se_task_id\x18\x03 \x01(\r\x12\x11\n\treward_id\x18\x04 \x01(\r\x12\x14\n\x0creward_count\x18\x05 \x01(\r\x12\x15\n\rhidden_reward\x18\x06 \x01(\t\x12\x10\n\x08limit_id\x18\x07 \x01(\r\x12\x12\n\ndeprecated\x18\x08 \x01(\r\"\xb3\x01\n\x11\x41\x63tivityGamePoint\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x10\n\x08point_id\x18\x03 \x01(\r\x12\r\n\x05point\x18\x04 \x01(\r\x12\x0e\n\x06res_id\x18\x05 \x01(\r\x12\x11\n\tres_count\x18\x06 \x01(\r\x12\x12\n\nunlock_day\x18\x07 \x01(\x05\x12\x11\n\tnode_mark\x18\x08 \x01(\r\x12\x12\n\nimage_mark\x18\t \x01(\r\"j\n\x0c\x41\x63tivityRank\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x16\n\x0eleaderboard_id\x18\x02 \x01(\r\x12\x16\n\x0erank_reward_id\x18\x03 \x01(\r\x12\x15\n\rrequire_point\x18\x04 \x01(\r\"J\n\x12\x41\x63tivityRankReward\x12\n\n\x02id\x18\x01 \x01(\r\x12\x18\n\x10lower_rank_bound\x18\x02 \x01(\r\x12\x0e\n\x06reward\x18\x03 \x01(\t\"\x90\x01\n\x10\x41\x63tivityFlipTask\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x14\n\x0c\x62\x61se_task_id\x18\x03 \x01(\r\x12\x0e\n\x06reward\x18\x04 \x01(\t\x12\x10\n\x08matrix_x\x18\x05 \x01(\r\x12\x10\n\x08matrix_y\x18\x06 \x01(\r\x12\x11\n\tis_reward\x18\x07 \x01(\r\"p\n\x10\x41\x63tivityFlipInfo\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\nflip_count\x18\x02 \x01(\r\x12\x16\n\x0einit_task_list\x18\x03 \x01(\t\x12\x12\n\nstart_time\x18\x04 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x05 \x01(\t\"^\n\x11\x41\x63tivityDailySign\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\treward_id\x18\x02 \x01(\r\x12\x14\n\x0creward_count\x18\x03 \x01(\r\x12\x0b\n\x03\x64\x61y\x18\x04 \x01(\r\"\xe6\x02\n\x13\x41\x63tivityRichmanInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0e\n\x06map_id\x18\x02 \x01(\r\x12\x14\n\x0cmap_distance\x18\x03 \x01(\r\x12\x10\n\x08\x63hest_id\x18\x04 \x01(\r\x12\x17\n\x0f\x63onsume_item_id\x18\x05 \x01(\r\x12\x17\n\x0fspecial_item_id\x18\x06 \x01(\r\x12\x16\n\x0estep_bank_save\x18\x07 \x01(\r\x12\x18\n\x10\x66inish_bank_save\x18\x08 \x01(\r\x12\x19\n\x11\x66inish_reward_seq\x18\t \x01(\r\x12\x10\n\x08step_exp\x18\n \x01(\r\x12\x12\n\nfinish_exp\x18\x0b \x01(\r\x12\x17\n\x0fitem_worth_pool\x18\x0c \x01(\r\x12\x15\n\rmin_avg_worth\x18\r \x01(\r\x12\x15\n\rmax_avg_worth\x18\x0e \x01(\r\x12\x16\n\x0e\x63hest_pool_seq\x18\x0f \x01(\r\"\xa8\x01\n\x12\x41\x63tivityRichmanMap\x12\x0e\n\x06map_id\x18\x01 \x01(\r\x12\x10\n\x08location\x18\x02 \x01(\r\x12\r\n\x05pos_x\x18\x03 \x01(\r\x12\r\n\x05pos_y\x18\x04 \x01(\r\x12\x12\n\npiece_face\x18\x05 \x01(\r\x12\x0c\n\x04type\x18\x06 \x01(\r\x12\r\n\x05param\x18\x07 \x01(\r\x12\x12\n\nbonus_type\x18\x08 \x01(\r\x12\r\n\x05worth\x18\t \x01(\r\"`\n\x14\x41\x63tivityRichmanLevel\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\r\x12\x0b\n\x03\x65xp\x18\x04 \x01(\r\x12\x0c\n\x04\x62uff\x18\x05 \x01(\r\"p\n\x14\x41\x63tivityRichmanEvent\x12\x10\n\x08\x65vent_id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x12\n\nevent_type\x18\x03 \x01(\r\x12\x0e\n\x06weight\x18\x04 \x01(\r\x12\r\n\x05param\x18\x05 \x03(\r\"\xfb\x01\n\x12\x41\x63tivityPeriodTask\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x14\n\x0c\x62\x61se_task_id\x18\x03 \x01(\r\x12\x0e\n\x06reward\x18\x04 \x01(\t\x12\x10\n\x08interval\x18\x05 \x01(\r\x12\x16\n\x0eprogress_limit\x18\x06 \x01(\r\x12\x1f\n\x17progress_limit_interval\x18\x07 \x01(\r\x12\x18\n\x10reward_limit_day\x18\x08 \x01(\r\x12\x12\n\nunlock_day\x18\t \x01(\r\x12\x12\n\ndeprecated\x18\n \x01(\r\x12\x11\n\tnode_mark\x18\x0b \x01(\r\"\xc7\x01\n\x16\x41\x63tivityRandomTaskPool\x12\x0f\n\x07pool_id\x18\x01 \x01(\r\x12\x0f\n\x07task_id\x18\x02 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x03 \x01(\r\x12\x14\n\x0c\x62\x61se_task_id\x18\x04 \x01(\r\x12\x11\n\treward_id\x18\x05 \x01(\r\x12\x14\n\x0creward_count\x18\x06 \x01(\r\x12\x0e\n\x06weight\x18\x07 \x01(\r\x12\x15\n\rhidden_reward\x18\x08 \x01(\t\x12\x10\n\x08limit_id\x18\t \x01(\r\">\n\x16\x41\x63tivityRandomTaskInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07pool_id\x18\x02 \x03(\r\"E\n\x18\x41\x63tivityRichmanRewardSeq\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0e\n\x06reward\x18\x03 \x01(\t\"\xb0\x01\n\x14\x41\x63tivityActivityBuff\x12\x0f\n\x07\x62uff_id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x12\n\nbuff_level\x18\x03 \x01(\r\x12\x11\n\tbuff_type\x18\x04 \x01(\r\x12\x1b\n\x13upgrade_resource_id\x18\x05 \x01(\r\x12\x1e\n\x16upgrade_resource_count\x18\x06 \x01(\r\x12\x0e\n\x06\x65\x66\x66\x65\x63t\x18\x07 \x01(\r\"\x17\n\x15\x41\x63tivityBuffCondition\"\x8f\x01\n\x15\x41\x63tivityGamePointInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\tfilter_id\x18\x02 \x01(\r\x12\x1c\n\x14reward_mail_template\x18\x03 \x01(\r\x12\x1b\n\x13max_point_limit_day\x18\x04 \x01(\x05\x12\x13\n\x0bshould_rank\x18\x05 \x01(\r\"n\n\x15\x41\x63tivityGamePointRank\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x17\n\x0frank_rate_lower\x18\x02 \x01(\r\x12\x17\n\x0frank_rate_upper\x18\x03 \x01(\r\x12\x0e\n\x06reward\x18\x04 \x01(\t\"y\n\x17\x41\x63tivityGamePointFilter\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\thas_robot\x18\x02 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x03 \x01(\t\x12\x0c\n\x04room\x18\x04 \x01(\t\x12\x0c\n\x04mode\x18\x05 \x01(\t\x12\x11\n\tpoint_coe\x18\x06 \x01(\r\"\xef\x02\n\x14\x41\x63tivityActivityRoom\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04sort\x18\x02 \x01(\r\x12\x10\n\x08str_name\x18\x03 \x01(\t\x12\x10\n\x08str_rule\x18\x04 \x01(\t\x12\x12\n\ndora3_mode\x18\x05 \x01(\r\x12\x17\n\x0f\x62\x65gin_open_mode\x18\x06 \x01(\r\x12\x11\n\tmuyu_mode\x18\x07 \x01(\r\x12\x14\n\x0cxuezhan_mode\x18\x08 \x01(\r\x12\x16\n\x0ehuanzhang_mode\x18\t \x01(\r\x12\x14\n\x0c\x63huanma_mode\x18\n \x01(\r\x12\x14\n\x0cjiuchao_mode\x18\x0b \x01(\r\x12\x16\n\x0ereveal_discard\x18\x0c \x01(\r\x12\x18\n\x10\x66ield_spell_mode\x18\r \x01(\r\x12\x15\n\rzhanxing_mode\x18\x0e \x01(\r\x12\x15\n\rtianming_mode\x18\x0f \x01(\r\x12\x16\n\x0eyongchang_mode\x18\x10 \x01(\r\"\xe6\x02\n\x13\x41\x63tivitySnsActivity\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07\x64isable\x18\x02 \x01(\r\x12\n\n\x02pm\x18\x03 \x01(\r\x12\x0e\n\x06period\x18\x04 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x05 \x01(\r\x12\x16\n\x0e\x63ontent_str_id\x18\x06 \x01(\r\x12\x11\n\tparent_id\x18\x07 \x01(\r\x12\x0f\n\x07\x63har_id\x18\x08 \x01(\r\x12\x13\n\x0b\x63har_str_id\x18\t \x01(\r\x12\x15\n\rreply_char_id\x18\n \x01(\r\x12\x19\n\x11reply_char_str_id\x18\x0b \x01(\r\x12\x11\n\tchoice_id\x18\x0c \x01(\r\x12\x0c\n\x04like\x18\r \x01(\r\x12\x13\n\x0bunlock_time\x18\x0e \x01(\t\x12\x16\n\x0eunlock_item_id\x18\x0f \x01(\r\x12\x19\n\x11unlock_item_count\x18\x10 \x01(\r\x12\x15\n\rcontent_image\x18\x11 \x03(\t\"|\n\x14\x41\x63tivityMineActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x14\n\x0creward_group\x18\x02 \x01(\r\x12\x11\n\tcost_item\x18\x03 \x01(\t\x12\x12\n\nmap_size_x\x18\x04 \x01(\r\x12\x12\n\nmap_size_y\x18\x05 \x01(\r\"m\n\x12\x41\x63tivityMineReward\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\x11\n\treward_id\x18\x02 \x01(\r\x12\x0e\n\x06reward\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\r\x12\t\n\x01x\x18\x05 \x01(\r\x12\t\n\x01y\x18\x06 \x01(\r\"\xad\x02\n\x13\x41\x63tivityRpgActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07\x62\x61se_hp\x18\x02 \x01(\r\x12\x10\n\x08\x62\x61se_atk\x18\x03 \x01(\r\x12\x10\n\x08\x62\x61se_dex\x18\x04 \x01(\r\x12\x10\n\x08\x62\x61se_luk\x18\x05 \x01(\r\x12\x0e\n\x06\x64s_atk\x18\x06 \x01(\r\x12\x14\n\x0cspecial_heal\x18\x07 \x01(\r\x12\x11\n\tchain_atk\x18\x08 \x03(\r\x12\x15\n\rmonster_group\x18\t \x01(\r\x12\x14\n\x0csanma_debuff\x18\n \x01(\r\x12\x11\n\thas_robot\x18\x0b \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x0c \x01(\t\x12\x0c\n\x04mode\x18\r \x01(\t\x12\x0c\n\x04room\x18\x0e \x01(\t\x12\x13\n\x0b\x64\x61ily_limit\x18\x0f \x01(\r\"\xcf\x01\n\x17\x41\x63tivityRpgMonsterGroup\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\x0b\n\x03seq\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\x12\n\n\x02hp\x18\x04 \x01(\r\x12\x0b\n\x03\x61tk\x18\x05 \x01(\r\x12\x0e\n\x06reward\x18\x06 \x01(\t\x12\x0e\n\x06season\x18\x07 \x01(\r\x12\x10\n\x08\x63hapters\x18\x08 \x01(\t\x12\x13\n\x0bimaget_path\x18\t \x01(\t\x12\x12\n\nbackground\x18\n \x01(\t\x12\x13\n\x0bname_str_id\x18\x0b \x01(\r\"\xce\x02\n\x15\x41\x63tivityArenaActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\nmatch_time\x18\x02 \x01(\t\x12\x19\n\x11ticket_time_limit\x18\x03 \x01(\t\x12\x16\n\x0eticket_item_id\x18\x04 \x01(\r\x12\x14\n\x0cticket_price\x18\x05 \x01(\t\x12\x12\n\ndesktop_id\x18\x06 \x01(\r\x12\x15\n\rmax_win_count\x18\x07 \x01(\r\x12\x16\n\x0emax_lose_count\x18\x08 \x01(\r\x12\x14\n\x0creward_group\x18\t \x01(\r\x12\x1a\n\x12\x64\x61ily_ticket_limit\x18\n \x01(\r\x12\x15\n\rmail_template\x18\x0b \x01(\r\x12\"\n\x1a\x61rena_reward_display_group\x18\x0c \x01(\r\x12\x13\n\x0blevel_limit\x18\r \x01(\r\"J\n\x13\x41\x63tivityArenaReward\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\x11\n\twin_count\x18\x02 \x01(\r\x12\x0e\n\x06reward\x18\x03 \x01(\t\"\x88\x02\n\x1a\x41\x63tivityArenaRewardDisplay\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\x15\n\rwin_count_min\x18\x02 \x01(\r\x12\x15\n\rwin_count_max\x18\x03 \x01(\r\x12\x10\n\x08reward_1\x18\x04 \x01(\r\x12\x17\n\x0freward_1_remark\x18\x05 \x01(\t\x12\x10\n\x08reward_2\x18\x06 \x01(\r\x12\x17\n\x0freward_2_remark\x18\x07 \x01(\t\x12\x10\n\x08reward_3\x18\x08 \x01(\r\x12\x17\n\x0freward_3_remark\x18\t \x01(\t\x12\x10\n\x08reward_4\x18\n \x01(\r\x12\x17\n\x0freward_4_remark\x18\x0b \x01(\t\"\x88\x01\n\x13\x41\x63tivitySegmentTask\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x14\n\x0c\x62\x61se_task_id\x18\x03 \x01(\r\x12\x18\n\x10max_finish_count\x18\x04 \x01(\r\x12\x0e\n\x06reward\x18\x05 \x01(\t\x12\x10\n\x08interval\x18\x06 \x01(\r\"\xab\x01\n\x18\x41\x63tivityFeedActivityInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x16\n\x0emax_feed_count\x18\x02 \x01(\r\x12\x16\n\x0e\x66\x65\x65\x64_reward_id\x18\x03 \x01(\r\x12\x19\n\x11\x66riend_send_limit\x18\x04 \x01(\r\x12\x19\n\x11\x66riend_recv_limit\x18\x05 \x01(\r\x12\x14\n\x0c\x66ood_item_id\x18\x06 \x03(\r\"Z\n\x1a\x41\x63tivityFeedActivityReward\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0e\n\x06reward\x18\x03 \x01(\t\x12\x11\n\timg_stage\x18\x04 \x01(\r\"d\n\x14\x41\x63tivityVoteActivity\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\tvote_item\x18\x02 \x01(\r\x12\x16\n\x0e\x63hoice_id_list\x18\x03 \x01(\t\x12\x15\n\rvote_end_time\x18\x04 \x01(\t\"\xf3\x01\n\x15\x41\x63tivityRpgV2Activity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x10\n\x08\x62\x61se_atk\x18\x02 \x01(\r\x12\x15\n\rmonster_group\x18\x03 \x01(\r\x12\x14\n\x0csanma_debuff\x18\x04 \x01(\r\x12\x16\n\x0especial_debuff\x18\x05 \x01(\r\x12\x11\n\thas_robot\x18\x06 \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x07 \x01(\t\x12\x0c\n\x04mode\x18\x08 \x01(\t\x12\x0c\n\x04room\x18\t \x01(\t\x12\x13\n\x0b\x64\x61ily_limit\x18\n \x01(\r\x12\x18\n\x10mail_template_id\x18\x0b \x01(\r\"\xa8\x02\n\x14\x41\x63tivitySpotActivity\x12\x11\n\tunique_id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x0b\n\x03\x64\x65p\x18\x03 \x01(\r\x12\x11\n\tlimit_day\x18\x04 \x01(\r\x12\x12\n\nspot_group\x18\x05 \x01(\r\x12\x18\n\x10unlock_spot_item\x18\x06 \x01(\t\x12\x14\n\x0c\x63ontent_path\x18\x07 \x01(\t\x12\x13\n\x0bunlock_item\x18\x08 \x01(\t\x12\x1b\n\x13unlock_by_ending_id\x18\t \x01(\r\x12\x18\n\x10unlock_ending_id\x18\n \x03(\t\x12\x11\n\tending_id\x18\x0b \x03(\r\x12\x15\n\rending_id_dep\x18\x0c \x03(\r\x12\x0e\n\x06reward\x18\r \x01(\t\">\n\x14\x41\x63tivityActivityItem\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\titem_list\x18\x02 \x01(\t\"\x81\x01\n\x17\x41\x63tivityUpgradeActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0f\n\x07mail_id\x18\x02 \x01(\r\x12\x14\n\x0c\x63onsume_item\x18\x03 \x01(\t\x12\x17\n\x0ftotal_reward_id\x18\x04 \x01(\r\x12\x11\n\treward_id\x18\x05 \x03(\r\"q\n\x1d\x41\x63tivityUpgradeActivityReward\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0e\n\x06reward\x18\x03 \x01(\t\x12\x12\n\nunlock_day\x18\x04 \x01(\r\x12\x11\n\thighlight\x18\x05 \x01(\r\"\xae\x01\n\x1a\x41\x63tivityFriendGiftActivity\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x19\n\x11\x66riend_send_limit\x18\x02 \x01(\r\x12\x1b\n\x13\x66riend_send_consume\x18\x03 \x01(\r\x12\x19\n\x11\x66riend_recv_limit\x18\x04 \x01(\r\x12\x12\n\nextra_gift\x18\x05 \x01(\r\x12\x14\n\x0cgift_item_id\x18\x06 \x03(\r\"L\n\x1e\x41\x63tivityUpgradeActivityDisplay\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0f\n\x07\x64isplay\x18\x03 \x01(\r\"k\n\x17\x41\x63tivityActivityDesktop\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\ndesktop_id\x18\x02 \x01(\r\x12\x10\n\x08interval\x18\x03 \x01(\r\x12\x15\n\rinterval_type\x18\x04 \x01(\r\"\x9a\x01\n\x19\x41\x63tivityGachaActivityInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\ngacha_pool\x18\x02 \x01(\r\x12\x15\n\rgacha_control\x18\x03 \x01(\r\x12\x18\n\x10sp_trigger_times\x18\x04 \x01(\r\x12\x12\n\nsp_rewards\x18\x05 \x01(\t\x12\x0f\n\x07\x63onsume\x18\x06 \x01(\t\"b\n\x11\x41\x63tivityGachaPool\x12\x0f\n\x07pool_id\x18\x01 \x01(\r\x12\x11\n\treward_id\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\x0c\n\x04rare\x18\x04 \x01(\r\x12\x0c\n\x04item\x18\x05 \x01(\t\"\x16\n\x14\x41\x63tivityGachaControl\"\xb4\x01\n\x13\x41\x63tivityTaskDisplay\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0b\n\x03\x64\x61y\x18\x02 \x01(\r\x12\x1a\n\x12task_serial_number\x18\x03 \x01(\r\x12\x11\n\ttask_type\x18\x04 \x01(\r\x12\x16\n\x0eperiod_task_id\x18\x05 \x01(\r\x12\x0e\n\x06\x61nswer\x18\x06 \x01(\r\x12\x11\n\tright_str\x18\x07 \x01(\r\x12\x11\n\twrong_str\x18\x08 \x01(\r\"N\n\x1e\x41\x63tivitySimulationActivityInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x17\n\x0fstamina_item_id\x18\x02 \x01(\r\"C\n\x12\x41\x63tivityRewardMail\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x18\n\x10mail_template_id\x18\x02 \x01(\r\"\x8e\x01\n\x1d\x41\x63tivityCombiningActivityInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x11\n\tcraft_bin\x18\x02 \x03(\r\x12\x17\n\x0f\x63raft_bin_price\x18\x03 \x03(\t\x12\x18\n\x10\x63raft_bin_unlock\x18\x04 \x03(\r\x12\x12\n\npoint_item\x18\x05 \x01(\r\">\n\x1a\x41\x63tivityCombiningCraftPool\x12\x0e\n\x06\x62in_id\x18\x01 \x01(\r\x12\x10\n\x08\x63raft_id\x18\x02 \x01(\r\"u\n\x14\x41\x63tivityCombiningMap\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x15\n\rpoint_item_id\x18\x02 \x01(\r\x12\x18\n\x10point_item_count\x18\x03 \x01(\r\x12\x17\n\x0fworkbench_count\x18\x04 \x01(\r\"\x18\n\x16\x41\x63tivityCombiningOrder\"\xaa\x01\n\x16\x41\x63tivityCombiningCraft\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\r\n\x05group\x18\x03 \x01(\r\x12\r\n\x05level\x18\x05 \x01(\r\x12\x18\n\x10upgrade_craft_id\x18\x06 \x01(\r\x12\x13\n\x0border_price\x18\x08 \x01(\t\x12\x10\n\x08if_bonus\x18\t \x01(\r\x12\x10\n\x08img_name\x18\n \x01(\t\"*\n\x16\x41\x63tivityChestReplaceUp\x12\x10\n\x08\x63hest_id\x18\x02 \x01(\r\"\xfe\x01\n\x1b\x41\x63tivityVillageActivityInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x16\n\x0eworker_item_id\x18\x02 \x01(\r\x12\x15\n\rround_consume\x18\x03 \x01(\t\x12\x17\n\x0frandom_building\x18\x04 \x01(\t\x12\x14\n\x0c\x66ood_item_id\x18\x05 \x01(\r\x12\x14\n\x0ctrip_consume\x18\x06 \x01(\r\x12\x16\n\x0etrip_cold_down\x18\x07 \x01(\r\x12\x12\n\ntrip_round\x18\x08 \x01(\r\x12\x13\n\x0btrip_reward\x18\t \x03(\t\x12\x15\n\rstage_require\x18\n \x03(\t\"\xdf\x02\n\x17\x41\x63tivityVillageBuilding\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x13\n\x0b\x62uilding_id\x18\x02 \x01(\r\x12\x0f\n\x07initial\x18\x03 \x01(\r\x12\x15\n\rbuilding_name\x18\x04 \x01(\r\x12\r\n\x05level\x18\x05 \x01(\r\x12\x15\n\rnext_level_id\x18\x06 \x01(\r\x12\x14\n\x0cproduce_item\x18\x07 \x01(\t\x12\x14\n\x0c\x62\x61se_produce\x18\x08 \x01(\t\x12\x14\n\x0cupgrade_item\x18\t \x01(\t\x12\x18\n\x10max_worker_count\x18\n \x01(\r\x12\x16\n\x0eupgrade_reward\x18\x0b \x01(\t\x12\x16\n\x0e\x62uilding_stage\x18\x0c \x01(\r\x12\x16\n\x0eworker_consume\x18\r \x01(\r\x12\x0c\n\x04\x66unc\x18\x0e \x01(\t\x12\x0c\n\x04\x61rgs\x18\x0f \x03(\r\x12\x0c\n\x04type\x18\x10 \x01(\r\"\xc3\x01\n\x13\x41\x63tivityVillageTask\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x12\n\nmission_id\x18\x02 \x01(\r\x12\x13\n\x0bmission_str\x18\x03 \x01(\r\x12\x12\n\nfruit_type\x18\x04 \x01(\t\x12\x0f\n\x07\x63onsume\x18\x05 \x01(\t\x12\x0e\n\x06reward\x18\x06 \x01(\t\x12\x12\n\nunlock_day\x18\x07 \x01(\r\x12\x14\n\x0cunlock_point\x18\x08 \x01(\t\x12\x0f\n\x07if_loop\x18\t \x01(\r\"\xd3\x02\n\x16\x41\x63tivityLiverEventInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x17\n\x0f\x66ollower_amount\x18\x02 \x01(\r\x12\x1b\n\x13\x64\x61ily_follower_plus\x18\x03 \x01(\r\x12\x12\n\nintro_text\x18\x04 \x01(\r\x12\x17\n\x0ftext_max_amount\x18\x05 \x01(\r\x12\x19\n\x11text_create_timer\x18\x06 \x01(\t\x12\x1a\n\x12text_create_weight\x18\x07 \x01(\t\x12\x16\n\x0erolltext_speed\x18\x08 \x01(\r\x12\x13\n\x0bgift_weight\x18\t \x01(\t\x12\x1a\n\x12rolltext_gift_time\x18\n \x01(\t\x12\x13\n\x0bkey_item_id\x18\x0b \x01(\r\x12\x17\n\x0f\x66\x61\x63\x65_time_block\x18\x0c \x01(\t\x12\x13\n\x0b\x66\x61\x63\x65_weight\x18\r \x01(\t\"\x88\x01\n\x15\x41\x63tivityLiverTextInfo\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x10\n\x08\x63hara_id\x18\x03 \x01(\r\x12\x12\n\nmob_str_id\x18\x04 \x01(\r\x12\x13\n\x0bnormal_text\x18\x05 \x01(\t\x12\x11\n\tgift_text\x18\x06 \x01(\t\"o\n\x12\x41nimationAnimation\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08lifetime\x18\x04 \x01(\r\x12\r\n\x05speed\x18\x05 \x01(\x02\x12\x10\n\x08keypoint\x18\x06 \x03(\r\"I\n\nAudioAudio\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x13\n\x0btime_length\x18\x03 \x01(\x02\x12\x0c\n\x04type\x18\x04 \x01(\t\"\xc4\x02\n\x08\x41udioBgm\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\tauto_hide\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x0f\n\x07name_jp\x18\x05 \x01(\t\x12\x0f\n\x07name_en\x18\x06 \x01(\t\x12\x0f\n\x07name_kr\x18\x07 \x01(\t\x12\x0c\n\x04path\x18\x08 \x01(\t\x12\x13\n\x0btime_length\x18\t \x01(\x02\x12\x0c\n\x04type\x18\n \x01(\t\x12\x17\n\x0funlock_desc_chs\x18\x0b \x01(\t\x12\x19\n\x11unlock_desc_chs_t\x18\x0c \x01(\t\x12\x16\n\x0eunlock_desc_jp\x18\r \x01(\t\x12\x16\n\x0eunlock_desc_en\x18\x0e \x01(\t\x12\x16\n\x0eunlock_desc_kr\x18\x0f \x01(\t\x12\x13\n\x0bunlock_item\x18\x10 \x01(\r\"\x9c\x03\n\x0e\x43haracterEmoji\x12\x0e\n\x06\x63harid\x18\x01 \x01(\r\x12\x0e\n\x06sub_id\x18\x02 \x01(\r\x12\x17\n\x0funlock_desc_chs\x18\x03 \x01(\t\x12\x19\n\x11unlock_desc_chs_t\x18\x04 \x01(\t\x12\x16\n\x0eunlock_desc_jp\x18\x05 \x01(\t\x12\x16\n\x0eunlock_desc_en\x18\x06 \x01(\t\x12\x16\n\x0eunlock_desc_kr\x18\x07 \x01(\t\x12\x0c\n\x04type\x18\x08 \x01(\r\x12\x0c\n\x04view\x18\t \x01(\t\x12\r\n\x05\x61udio\x18\n \x01(\r\x12\x1d\n\x15\x61\x66ter_unlock_desc_chs\x18\x0b \x01(\t\x12\x1f\n\x17\x61\x66ter_unlock_desc_chs_t\x18\x0c \x01(\t\x12\x1c\n\x14\x61\x66ter_unlock_desc_jp\x18\r \x01(\t\x12\x1c\n\x14\x61\x66ter_unlock_desc_en\x18\x0e \x01(\t\x12\x1c\n\x14\x61\x66ter_unlock_desc_kr\x18\x0f \x01(\t\x12\x13\n\x0bunlock_type\x18\x10 \x01(\r\x12\x14\n\x0cunlock_param\x18\x11 \x03(\r\"\x9c\x01\n\x0e\x43haracterCutin\x12\x0e\n\x06skinid\x18\x01 \x01(\r\x12\x12\n\ncutin_name\x18\x02 \x01(\t\x12\x0e\n\x06\x65\x66\x66\x65\x63t\x18\x03 \x01(\t\x12\r\n\x05\x61tlas\x18\x04 \x01(\t\x12\x0e\n\x06\x63har_x\x18\x05 \x01(\x05\x12\x0e\n\x06\x63har_y\x18\x06 \x01(\x05\x12\x12\n\nchar_width\x18\x07 \x01(\r\x12\x13\n\x0b\x63har_height\x18\x08 \x01(\r\"\xa0\x01\n\rCharacterSkin\x12\x0e\n\x06skinid\x18\x01 \x01(\r\x12\x14\n\x0cspine_layers\x18\x02 \x01(\r\x12\x0f\n\x07\x65\x66\x66\x65\x63ts\x18\x03 \x03(\t\x12\x17\n\x0f\x61udio_celebrate\x18\x04 \x01(\t\x12\x12\n\naudio_idle\x18\x05 \x01(\t\x12\x16\n\x0e\x61udio_greeting\x18\x06 \x01(\t\x12\x13\n\x0b\x61udio_click\x18\x07 \x01(\t\"\xc3\x02\n\nChestChest\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x06 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x07 \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x08 \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\t \x01(\t\x12\x0b\n\x03img\x18\n \x01(\t\x12\x0c\n\x04gift\x18\x0b \x01(\r\x12\x10\n\x08\x63urrency\x18\x0c \x01(\r\x12\r\n\x05price\x18\r \x01(\r\x12\x0f\n\x07price10\x18\x0e \x01(\r\x12\x11\n\tticket_id\x18\x0f \x01(\r\x12\x14\n\x0cticket_10_id\x18\x10 \x01(\r\x12\x10\n\x08\x66\x61ith_id\x18\x16 \x01(\r\x12\x0c\n\x04zone\x18\x17 \x01(\r\x12\x0c\n\x04sort\x18\x18 \x01(\r\"\x0b\n\tChestPool\"\x0e\n\x0c\x43hestPoolSeq\"\x0f\n\rChestItemPool\"\x8f\x02\n\x0e\x43hestChestShop\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x63hest_id\x18\x02 \x01(\r\x12\x0c\n\x04icon\x18\x03 \x01(\t\x12\x10\n\x08name_chs\x18\x04 \x01(\t\x12\x12\n\nname_chs_t\x18\x05 \x01(\t\x12\x0f\n\x07name_jp\x18\x06 \x01(\t\x12\x0f\n\x07name_en\x18\x07 \x01(\t\x12\x0f\n\x07item_id\x18\x08 \x01(\r\x12\r\n\x05price\x18\t \x01(\r\x12\x13\n\x0bneed_amount\x18\n \x01(\r\x12\x16\n\x0e\x63heck_activity\x18\x0b \x01(\r\x12\x13\n\x0blaunch_time\x18\x0c \x01(\t\x12\x19\n\x11remove_limit_mark\x18\r \x01(\r\x12\x0c\n\x04sort\x18\x0e \x01(\r\"W\n\x0c\x43hestPreview\x12\x10\n\x08\x63hest_id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x16\n\x0e\x63heck_activity\x18\x04 \x01(\r\"\t\n\x07\x43hestUp\"\x10\n\x0e\x43hestItemPrice\"y\n\x0e\x43hestReplaceUp\x12\n\n\x02id\x18\x01 \x01(\r\x12\x17\n\x0freplace_pool_id\x18\x02 \x01(\r\x12\r\n\x05\x63ount\x18\x03 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x05 \x01(\r\x12\x10\n\x08\x63ount_id\x18\x06 \x01(\r\x12\x0c\n\x04type\x18\x07 \x01(\r\"Z\n\x10\x43hestReplacePool\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0bresource_id\x18\x02 \x01(\r\x12\x12\n\nis_replace\x18\x04 \x01(\r\x12\x11\n\tadd_count\x18\x05 \x01(\r\"V\n\x13\x43omposeCharacompose\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\x10\n\x08item_num\x18\x03 \x01(\r\x12\x10\n\x08\x63hara_id\x18\x04 \x01(\r\"/\n\x0e\x43ontestContest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\tint_value\x18\x02 \x01(\x05\"\xf7\x07\n\x10\x44\x65sktopMatchmode\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07is_open\x18\x02 \x01(\r\x12\x13\n\x0bmatch_group\x18\x03 \x01(\r\x12\x0c\n\x04type\x18\x04 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x05 \x01(\r\x12\x11\n\topen_guyi\x18\x06 \x01(\r\x12\x12\n\ndora3_mode\x18\x07 \x01(\r\x12\x17\n\x0f\x62\x65gin_open_mode\x18\x08 \x01(\r\x12\x11\n\tmuyu_mode\x18\t \x01(\r\x12\x14\n\x0cxuezhan_mode\x18\n \x01(\r\x12\x14\n\x0c\x63huanma_mode\x18\x0b \x01(\r\x12\x16\n\x0ehuanzhang_mode\x18\x0c \x01(\r\x12\x14\n\x0cjiuchao_mode\x18\r \x01(\r\x12\x16\n\x0ereveal_discard\x18\x0e \x01(\r\x12\x18\n\x10\x66ield_spell_mode\x18\x0f \x01(\r\x12\x15\n\rzhanxing_mode\x18\x10 \x01(\r\x12\x15\n\rtianming_mode\x18\x11 \x01(\r\x12\x16\n\x0eyongchang_mode\x18\x12 \x01(\r\x12\x0c\n\x04room\x18\x13 \x01(\r\x12\x0c\n\x04mode\x18\x14 \x01(\r\x12\x11\n\tcan_sumup\x18\x15 \x01(\r\x12\x15\n\rroom_name_chs\x18\x16 \x01(\t\x12\x17\n\x0froom_name_chs_t\x18\x17 \x01(\t\x12\x14\n\x0croom_name_jp\x18\x18 \x01(\t\x12\x14\n\x0croom_name_en\x18\x19 \x01(\t\x12\x14\n\x0croom_name_kr\x18\x1a \x01(\t\x12\x14\n\x0cglimit_floor\x18\x1b \x01(\x05\x12\x13\n\x0bglimit_ceil\x18\x1c \x01(\x05\x12\x0e\n\x06gcarry\x18\x1d \x01(\x05\x12\x15\n\rexchange_rate\x18\x1e \x01(\x05\x12\x13\n\x0blevelpoint1\x18\x1f \x01(\x05\x12\x13\n\x0blevelpoint2\x18  \x01(\x05\x12\x13\n\x0blevelpoint3\x18! \x01(\x05\x12\x13\n\x0blevelpoint4\x18\" \x01(\x05\x12\x12\n\nfish_point\x18# \x01(\x05\x12\x12\n\ninit_point\x18$ \x01(\x05\x12\x12\n\nback_point\x18% \x01(\x05\x12\x13\n\x0b\x63ount_point\x18& \x01(\x05\x12\x0f\n\x07\x62uchang\x18\' \x03(\x05\x12\x13\n\x0blevel_limit\x18( \x01(\r\x12\x18\n\x10level_limit_ceil\x18) \x01(\r\x12\x0b\n\x03tip\x18* \x01(\x05\x12\x12\n\nfriendship\x18+ \x01(\x05\x12\x10\n\x08\x63hest_id\x18, \x01(\r\x12\x15\n\rchest_exp_add\x18- \x03(\r\x12\x13\n\x0blevel_match\x18. \x01(\r\x12\x19\n\x11level_match_range\x18/ \x01(\r\x12\x17\n\x0flevel_match_max\x18\x30 \x01(\r\"\xd0\x01\n\x0c\x44\x65sktopChest\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x65xp_step\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x0f\n\x07name_jp\x18\x05 \x01(\t\x12\x0f\n\x07name_en\x18\x06 \x01(\t\x12\x0f\n\x07name_kr\x18\x07 \x01(\t\x12\x0c\n\x04icon\x18\x08 \x01(\t\x12\x13\n\x0breward_pool\x18\t \x01(\r\x12\x14\n\x0cselect_count\x18\n \x01(\r\x12\x10\n\x08repeated\x18\x0b \x01(\r\"1\n\x0f\x44\x65sktopSettings\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x11\n\tint_value\x18\x02 \x01(\x05\"V\n\x11\x44\x65sktopFieldSpell\x12\r\n\x05\x66ield\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\x12\x10\n\x08\x63\x61rdname\x18\x04 \x01(\t\x12\x14\n\x0csord_card_id\x18\x05 \x01(\r\"\xc5\x01\n\rEventsSoscoin\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0blevel_limit\x18\x02 \x01(\r\x12\x14\n\x0clevel3_limit\x18\x03 \x01(\r\x12\x12\n\ngold_limit\x18\x04 \x01(\r\x12\x10\n\x08gold_num\x18\x05 \x01(\r\x12\x10\n\x08\x64\x65sc_chs\x18\x06 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x07 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x08 \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\n \x01(\t\"\xf7\x01\n\x10\x45ventsDailyevent\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0breward_type\x18\x02 \x01(\r\x12\x12\n\nreward_num\x18\x03 \x01(\r\x12\x10\n\x08\x64\x65sc_chs\x18\x04 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x05 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x06 \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x07 \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x08 \x01(\t\x12\x13\n\x0b\x61\x63tive_type\x18\t \x01(\r\x12\x0c\n\x04type\x18\x0b \x01(\r\x12\x0e\n\x06target\x18\x0c \x01(\r\x12\r\n\x05param\x18\r \x03(\t\x12\x13\n\x0blevel_limit\x18\x0e \x01(\t\"\xa2\x01\n\x0e\x45ventsBaseTask\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x64\x65sc_chs\x18\x02 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x04 \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x05 \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\r\x12\x0e\n\x06target\x18\x08 \x01(\r\x12\r\n\x05param\x18\t \x03(\t\"\xbc\x02\n\x10\x45xchangeExchange\x12\n\n\x02id\x18\x01 \x01(\r\x12\x17\n\x0fsource_currency\x18\x02 \x01(\r\x12\x14\n\x0csource_value\x18\x03 \x01(\x05\x12\x17\n\x0ftarget_currency\x18\x04 \x01(\r\x12\x14\n\x0ctarget_value\x18\x05 \x01(\x05\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x10\n\x08name_chs\x18\x07 \x01(\t\x12\x12\n\nname_chs_t\x18\x08 \x01(\t\x12\x0f\n\x07name_jp\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x0f\n\x07name_kr\x18\x0b \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x0c \x01(\t\x12\x12\n\ndesc_chs_t\x18\r \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0e \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0f \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x10 \x01(\t\"\xc3\x02\n\x16\x45xchangeSearchexchange\x12\n\n\x02id\x18\x01 \x01(\r\x12\x17\n\x0fsource_currency\x18\x02 \x01(\r\x12\x14\n\x0csource_value\x18\x03 \x01(\x05\x12\x17\n\x0ftarget_currency\x18\x04 \x01(\r\x12\x14\n\x0ctarget_value\x18\x05 \x01(\x05\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x10\n\x08name_chs\x18\x07 \x01(\t\x12\x12\n\nname_chs_t\x18\x08 \x01(\t\x12\x0f\n\x07name_jp\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x0f\n\x07name_kr\x18\x0b \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x0c \x01(\t\x12\x12\n\ndesc_chs_t\x18\r \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0e \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0f \x01(\t\x12\x10\n\x08\x64\x65sc_kor\x18\x10 \x01(\t\"\xc5\x02\n\x19\x45xchangeFushiquanexchange\x12\n\n\x02id\x18\x01 \x01(\r\x12\x17\n\x0fsource_currency\x18\x02 \x01(\r\x12\x14\n\x0csource_value\x18\x03 \x01(\x05\x12\x17\n\x0ftarget_currency\x18\x04 \x01(\r\x12\x14\n\x0ctarget_value\x18\x05 \x01(\x05\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x10\n\x08name_chs\x18\x07 \x01(\t\x12\x12\n\nname_chs_t\x18\x08 \x01(\t\x12\x0f\n\x07name_jp\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x0f\n\x07name_kr\x18\x0b \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x0c \x01(\t\x12\x12\n\ndesc_chs_t\x18\r \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0e \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0f \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x10 \x01(\t\"\xfa\x01\n\x06\x46\x61nFan\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x11\n\txuanshang\x18\x07 \x01(\r\x12\r\n\x05yiman\x18\x08 \x01(\r\x12\x13\n\x0b\x66\x61n_menqing\x18\t \x01(\r\x12\x10\n\x08\x66\x61n_fulu\x18\n \x01(\r\x12\x12\n\nshow_index\x18\x0b \x01(\r\x12\r\n\x05sound\x18\x0c \x01(\t\x12\x0f\n\x07is_guyi\x18\r \x01(\r\x12\x0e\n\x06rarity\x18\x0e \x01(\r\"\xe3\x02\n\x0e\x46\x61ndescFandesc\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03tag\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x0f\n\x07name_jp\x18\x05 \x01(\t\x12\x0f\n\x07name_en\x18\x06 \x01(\t\x12\x0f\n\x07name_kr\x18\x07 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x08 \x01(\t\x12\x12\n\ndesc_chs_t\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0c \x01(\t\x12\x11\n\tdesc2_chs\x18\r \x01(\t\x12\x10\n\x08\x64\x65sc2_jp\x18\x0e \x01(\t\x12\x10\n\x08\x64\x65sc2_en\x18\x0f \x01(\t\x12\x13\n\x0b\x64\x65sc2_chs_t\x18\x10 \x01(\t\x12\x10\n\x08\x64\x65sc2_kr\x18\x11 \x01(\t\x12\x0c\n\x04\x63\x61se\x18\x12 \x01(\t\x12\x0c\n\x04show\x18\x13 \x01(\r\x12\x0c\n\x04mode\x18\x14 \x01(\r\"\xc6\x02\n\x15GameLiveSelectFilters\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\r\x12\x0f\n\x07mode_id\x18\x03 \x01(\r\x12\x0c\n\x04mode\x18\x04 \x01(\r\x12\x15\n\rtournament_id\x18\x05 \x01(\r\x12\x0c\n\x04open\x18\x06 \x01(\r\x12\x0f\n\x07initial\x18\x07 \x01(\r\x12\x11\n\tname1_chs\x18\x08 \x01(\t\x12\x13\n\x0bname1_chs_t\x18\t \x01(\t\x12\x10\n\x08name1_jp\x18\n \x01(\t\x12\x10\n\x08name1_en\x18\x0b \x01(\t\x12\x10\n\x08name1_kr\x18\x0c \x01(\t\x12\x11\n\tname2_chs\x18\r \x01(\t\x12\x13\n\x0bname2_chs_t\x18\x0e \x01(\t\x12\x10\n\x08name2_jp\x18\x0f \x01(\t\x12\x10\n\x08name2_en\x18\x10 \x01(\t\x12\x10\n\x08name2_kr\x18\x11 \x01(\t\"W\n\tInfoError\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0b\n\x03\x63hs\x18\x02 \x01(\t\x12\r\n\x05\x63hs_t\x18\x03 \x01(\t\x12\n\n\x02jp\x18\x04 \x01(\t\x12\n\n\x02\x65n\x18\x05 \x01(\t\x12\n\n\x02kr\x18\x06 \x01(\t\"\xaa\x01\n\rInfoForbidden\x12\x0c\n\x04word\x18\x01 \x01(\t\x12\x10\n\x08type_chs\x18\x02 \x01(\r\x12\x10\n\x08near_chs\x18\x03 \x01(\r\x12\x0b\n\x03\x63hs\x18\x04 \x01(\r\x12\x0f\n\x07type_us\x18\x05 \x01(\r\x12\x0f\n\x07near_us\x18\x06 \x01(\r\x12\n\n\x02us\x18\x07 \x01(\r\x12\x0f\n\x07type_jp\x18\x08 \x01(\r\x12\x0f\n\x07near_jp\x18\t \x01(\r\x12\n\n\x02jp\x18\n \x01(\r\"U\n\x08InfoNear\x12\r\n\x05word1\x18\x01 \x01(\t\x12\r\n\x05word2\x18\x02 \x01(\t\x12\r\n\x05word3\x18\x03 \x01(\t\x12\r\n\x05word4\x18\x04 \x01(\t\x12\r\n\x05word5\x18\x05 \x01(\t\"a\n\rInfoTranslate\x12\x10\n\x08original\x18\x01 \x01(\t\x12\x0b\n\x03\x63hs\x18\x02 \x01(\t\x12\r\n\x05\x63hs_t\x18\x03 \x01(\t\x12\n\n\x02jp\x18\x04 \x01(\t\x12\n\n\x02\x65n\x18\x05 \x01(\t\x12\n\n\x02kr\x18\x06 \x01(\t\"\xf6\x01\n\x16ItemDefinitionCurrency\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x07 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x08 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0b \x01(\t\x12\x0c\n\x04icon\x18\x0c \x01(\t\x12\x10\n\x08icon_jpg\x18\r \x01(\t\"\xd2\x06\n\x12ItemDefinitionItem\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04sort\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x13\n\x0bname_chs_t2\x18\x05 \x01(\t\x12\x0f\n\x07name_jp\x18\x06 \x01(\t\x12\x0f\n\x07name_en\x18\x07 \x01(\t\x12\x0f\n\x07name_kr\x18\x08 \x01(\t\x12\x15\n\rdesc_func_chs\x18\t \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\n \x01(\t\x12\x17\n\x0f\x64\x65sc_func_chs_t\x18\x0b \x01(\t\x12\x12\n\ndesc_chs_t\x18\x0c \x01(\t\x12\x13\n\x0b\x64\x65sc_chs_t2\x18\r \x01(\t\x12\x14\n\x0c\x64\x65sc_func_jp\x18\x0e \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0f \x01(\t\x12\x14\n\x0c\x64\x65sc_func_en\x18\x10 \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x11 \x01(\t\x12\x14\n\x0c\x64\x65sc_func_kr\x18\x12 \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x13 \x01(\t\x12\x0c\n\x04icon\x18\x14 \x01(\t\x12\x18\n\x10icon_transparent\x18\x15 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x16 \x01(\r\x12\x0c\n\x04type\x18\x17 \x01(\r\x12\x11\n\tis_unique\x18\x18 \x01(\r\x12\x11\n\tmax_stack\x18\x19 \x01(\r\x12\x0e\n\x06\x61\x63\x63\x65ss\x18\x1a \x01(\t\x12\x12\n\naccessinfo\x18\x1b \x01(\r\x12\x0c\n\x04\x66unc\x18\x1c \x01(\t\x12\r\n\x05iargs\x18\x1d \x03(\r\x12\r\n\x05sargs\x18\x1e \x03(\t\x12\x10\n\x08\x63\x61n_sell\x18\x1f \x01(\r\x12\x16\n\x0esell_reward_id\x18  \x01(\r\x12\x19\n\x11sell_reward_count\x18! \x01(\r\x12\x13\n\x0bitem_expire\x18# \x01(\t\x12\x17\n\x0f\x65xpire_desc_chs\x18$ \x01(\t\x12\x19\n\x11\x65xpire_desc_chs_t\x18% \x01(\t\x12\x16\n\x0e\x65xpire_desc_jp\x18& \x01(\t\x12\x16\n\x0e\x65xpire_desc_en\x18\' \x01(\t\x12\x16\n\x0e\x65xpire_desc_kr\x18( \x01(\t\x12\x14\n\x0cregion_limit\x18) \x01(\r\x12\x12\n\ncross_view\x18* \x01(\r\x12\x16\n\x0e\x64\x61tabase_cache\x18+ \x01(\r\"\xc5\x02\n\x13ItemDefinitionTitle\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x07 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x08 \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0b \x01(\t\x12\x0c\n\x04icon\x18\x0c \x01(\t\x12\x11\n\ticon_item\x18\r \x01(\t\x12\x10\n\x08priority\x18\x0e \x01(\r\x12\x13\n\x0bunlock_type\x18\x0f \x01(\r\x12\x14\n\x0cunlock_param\x18\x10 \x03(\r\x12\x12\n\ncross_view\x18\x11 \x01(\r\"\xa9\x0c\n\x17ItemDefinitionCharacter\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04sort\x18\x02 \x01(\r\x12\x13\n\x0blaunch_time\x18\x03 \x01(\t\x12\x10\n\x08name_chs\x18\x04 \x01(\t\x12\x11\n\tname_chs2\x18\x05 \x01(\t\x12\x12\n\nname_chs_t\x18\x06 \x01(\t\x12\x13\n\x0bname_chs_t2\x18\x07 \x01(\t\x12\x0f\n\x07name_jp\x18\x08 \x01(\t\x12\x10\n\x08name_jp2\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x0f\n\x07name_kr\x18\x0b \x01(\t\x12\x0c\n\x04open\x18\x0c \x01(\r\x12\x11\n\tinit_skin\x18\r \x01(\r\x12\x18\n\x10\x66ull_fetter_skin\x18\x0e \x01(\r\x12\x0c\n\x04hand\x18\x0f \x01(\r\x12\x10\n\x08\x66\x61vorite\x18\x10 \x01(\r\x12\x17\n\x0fstar_5_material\x18\x11 \x01(\t\x12\x13\n\x0bstar_5_cost\x18\x12 \x01(\r\x12\x11\n\tcan_marry\x18\x13 \x01(\r\x12\x18\n\x10\x65xchange_item_id\x18\x14 \x01(\r\x12\x19\n\x11\x65xchange_item_num\x18\x15 \x01(\x05\x12\x0b\n\x03\x65mo\x18\x16 \x01(\t\x12\r\n\x05sound\x18\x17 \x01(\r\x12\x14\n\x0csound_volume\x18\x18 \x01(\x02\x12\x0b\n\x03sex\x18\x19 \x01(\r\x12\x18\n\x10\x64\x65sc_stature_chs\x18\x1a \x01(\t\x12\x1a\n\x12\x64\x65sc_stature_chs_t\x18\x1b \x01(\t\x12\x17\n\x0f\x64\x65sc_stature_jp\x18\x1c \x01(\t\x12\x17\n\x0f\x64\x65sc_stature_en\x18\x1d \x01(\t\x12\x17\n\x0f\x64\x65sc_stature_kr\x18\x1e \x01(\t\x12\x16\n\x0e\x64\x65sc_birth_chs\x18\x1f \x01(\t\x12\x18\n\x10\x64\x65sc_birth_chs_t\x18  \x01(\t\x12\x15\n\rdesc_birth_jp\x18! \x01(\t\x12\x15\n\rdesc_birth_en\x18\" \x01(\t\x12\x15\n\rdesc_birth_kr\x18# \x01(\t\x12\x14\n\x0c\x64\x65sc_age_chs\x18$ \x01(\t\x12\x16\n\x0e\x64\x65sc_age_chs_t\x18% \x01(\t\x12\x13\n\x0b\x64\x65sc_age_jp\x18& \x01(\t\x12\x13\n\x0b\x64\x65sc_age_en\x18\' \x01(\t\x12\x13\n\x0b\x64\x65sc_age_kr\x18( \x01(\t\x12\x1a\n\x12\x64\x65sc_bloodtype_chs\x18) \x01(\t\x12\x1c\n\x14\x64\x65sc_bloodtype_chs_t\x18* \x01(\t\x12\x19\n\x11\x64\x65sc_bloodtype_jp\x18+ \x01(\t\x12\x19\n\x11\x64\x65sc_bloodtype_en\x18, \x01(\t\x12\x19\n\x11\x64\x65sc_bloodtype_kr\x18- \x01(\t\x12\x13\n\x0b\x64\x65sc_cv_chs\x18. \x01(\t\x12\x15\n\rdesc_cv_chs_t\x18/ \x01(\t\x12\x12\n\ndesc_cv_jp\x18\x30 \x01(\t\x12\x12\n\ndesc_cv_en\x18\x31 \x01(\t\x12\x12\n\ndesc_cv_kr\x18\x32 \x01(\t\x12\x16\n\x0e\x64\x65sc_hobby_chs\x18\x33 \x01(\t\x12\x18\n\x10\x64\x65sc_hobby_chs_t\x18\x34 \x01(\t\x12\x15\n\rdesc_hobby_jp\x18\x35 \x01(\t\x12\x15\n\rdesc_hobby_en\x18\x36 \x01(\t\x12\x15\n\rdesc_hobby_kr\x18\x37 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x38 \x01(\t\x12\x15\n\rdesc_item_chs\x18\x39 \x01(\t\x12\x12\n\ndesc_chs_t\x18: \x01(\t\x12\x17\n\x0f\x64\x65sc_item_chs_t\x18; \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18< \x01(\t\x12\x14\n\x0c\x64\x65sc_item_jp\x18= \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18> \x01(\t\x12\x14\n\x0c\x64\x65sc_item_en\x18? \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18@ \x01(\t\x12\x14\n\x0c\x64\x65sc_item_kr\x18\x41 \x01(\t\x12\x15\n\rcollaboration\x18\x42 \x01(\r\x12\x14\n\x0cregion_limit\x18\x43 \x01(\r\x12\x10\n\x08skin_lib\x18\x44 \x03(\r\x12\n\n\x02ur\x18\x45 \x01(\r\x12\x0e\n\x06ur_ron\x18\x46 \x01(\r\x12\x0f\n\x07ur_liqi\x18G \x01(\r\x12\x10\n\x08ur_cutin\x18H \x01(\t\x12\x0f\n\x07limited\x18I \x01(\r\x12\x13\n\x0btreasure_sp\x18J \x01(\r\"i\n\x12ItemDefinitionView\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08res_name\x18\x02 \x01(\t\x12\x10\n\x08\x61udio_id\x18\x03 \x01(\r\x12\x14\n\x0c\x63haracter_id\x18\x04 \x01(\r\x12\r\n\x05sargs\x18\x05 \x03(\t\"\x9c\x0b\n\x12ItemDefinitionSkin\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x0f\n\x07name_jp\x18\x05 \x01(\t\x12\x0f\n\x07name_en\x18\x06 \x01(\t\x12\x0f\n\x07name_kr\x18\x07 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x08 \x01(\t\x12\x12\n\ndesc_chs_t\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0c \x01(\t\x12\x14\n\x0c\x63haracter_id\x18\r \x01(\r\x12\x15\n\rlock_tips_chs\x18\x0e \x01(\t\x12\x17\n\x0flock_tips_chs_t\x18\x0f \x01(\t\x12\x14\n\x0clock_tips_jp\x18\x10 \x01(\t\x12\x14\n\x0clock_tips_en\x18\x11 \x01(\t\x12\x14\n\x0clock_tips_kr\x18\x12 \x01(\t\x12\x0c\n\x04path\x18\x13 \x01(\t\x12\x18\n\x10\x65xchange_item_id\x18\x14 \x01(\r\x12\x19\n\x11\x65xchange_item_num\x18\x15 \x01(\x05\x12\x11\n\tdirection\x18\x16 \x01(\r\x12\x12\n\nno_reverse\x18\x17 \x01(\r\x12\x14\n\x0clock_reverse\x18\x18 \x01(\r\x12\x10\n\x08offset_x\x18\x19 \x01(\x05\x12\x10\n\x08offset_y\x18\x1a \x01(\x05\x12\x12\n\nspot_scale\x18\x1b \x01(\t\x12\x16\n\x0e\x65\x66\x66\x65\x63tive_time\x18\x1c \x01(\t\x12\x14\n\x0clobby_offset\x18\x1d \x01(\t\x12\x16\n\x0eliaoshe_offset\x18\x1e \x01(\t\x12\x13\n\x0bshop_offset\x18\x1f \x01(\t\x12\x12\n\nwin_offset\x18  \x01(\t\x12\x16\n\x0egameend_offset\x18! \x01(\t\x12\x15\n\rstarup_offset\x18\" \x01(\t\x12\x17\n\x0ftreasure_offset\x18# \x01(\t\x12\x18\n\x10\x63k_full_0_offset\x18$ \x01(\t\x12\x18\n\x10\x63k_full_1_offset\x18% \x01(\t\x12\x18\n\x10\x63k_full_2_offset\x18& \x01(\t\x12\x18\n\x10\x63k_full_3_offset\x18\' \x01(\t\x12\x1d\n\x15\x63k_full_single_offset\x18( \x01(\t\x12\x18\n\x10\x63k_half_0_offset\x18) \x01(\t\x12\x18\n\x10\x63k_half_1_offset\x18* \x01(\t\x12\x18\n\x10\x63k_half_2_offset\x18+ \x01(\t\x12\x18\n\x10\x63k_half_3_offset\x18, \x01(\t\x12\x1d\n\x15\x63k_half_single_offset\x18- \x01(\t\x12\x13\n\x0bsmallhead_x\x18. \x01(\x05\x12\x13\n\x0bsmallhead_y\x18/ \x01(\x05\x12\x17\n\x0fsmallhead_width\x18\x30 \x01(\x05\x12\x0e\n\x06\x66ull_x\x18\x31 \x01(\x05\x12\x0e\n\x06\x66ull_y\x18\x32 \x01(\x05\x12\x12\n\nfull_width\x18\x33 \x01(\x05\x12\x13\n\x0b\x66ull_height\x18\x34 \x01(\x05\x12\x0e\n\x06half_x\x18\x35 \x01(\x05\x12\x0e\n\x06half_y\x18\x36 \x01(\x05\x12\x12\n\nhalf_width\x18\x37 \x01(\x05\x12\x13\n\x0bhalf_height\x18\x38 \x01(\x05\x12\x12\n\nspine_type\x18\x39 \x01(\x05\x12\x13\n\x0bspine_width\x18: \x01(\x05\x12\x14\n\x0cspine_height\x18; \x01(\x05\x12\x0f\n\x07pivot_x\x18< \x01(\x05\x12\x0f\n\x07pivot_y\x18= \x01(\x05\x12\x0c\n\x04idle\x18> \x01(\x05\x12\x10\n\x08greeting\x18? \x01(\x05\x12\x11\n\tcelebrate\x18@ \x01(\x05\x12\r\n\x05\x63lick\x18\x41 \x01(\x05\x12\x15\n\rgreeting_init\x18\x42 \x01(\x05\x12\x0e\n\x06\x63lick2\x18\x43 \x01(\x05\x12\x16\n\x0e\x63\x65lebrate_idle\x18\x44 \x01(\x05\"\x1c\n\x1aItemDefinitionItemRecovery\"M\n\x1cItemDefinitionItemManualPool\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06res_id\x18\x02 \x01(\r\x12\x11\n\tres_count\x18\x03 \x01(\r\"L\n\x19ItemDefinitionSourceLimit\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07item_id\x18\x02 \x01(\r\x12\x12\n\nitem_limit\x18\x03 \x01(\r\"J\n\x19ItemDefinitionItemPackage\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06res_id\x18\x02 \x01(\r\x12\x11\n\tres_count\x18\x03 \x01(\r\"U\n\x1cItemDefinitionFakeRandomPool\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0bstage_count\x18\x02 \x03(\r\x12\x14\n\x0cstage_weight\x18\x03 \x03(\r\"r\n\x1aItemDefinitionLoadingImage\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08img_path\x18\x02 \x01(\t\x12\x12\n\nthumb_path\x18\x03 \x01(\t\x12\x0c\n\x04sort\x18\x04 \x01(\r\x12\x14\n\x0cunlock_items\x18\x05 \x03(\r\"\x84\x01\n\x16LeaderboardLeaderboard\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\nstart_time\x18\x02 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\t\x12\x12\n\nrefresh_cd\x18\x04 \x01(\r\x12\x11\n\tmax_count\x18\x05 \x01(\r\x12\x11\n\tshow_list\x18\x06 \x01(\t\"\xe9\x03\n\x1eLevelDefinitionLevelDefinition\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\x05\x12\x15\n\rprimary_level\x18\x03 \x01(\r\x12\x17\n\x0fsecondary_level\x18\x04 \x01(\r\x12\x12\n\ninit_point\x18\x05 \x01(\r\x12\x11\n\tend_point\x18\x06 \x01(\r\x12\x14\n\x0cprimary_icon\x18\x07 \x01(\t\x12\x10\n\x08name_chs\x18\x08 \x01(\t\x12\x12\n\nname_chs_t\x18\t \x01(\t\x12\x0f\n\x07name_jp\x18\n \x01(\t\x12\x0f\n\x07name_en\x18\x0b \x01(\t\x12\x0f\n\x07name_kr\x18\x0c \x01(\t\x12\x15\n\rfull_name_chs\x18\r \x01(\t\x12\x17\n\x0f\x66ull_name_chs_t\x18\x0e \x01(\t\x12\x14\n\x0c\x66ull_name_jp\x18\x0f \x01(\t\x12\x14\n\x0c\x66ull_name_en\x18\x10 \x01(\t\x12\x14\n\x0c\x66ull_name_kr\x18\x11 \x01(\t\x12\x13\n\x0b\x63\x61n_degrade\x18\x12 \x01(\r\x12\x13\n\x0b\x63\x61n_upgrade\x18\x13 \x01(\r\x12\x14\n\x0c\x63\x61n_getpoint\x18\x14 \x01(\r\x12\x0f\n\x07rankpt1\x18\x15 \x01(\x05\x12\x0f\n\x07rankpt2\x18\x16 \x01(\x05\x12\x13\n\x0btop_rank_id\x18\x17 \x01(\r\"\xed\x01\n\x18LevelDefinitionCharacter\x12\r\n\x05level\x18\x01 \x01(\r\x12\x14\n\x0c\x63haracter_id\x18\x02 \x01(\r\x12\x0b\n\x03\x65xp\x18\x03 \x01(\r\x12\x0e\n\x06reward\x18\x04 \x01(\t\x12\x13\n\x0bunlock_says\x18\x05 \x01(\r\x12\x17\n\x0funlock_desc_chs\x18\x06 \x01(\t\x12\x19\n\x11unlock_desc_chs_t\x18\x07 \x01(\t\x12\x16\n\x0eunlock_desc_jp\x18\x08 \x01(\t\x12\x16\n\x0eunlock_desc_en\x18\t \x01(\t\x12\x16\n\x0eunlock_desc_kr\x18\n \x01(\t\"q\n\x14LevelDefinitionTrail\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\ninit_level\x18\x02 \x01(\r\x12\x11\n\tend_level\x18\x03 \x01(\r\x12\x12\n\ntrail_icon\x18\x04 \x01(\r\x12\x12\n\ntrail_fire\x18\x05 \x01(\x05\"X\n\x16LevelDefinitionTopRank\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07rank_pt\x18\x02 \x03(\x05\x12\x13\n\x0btop_rank_pt\x18\x03 \x03(\x05\x12\x0c\n\x04mode\x18\x04 \x01(\r\"\xf5\x04\n\tMallGoods\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x0c\n\x04\x64\x65sc\x18\x07 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x08 \x01(\t\x12\x12\n\ndesc_chs_t\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0c \x01(\t\x12\x0c\n\x04icon\x18\r \x01(\t\x12\x13\n\x0bresource_id\x18\x0e \x01(\r\x12\x16\n\x0eresource_count\x18\x0f \x01(\r\x12\x0f\n\x07vip_exp\x18\x10 \x01(\r\x12\x0b\n\x03\x63ny\x18\x11 \x01(\r\x12\r\n\x05price\x18\x12 \x01(\t\x12\x16\n\x0e\x66irst_desc_chs\x18\x13 \x01(\t\x12\x18\n\x10\x66irst_desc_chs_t\x18\x14 \x01(\t\x12\x15\n\rfirst_desc_jp\x18\x15 \x01(\t\x12\x15\n\rfirst_desc_en\x18\x16 \x01(\t\x12\x15\n\rfirst_desc_kr\x18\x17 \x01(\t\x12\x18\n\x10\x66irst_extend_add\x18\x18 \x01(\r\x12\x17\n\x0fnormal_desc_chs\x18\x19 \x01(\t\x12\x19\n\x11normal_desc_chs_t\x18\x1a \x01(\t\x12\x16\n\x0enormal_desc_jp\x18\x1b \x01(\t\x12\x16\n\x0enormal_desc_en\x18\x1c \x01(\t\x12\x16\n\x0enormal_desc_kr\x18\x1d \x01(\t\x12\x19\n\x11normal_extend_add\x18\x1e \x01(\r\x12\x0c\n\x04type\x18\x1f \x01(\r\"\xe6\x01\n\x0bMallProduct\x12\x18\n\x10payment_platform\x18\x01 \x01(\r\x12\x10\n\x08goods_id\x18\x02 \x01(\r\x12\x14\n\x0cproduct_type\x18\x03 \x01(\r\x12\x12\n\nproduct_id\x18\x04 \x01(\t\x12\x15\n\rcurrency_code\x18\x05 \x01(\t\x12\x16\n\x0e\x63urrency_price\x18\x06 \x01(\r\x12\x13\n\x0b\x61\x63tual_code\x18\x07 \x01(\t\x12\x14\n\x0c\x61\x63tual_price\x18\x08 \x01(\r\x12\x12\n\nbrief_desc\x18\t \x01(\t\x12\x13\n\x0b\x64\x65tail_desc\x18\n \x01(\t\"\x84\x01\n\x10MallGoodsShelves\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08goods_id\x18\x02 \x01(\r\x12\x15\n\rcurrency_code\x18\x03 \x01(\t\x12\x16\n\x0e\x63urrency_price\x18\x04 \x01(\r\x12\r\n\x05price\x18\x05 \x01(\t\x12\x14\n\x0cis_monthcard\x18\x06 \x01(\r\"D\n\x0eMallZoneParams\x12\x0f\n\x07zone_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\x14\n\x0cstring_value\x18\x03 \x01(\t\"\xb0\x04\n\x0fMallMonthTicket\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x13\n\x0bresource_id\x18\x07 \x01(\r\x12\x16\n\x0eresource_count\x18\x08 \x01(\r\x12\x0f\n\x07vip_exp\x18\t \x01(\r\x12\x16\n\x0e\x65\x66\x66\x65\x63tive_time\x18\n \x01(\r\x12\x0c\n\x04icon\x18\x0b \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x0c \x01(\t\x12\x12\n\ndesc_chs_t\x18\r \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0e \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0f \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x10 \x01(\t\x12\x17\n\x0f\x64\x65sc_detail_chs\x18\x11 \x01(\t\x12\x19\n\x11\x64\x65sc_detail_chs_t\x18\x12 \x01(\t\x12\x16\n\x0e\x64\x65sc_detail_jp\x18\x13 \x01(\t\x12\x16\n\x0e\x64\x65sc_detail_en\x18\x14 \x01(\t\x12\x16\n\x0e\x64\x65sc_detail_kr\x18\x15 \x01(\t\x12\x18\n\x10\x64\x65sc_detail2_chs\x18\x16 \x01(\t\x12\x1a\n\x12\x64\x65sc_detail2_chs_t\x18\x17 \x01(\t\x12\x17\n\x0f\x64\x65sc_detail2_jp\x18\x18 \x01(\t\x12\x17\n\x0f\x64\x65sc_detail2_en\x18\x19 \x01(\t\x12\x17\n\x0f\x64\x65sc_detail2_kr\x18\x1a \x01(\t\"\xd1\x01\n\x11MallChannelConfig\x12\n\n\x02id\x18\x01 \x01(\r\x12\x1a\n\x12\x63urrency_platforms\x18\x02 \x01(\t\x12\x15\n\rfree_jade_ids\x18\x03 \x01(\t\x12\x15\n\rpaid_jade_ids\x18\x04 \x01(\t\x12\x18\n\x10\x66ree_voucher_ids\x18\x05 \x01(\t\x12\x18\n\x10paid_voucher_ids\x18\x06 \x01(\t\x12\x10\n\x08goods_id\x18\x07 \x01(\r\x12\x12\n\nshelves_id\x18\x08 \x01(\t\x12\x0c\n\x04name\x18\t \x01(\t\"!\n\x13MallMonthTicketInfo\x12\n\n\x02id\x18\x01 \x01(\r\"\xc3\x01\n\x13MatchShilianShilian\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tticket_id\x18\x03 \x01(\r\x12\x13\n\x0b\x63urrency_id\x18\x04 \x01(\r\x12\x16\n\x0e\x63urrency_count\x18\x05 \x01(\r\x12\x0c\n\x04mode\x18\x06 \x01(\r\x12\r\n\x05mode1\x18\x07 \x01(\r\x12\r\n\x05mode2\x18\x08 \x01(\r\x12\x12\n\ninit_point\x18\t \x01(\x05\x12\x12\n\nback_point\x18\n \x01(\x05\"P\n\x19MatchShilianShilianReward\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\treward_id\x18\x02 \x01(\r\x12\x14\n\x0creward_count\x18\x03 \x01(\r\"A\n\x17MatchShilianShilianTime\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05start\x18\x02 \x01(\t\x12\x0b\n\x03\x65nd\x18\x03 \x01(\t\"N\n\x17MiscFunctionDailySignIn\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\treward_id\x18\x02 \x01(\r\x12\x14\n\x0creward_count\x18\x03 \x01(\r\"-\n\x11RankIntroduceRank\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04info\x18\x02 \x03(\t\".\n\x12RankIntroduceRank3\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04info\x18\x02 \x03(\t\"\xa9\x02\n\x0cSeasonSeason\x12\n\n\x02id\x18\x01 \x01(\r\x12\x12\n\nstart_time\x18\x02 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\t\x12\x16\n\x0e\x64isappear_time\x18\x04 \x01(\t\x12\x12\n\nmatch_mode\x18\x05 \x01(\r\x12\x19\n\x11level_ticket_pool\x18\x06 \x01(\r\x12\x14\n\x0cticket_retry\x18\x07 \x01(\r\x12\x15\n\rpoint_item_id\x18\x08 \x01(\r\x12\x15\n\rpoint_consume\x18\t \x01(\r\x12\x10\n\x08\x64\x65sc_chs\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0c \x01(\t\x12\x12\n\ndesc_chs_t\x18\r \x01(\t\x12\x14\n\x0c\x64\x65sktop_type\x18\x0e \x01(\r\"p\n\x11SeasonLevelTicket\x12\n\n\x02id\x18\x01 \x01(\r\x12\r\n\x05level\x18\x02 \x01(\r\x12\x12\n\ngame_count\x18\x03 \x01(\r\x12\x0e\n\x06weight\x18\x04 \x01(\r\x12\x0c\n\x04task\x18\x05 \x03(\r\x12\x0e\n\x06reward\x18\x06 \x01(\t\"h\n\x15SeasonLevelTicketPool\x12\x0f\n\x07pool_id\x18\x01 \x01(\r\x12\x13\n\x0blevel_lower\x18\x02 \x01(\r\x12\x13\n\x0blevel_upper\x18\x03 \x01(\r\x12\x14\n\x0cticket_level\x18\x04 \x01(\r\"B\n\x11SeasonTicketRetry\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0c\n\x04\x63ost\x18\x03 \x01(\r\"`\n\x12SeasonSeasonReward\x12\x11\n\tseason_id\x18\x01 \x01(\r\x12\x12\n\nrank_lower\x18\x02 \x01(\r\x12\x12\n\nrank_upper\x18\x03 \x01(\r\x12\x0f\n\x07rewards\x18\x04 \x01(\t\"\xee\x01\n\rShopsZhpGoods\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04icon\x18\x02 \x01(\t\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x0f\n\x07name_jp\x18\x05 \x01(\t\x12\x0f\n\x07name_en\x18\x06 \x01(\t\x12\x0f\n\x07item_id\x18\x07 \x01(\r\x12\x0f\n\x07name_kr\x18\x08 \x01(\t\x12\x11\n\tbuy_limit\x18\t \x01(\x05\x12\x10\n\x08\x63urrency\x18\n \x01(\r\x12\r\n\x05price\x18\x0b \x01(\r\x12\x13\n\x0bneed_amount\x18\x0c \x01(\r\x12\x10\n\x08show_has\x18\x0f \x01(\x05\"\x16\n\x14ShopsZhpRefreshGroup\"9\n\x14ShopsZhpRefreshPrice\x12\n\n\x02id\x18\x01 \x01(\r\x12\x15\n\rrefresh_price\x18\x02 \x01(\r\"\xdf\x03\n\nShopsGoods\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08\x63\x61tegory\x18\x02 \x01(\r\x12\x16\n\x0e\x63\x61tegory_goods\x18\x03 \x01(\r\x12\x0c\n\x04icon\x18\x04 \x01(\t\x12\x10\n\x08name_chs\x18\x05 \x01(\t\x12\x12\n\nname_chs_t\x18\x06 \x01(\t\x12\x0f\n\x07name_jp\x18\x07 \x01(\t\x12\x0f\n\x07name_en\x18\x08 \x01(\t\x12\x0f\n\x07name_kr\x18\t \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\n \x01(\t\x12\x12\n\ndesc_chs_t\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\x0c \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\r \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0e \x01(\t\x12\x0f\n\x07item_id\x18\x0f \x01(\r\x12\r\n\x05price\x18\x10 \x01(\t\x12\x13\n\x0bneed_amount\x18\x11 \x01(\r\x12\x11\n\tbuy_limit\x18\x12 \x01(\x05\x12\x10\n\x08show_has\x18\x13 \x01(\x05\x12\x0c\n\x04sort\x18\x14 \x01(\r\x12\x10\n\x08\x64iscount\x18\x15 \x01(\r\x12\x15\n\rsell_activity\x18\x16 \x01(\r\x12\x13\n\x0blaunch_time\x18\x17 \x01(\t\x12\x0c\n\x04\x66unc\x18\x18 \x01(\t\x12\x19\n\x11\x64iscount_activity\x18\x19 \x01(\r\x12\x0c\n\x04zone\x18\x1a \x01(\t\"D\n\x11ShopsGoodsPackage\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0f\n\x07good_id\x18\x02 \x01(\r\x12\x12\n\ngood_count\x18\x03 \x01(\r\"h\n\x19ShopsIntervalRefreshGoods\x12\x10\n\x08group_id\x18\x01 \x01(\r\x12\x10\n\x08goods_id\x18\x02 \x01(\r\x12\x10\n\x08interval\x18\x03 \x01(\r\x12\x15\n\rinterval_type\x18\x04 \x01(\r\"1\n\x10ShopsItemPackage\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\titem_info\x18\x02 \x01(\t\"\xc8\x03\n\x08SpotSpot\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\tunique_id\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\x12\x10\n\x08name_chs\x18\x04 \x01(\t\x12\x12\n\nname_chs_t\x18\x05 \x01(\t\x12\x0f\n\x07name_jp\x18\x06 \x01(\t\x12\x0f\n\x07name_en\x18\x07 \x01(\t\x12\x0f\n\x07name_kr\x18\x08 \x01(\t\x12\x13\n\x0blevel_limit\x18\t \x01(\r\x12\x12\n\nis_married\x18\n \x01(\r\x12\x15\n\rlock_tips_chs\x18\x0b \x01(\t\x12\x17\n\x0flock_tips_chs_t\x18\x0c \x01(\t\x12\x14\n\x0clock_tips_jp\x18\r \x01(\t\x12\x14\n\x0clock_tips_en\x18\x0e \x01(\t\x12\x14\n\x0clock_tips_kr\x18\x0f \x01(\t\x12\x0e\n\x06queque\x18\x10 \x01(\r\x12\x13\n\x0b\x63ontent_chs\x18\x11 \x01(\t\x12\x15\n\rcontent_chs_t\x18\x12 \x01(\t\x12\x12\n\ncontent_jp\x18\x13 \x01(\t\x12\x12\n\ncontent_en\x18\x14 \x01(\t\x12\x12\n\ncontent_kr\x18\x15 \x01(\t\x12\x14\n\x0c\x63ontent_path\x18\x16 \x01(\t\x12\r\n\x05jieju\x18\x17 \x03(\r\"\x9f\x01\n\x0bSpotRewards\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x13\n\x0b\x63ontent_chs\x18\x03 \x01(\t\x12\x15\n\rcontent_chs_t\x18\x04 \x01(\t\x12\x12\n\ncontent_jp\x18\x05 \x01(\t\x12\x12\n\ncontent_en\x18\x06 \x01(\t\x12\x12\n\ncontent_kr\x18\x07 \x01(\t\x12\x0e\n\x06reward\x18\x08 \x01(\t\"\xc1\x03\n\tSpotEvent\x12\n\n\x02id\x18\x01 \x01(\r\x12\x13\n\x0b\x61\x63tivity_id\x18\x02 \x01(\r\x12\x13\n\x0bkey_item_id\x18\x03 \x01(\r\x12\x12\n\nkey_amount\x18\x04 \x01(\r\x12\x0c\n\x04sort\x18\x05 \x01(\r\x12\x13\n\x0bunlock_time\x18\x06 \x01(\t\x12\x14\n\x0c\x63ontent_path\x18\x07 \x01(\t\x12\x11\n\ttitle_chs\x18\x08 \x01(\t\x12\x13\n\x0btitle_chs_t\x18\t \x01(\t\x12\x10\n\x08title_jp\x18\n \x01(\t\x12\x10\n\x08title_en\x18\x0b \x01(\t\x12\x10\n\x08title_kr\x18\x0c \x01(\t\x12\x14\n\x0csubtitle_chs\x18\r \x01(\t\x12\x16\n\x0esubtitle_chs_t\x18\x0e \x01(\t\x12\x13\n\x0bsubtitle_jp\x18\x0f \x01(\t\x12\x13\n\x0bsubtitle_en\x18\x10 \x01(\t\x12\x13\n\x0bsubtitle_kr\x18\x11 \x01(\t\x12\x13\n\x0b\x63ontent_chs\x18\x12 \x01(\t\x12\x15\n\rcontent_chs_t\x18\x13 \x01(\t\x12\x12\n\ncontent_jp\x18\x14 \x01(\t\x12\x12\n\ncontent_en\x18\x15 \x01(\t\x12\x12\n\ncontent_kr\x18\x16 \x01(\t\"\xf8\x0b\n\x11SpotCharacterSpot\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04sort\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x11\n\tname_chs2\x18\x04 \x01(\t\x12\x12\n\nname_chs_t\x18\x05 \x01(\t\x12\x13\n\x0bname_chs_t2\x18\x06 \x01(\t\x12\x0f\n\x07name_jp\x18\x07 \x01(\t\x12\x10\n\x08name_jp2\x18\x08 \x01(\t\x12\x0f\n\x07name_en\x18\t \x01(\t\x12\x0f\n\x07name_kr\x18\n \x01(\t\x12\x0c\n\x04open\x18\x0b \x01(\r\x12\x11\n\tinit_skin\x18\x0c \x01(\r\x12\x18\n\x10\x66ull_fetter_skin\x18\r \x01(\r\x12\x0c\n\x04hand\x18\x0e \x01(\r\x12\x10\n\x08\x66\x61vorite\x18\x0f \x01(\r\x12\x17\n\x0fstar_5_material\x18\x10 \x01(\t\x12\x13\n\x0bstar_5_cost\x18\x11 \x01(\r\x12\x11\n\tcan_marry\x18\x12 \x01(\r\x12\x18\n\x10\x65xchange_item_id\x18\x13 \x01(\r\x12\x19\n\x11\x65xchange_item_num\x18\x14 \x01(\x05\x12\x0b\n\x03\x65mo\x18\x15 \x01(\t\x12\r\n\x05sound\x18\x16 \x01(\r\x12\x14\n\x0csound_volume\x18\x17 \x01(\x02\x12\x0b\n\x03sex\x18\x18 \x01(\r\x12\x18\n\x10\x64\x65sc_stature_chs\x18\x19 \x01(\t\x12\x1a\n\x12\x64\x65sc_stature_chs_t\x18\x1a \x01(\t\x12\x17\n\x0f\x64\x65sc_stature_jp\x18\x1b \x01(\t\x12\x17\n\x0f\x64\x65sc_stature_en\x18\x1c \x01(\t\x12\x17\n\x0f\x64\x65sc_stature_kr\x18\x1d \x01(\t\x12\x16\n\x0e\x64\x65sc_birth_chs\x18\x1e \x01(\t\x12\x18\n\x10\x64\x65sc_birth_chs_t\x18\x1f \x01(\t\x12\x15\n\rdesc_birth_jp\x18  \x01(\t\x12\x15\n\rdesc_birth_en\x18! \x01(\t\x12\x15\n\rdesc_birth_kr\x18\" \x01(\t\x12\x14\n\x0c\x64\x65sc_age_chs\x18# \x01(\t\x12\x16\n\x0e\x64\x65sc_age_chs_t\x18$ \x01(\t\x12\x13\n\x0b\x64\x65sc_age_jp\x18% \x01(\t\x12\x13\n\x0b\x64\x65sc_age_en\x18& \x01(\t\x12\x13\n\x0b\x64\x65sc_age_kr\x18\' \x01(\t\x12\x1a\n\x12\x64\x65sc_bloodtype_chs\x18( \x01(\t\x12\x1c\n\x14\x64\x65sc_bloodtype_chs_t\x18) \x01(\t\x12\x19\n\x11\x64\x65sc_bloodtype_jp\x18* \x01(\t\x12\x19\n\x11\x64\x65sc_bloodtype_en\x18+ \x01(\t\x12\x19\n\x11\x64\x65sc_bloodtype_kr\x18, \x01(\t\x12\x13\n\x0b\x64\x65sc_cv_chs\x18- \x01(\t\x12\x15\n\rdesc_cv_chs_t\x18. \x01(\t\x12\x12\n\ndesc_cv_jp\x18/ \x01(\t\x12\x12\n\ndesc_cv_en\x18\x30 \x01(\t\x12\x12\n\ndesc_cv_kr\x18\x31 \x01(\t\x12\x16\n\x0e\x64\x65sc_hobby_chs\x18\x32 \x01(\t\x12\x18\n\x10\x64\x65sc_hobby_chs_t\x18\x33 \x01(\t\x12\x15\n\rdesc_hobby_jp\x18\x34 \x01(\t\x12\x15\n\rdesc_hobby_en\x18\x35 \x01(\t\x12\x15\n\rdesc_hobby_kr\x18\x36 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x37 \x01(\t\x12\x15\n\rdesc_item_chs\x18\x38 \x01(\t\x12\x12\n\ndesc_chs_t\x18\x39 \x01(\t\x12\x17\n\x0f\x64\x65sc_item_chs_t\x18: \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18; \x01(\t\x12\x14\n\x0c\x64\x65sc_item_jp\x18< \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18= \x01(\t\x12\x14\n\x0c\x64\x65sc_item_en\x18> \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18? \x01(\t\x12\x14\n\x0c\x64\x65sc_item_kr\x18@ \x01(\t\x12\x15\n\rcollaboration\x18\x41 \x01(\r\x12\x10\n\x08skin_lib\x18\x42 \x03(\r\x12\n\n\x02ur\x18\x43 \x01(\r\x12\x0e\n\x06ur_ron\x18\x44 \x01(\r\x12\x0f\n\x07ur_liqi\x18\x45 \x01(\r\x12\x10\n\x08ur_cutin\x18\x46 \x01(\t\x12\x0f\n\x07limited\x18G \x01(\r\x12\x13\n\x0btreasure_sp\x18H \x01(\r\"\x96\x0b\n\x0cSpotSkinSpot\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x10\n\x08name_chs\x18\x03 \x01(\t\x12\x12\n\nname_chs_t\x18\x04 \x01(\t\x12\x0f\n\x07name_jp\x18\x05 \x01(\t\x12\x0f\n\x07name_en\x18\x06 \x01(\t\x12\x0f\n\x07name_kr\x18\x07 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x08 \x01(\t\x12\x12\n\ndesc_chs_t\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0c \x01(\t\x12\x14\n\x0c\x63haracter_id\x18\r \x01(\r\x12\x15\n\rlock_tips_chs\x18\x0e \x01(\t\x12\x17\n\x0flock_tips_chs_t\x18\x0f \x01(\t\x12\x14\n\x0clock_tips_jp\x18\x10 \x01(\t\x12\x14\n\x0clock_tips_en\x18\x11 \x01(\t\x12\x14\n\x0clock_tips_kr\x18\x12 \x01(\t\x12\x0c\n\x04path\x18\x13 \x01(\t\x12\x18\n\x10\x65xchange_item_id\x18\x14 \x01(\r\x12\x19\n\x11\x65xchange_item_num\x18\x15 \x01(\x05\x12\x11\n\tdirection\x18\x16 \x01(\r\x12\x12\n\nno_reverse\x18\x17 \x01(\r\x12\x14\n\x0clock_reverse\x18\x18 \x01(\r\x12\x10\n\x08offset_x\x18\x19 \x01(\x05\x12\x10\n\x08offset_y\x18\x1a \x01(\x05\x12\x12\n\nspot_scale\x18\x1b \x01(\t\x12\x16\n\x0e\x65\x66\x66\x65\x63tive_time\x18\x1c \x01(\t\x12\x14\n\x0clobby_offset\x18\x1d \x01(\t\x12\x16\n\x0eliaoshe_offset\x18\x1e \x01(\t\x12\x13\n\x0bshop_offset\x18\x1f \x01(\t\x12\x12\n\nwin_offset\x18  \x01(\t\x12\x16\n\x0egameend_offset\x18! \x01(\t\x12\x15\n\rstarup_offset\x18\" \x01(\t\x12\x17\n\x0ftreasure_offset\x18# \x01(\t\x12\x18\n\x10\x63k_full_0_offset\x18$ \x01(\t\x12\x18\n\x10\x63k_full_1_offset\x18% \x01(\t\x12\x18\n\x10\x63k_full_2_offset\x18& \x01(\t\x12\x18\n\x10\x63k_full_3_offset\x18\' \x01(\t\x12\x1d\n\x15\x63k_full_single_offset\x18( \x01(\t\x12\x18\n\x10\x63k_half_0_offset\x18) \x01(\t\x12\x18\n\x10\x63k_half_1_offset\x18* \x01(\t\x12\x18\n\x10\x63k_half_2_offset\x18+ \x01(\t\x12\x18\n\x10\x63k_half_3_offset\x18, \x01(\t\x12\x1d\n\x15\x63k_half_single_offset\x18- \x01(\t\x12\x13\n\x0bsmallhead_x\x18. \x01(\x05\x12\x13\n\x0bsmallhead_y\x18/ \x01(\x05\x12\x17\n\x0fsmallhead_width\x18\x30 \x01(\x05\x12\x0e\n\x06\x66ull_x\x18\x31 \x01(\x05\x12\x0e\n\x06\x66ull_y\x18\x32 \x01(\x05\x12\x12\n\nfull_width\x18\x33 \x01(\x05\x12\x13\n\x0b\x66ull_height\x18\x34 \x01(\x05\x12\x0e\n\x06half_x\x18\x35 \x01(\x05\x12\x0e\n\x06half_y\x18\x36 \x01(\x05\x12\x12\n\nhalf_width\x18\x37 \x01(\x05\x12\x13\n\x0bhalf_height\x18\x38 \x01(\x05\x12\x12\n\nspine_type\x18\x39 \x01(\x05\x12\x13\n\x0bspine_width\x18: \x01(\x05\x12\x14\n\x0cspine_height\x18; \x01(\x05\x12\x0f\n\x07pivot_x\x18< \x01(\x05\x12\x0f\n\x07pivot_y\x18= \x01(\x05\x12\x0c\n\x04idle\x18> \x01(\t\x12\x10\n\x08greeting\x18? \x01(\x05\x12\x11\n\tcelebrate\x18@ \x01(\x05\x12\r\n\x05\x63lick\x18\x41 \x01(\x05\x12\x15\n\rgreeting_init\x18\x42 \x01(\x05\x12\x0e\n\x06\x63lick2\x18\x43 \x01(\x05\x12\x16\n\x0e\x63\x65lebrate_idle\x18\x44 \x01(\x05\"Y\n\rSpotAudioSpot\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x13\n\x0btime_length\x18\x03 \x01(\x02\x12\x0b\n\x03str\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\r\"b\n\x06StrStr\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x0b\n\x03\x63hs\x18\x03 \x01(\t\x12\r\n\x05\x63hs_t\x18\x04 \x01(\t\x12\n\n\x02jp\x18\x05 \x01(\t\x12\n\n\x02\x65n\x18\x06 \x01(\t\x12\n\n\x02kr\x18\x07 \x01(\t\"d\n\x08StrEvent\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x0b\n\x03\x63hs\x18\x03 \x01(\t\x12\r\n\x05\x63hs_t\x18\x04 \x01(\t\x12\n\n\x02jp\x18\x05 \x01(\t\x12\n\n\x02\x65n\x18\x06 \x01(\t\x12\n\n\x02kr\x18\x07 \x01(\t\"I\n\x15TournamentTournaments\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x16\n\x0egame_ticket_id\x18\x03 \x01(\r\"\xea\x02\n\x0cTutorialInit\x12\x12\n\nepisode_id\x18\x01 \x01(\r\x12\x0c\n\x04\x64ora\x18\x02 \x01(\t\x12\r\n\x05rival\x18\x03 \x01(\t\x12\x12\n\ninit_score\x18\x04 \x01(\t\x12\r\n\x05paihe\x18\x05 \x01(\t\x12\r\n\x05\x63hang\x18\x06 \x01(\r\x12\n\n\x02ju\x18\x07 \x01(\r\x12\x10\n\x08\x62\x65nchang\x18\x08 \x01(\r\x12\x16\n\x0e\x66irst_position\x18\t \x01(\r\x12\x15\n\rview_position\x18\n \x01(\r\x12\x15\n\rstart_shoupai\x18\x0b \x01(\t\x12\x12\n\nstart_ming\x18\x0c \x01(\t\x12\x13\n\x0b\x65nd_shoupai\x18\r \x01(\t\x12\x10\n\x08\x65nd_ming\x18\x0e \x01(\t\x12\x10\n\x08\x65nd_yaku\x18\x0f \x01(\t\x12\x10\n\x08ura_dora\x18\x10 \x01(\t\x12\x0e\n\x06\x65nd_fu\x18\x11 \x01(\r\x12\x10\n\x08hu_score\x18\x12 \x01(\r\x12\x12\n\nflow_score\x18\x13 \x01(\t\"\xaa\x02\n\x0cTutorialStep\x12\x12\n\nepisode_id\x18\x01 \x01(\r\x12\n\n\x02id\x18\x02 \x01(\r\x12\x0c\n\x04seat\x18\x03 \x01(\r\x12\x10\n\x08\x61\x63t_type\x18\x04 \x01(\r\x12\x11\n\tact_param\x18\x05 \x01(\t\x12\x15\n\rtingpai_param\x18\x06 \x01(\t\x12\x13\n\x0bis_zhenting\x18\x07 \x01(\r\x12\x10\n\x08str_type\x18\x08 \x01(\r\x12\x0e\n\x06str_id\x18\t \x01(\r\x12\x14\n\x0cview_ui_hand\x18\n \x01(\r\x12\x10\n\x08pic_path\x18\x0b \x01(\t\x12\x13\n\x0b\x62utton_show\x18\x0c \x01(\t\x12\x12\n\nbutton_pai\x18\r \x01(\t\x12\x12\n\nplayer_act\x18\x0e \x01(\r\x12\x14\n\x0cplayer_param\x18\x0f \x01(\t\"\x95\x03\n\x06VipVip\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x0b\n\x03img\x18\x07 \x01(\t\x12\x10\n\x08\x64\x65sc_chs\x18\x08 \x01(\t\x12\x12\n\ndesc_chs_t\x18\t \x01(\t\x12\x0f\n\x07\x64\x65sc_jp\x18\n \x01(\t\x12\x0f\n\x07\x64\x65sc_en\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65sc_kr\x18\x0c \x01(\t\x12\x0e\n\x06\x63harge\x18\r \x01(\r\x12\x12\n\ngift_limit\x18\x0e \x01(\r\x12\x14\n\x0c\x66riend_added\x18\x0f \x01(\r\x12\x19\n\x11shop_free_refresh\x18\x10 \x01(\r\x12\x1a\n\x12shop_refresh_limit\x18\x11 \x01(\r\x12\x13\n\x0b\x62uddy_bonus\x18\x12 \x01(\r\x12\x17\n\x0f\x66\x61vourite_limit\x18\x13 \x01(\r\x12\x10\n\x08title_id\x18\x14 \x01(\r\x12\x0f\n\x07rewards\x18\x15 \x03(\t\"\xdd\x02\n\nVoiceSound\x12\n\n\x02id\x18\x01 \x01(\r\x12\x10\n\x08name_chs\x18\x02 \x01(\t\x12\x12\n\nname_chs_t\x18\x03 \x01(\t\x12\x0f\n\x07name_jp\x18\x04 \x01(\t\x12\x0f\n\x07name_en\x18\x05 \x01(\t\x12\x0f\n\x07name_kr\x18\x06 \x01(\t\x12\x11\n\twords_chs\x18\x07 \x01(\t\x12\x13\n\x0bwords_chs_t\x18\x08 \x01(\t\x12\x10\n\x08words_jp\x18\t \x01(\t\x12\x10\n\x08words_en\x18\n \x01(\t\x12\x10\n\x08words_kr\x18\x0b \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x0c \x01(\r\x12\x0c\n\x04type\x18\r \x01(\t\x12\x13\n\x0blevel_limit\x18\x0e \x01(\r\x12\x12\n\nbond_limit\x18\x0f \x01(\r\x12\x13\n\x0btime_length\x18\x10 \x01(\x02\x12\x0c\n\x04path\x18\x11 \x01(\t\x12\x0c\n\x04hide\x18\x12 \x01(\r\x12\x12\n\ndate_limit\x18\x13 \x01(\t\"\xdc\x01\n\nVoiceEvent\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\twords_chs\x18\x02 \x01(\t\x12\x13\n\x0bwords_chs_t\x18\x03 \x01(\t\x12\x10\n\x08words_jp\x18\x04 \x01(\t\x12\x10\n\x08words_en\x18\x05 \x01(\t\x12\x10\n\x08words_kr\x18\x06 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x07 \x01(\r\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\r\n\x05stage\x18\t \x01(\r\x12\x13\n\x0btime_length\x18\n \x01(\x02\x12\x0c\n\x04path\x18\x0b \x01(\t\x12\x12\n\nvolume_fix\x18\x0c \x01(\x02\"F\n\tVoiceSpot\x12\n\n\x02id\x18\x01 \x01(\r\x12\x11\n\tcharacter\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\x12\x0c\n\x04path\x18\x04 \x01(\tb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sheets_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ABMATCHMATCHINFO._serialized_start=17
  _ABMATCHMATCHINFO._serialized_end=302
  _ABMATCHPOINT._serialized_start=304
  _ABMATCHPOINT._serialized_end=384
  _ABMATCHREWARDSEQ._serialized_start=386
  _ABMATCHREWARDSEQ._serialized_end=494
  _ABMATCHCONSUMESEQ._serialized_start=496
  _ABMATCHCONSUMESEQ._serialized_end=585
  _ACHIEVEMENTACHIEVEMENT._serialized_start=588
  _ACHIEVEMENTACHIEVEMENT._serialized_end=955
  _ACHIEVEMENTACHIEVEMENTGROUP._serialized_start=958
  _ACHIEVEMENTACHIEVEMENTGROUP._serialized_end=1171
  _ACTIVITYACTIVITY._serialized_start=1174
  _ACTIVITYACTIVITY._serialized_end=1328
  _ACTIVITYTASK._serialized_start=1331
  _ACTIVITYTASK._serialized_end=1515
  _ACTIVITYEXCHANGE._serialized_start=1518
  _ACTIVITYEXCHANGE._serialized_end=1726
  _ACTIVITYCHESTUP._serialized_start=1729
  _ACTIVITYCHESTUP._serialized_end=1956
  _ACTIVITYGAMETASK._serialized_start=1959
  _ACTIVITYGAMETASK._serialized_end=2134
  _ACTIVITYGAMEPOINT._serialized_start=2137
  _ACTIVITYGAMEPOINT._serialized_end=2316
  _ACTIVITYRANK._serialized_start=2318
  _ACTIVITYRANK._serialized_end=2424
  _ACTIVITYRANKREWARD._serialized_start=2426
  _ACTIVITYRANKREWARD._serialized_end=2500
  _ACTIVITYFLIPTASK._serialized_start=2503
  _ACTIVITYFLIPTASK._serialized_end=2647
  _ACTIVITYFLIPINFO._serialized_start=2649
  _ACTIVITYFLIPINFO._serialized_end=2761
  _ACTIVITYDAILYSIGN._serialized_start=2763
  _ACTIVITYDAILYSIGN._serialized_end=2857
  _ACTIVITYRICHMANINFO._serialized_start=2860
  _ACTIVITYRICHMANINFO._serialized_end=3218
  _ACTIVITYRICHMANMAP._serialized_start=3221
  _ACTIVITYRICHMANMAP._serialized_end=3389
  _ACTIVITYRICHMANLEVEL._serialized_start=3391
  _ACTIVITYRICHMANLEVEL._serialized_end=3487
  _ACTIVITYRICHMANEVENT._serialized_start=3489
  _ACTIVITYRICHMANEVENT._serialized_end=3601
  _ACTIVITYPERIODTASK._serialized_start=3604
  _ACTIVITYPERIODTASK._serialized_end=3855
  _ACTIVITYRANDOMTASKPOOL._serialized_start=3858
  _ACTIVITYRANDOMTASKPOOL._serialized_end=4057
  _ACTIVITYRANDOMTASKINFO._serialized_start=4059
  _ACTIVITYRANDOMTASKINFO._serialized_end=4121
  _ACTIVITYRICHMANREWARDSEQ._serialized_start=4123
  _ACTIVITYRICHMANREWARDSEQ._serialized_end=4192
  _ACTIVITYACTIVITYBUFF._serialized_start=4195
  _ACTIVITYACTIVITYBUFF._serialized_end=4371
  _ACTIVITYBUFFCONDITION._serialized_start=4373
  _ACTIVITYBUFFCONDITION._serialized_end=4396
  _ACTIVITYGAMEPOINTINFO._serialized_start=4399
  _ACTIVITYGAMEPOINTINFO._serialized_end=4542
  _ACTIVITYGAMEPOINTRANK._serialized_start=4544
  _ACTIVITYGAMEPOINTRANK._serialized_end=4654
  _ACTIVITYGAMEPOINTFILTER._serialized_start=4656
  _ACTIVITYGAMEPOINTFILTER._serialized_end=4777
  _ACTIVITYACTIVITYROOM._serialized_start=4780
  _ACTIVITYACTIVITYROOM._serialized_end=5147
  _ACTIVITYSNSACTIVITY._serialized_start=5150
  _ACTIVITYSNSACTIVITY._serialized_end=5508
  _ACTIVITYMINEACTIVITY._serialized_start=5510
  _ACTIVITYMINEACTIVITY._serialized_end=5634
  _ACTIVITYMINEREWARD._serialized_start=5636
  _ACTIVITYMINEREWARD._serialized_end=5745
  _ACTIVITYRPGACTIVITY._serialized_start=5748
  _ACTIVITYRPGACTIVITY._serialized_end=6049
  _ACTIVITYRPGMONSTERGROUP._serialized_start=6052
  _ACTIVITYRPGMONSTERGROUP._serialized_end=6259
  _ACTIVITYARENAACTIVITY._serialized_start=6262
  _ACTIVITYARENAACTIVITY._serialized_end=6596
  _ACTIVITYARENAREWARD._serialized_start=6598
  _ACTIVITYARENAREWARD._serialized_end=6672
  _ACTIVITYARENAREWARDDISPLAY._serialized_start=6675
  _ACTIVITYARENAREWARDDISPLAY._serialized_end=6939
  _ACTIVITYSEGMENTTASK._serialized_start=6942
  _ACTIVITYSEGMENTTASK._serialized_end=7078
  _ACTIVITYFEEDACTIVITYINFO._serialized_start=7081
  _ACTIVITYFEEDACTIVITYINFO._serialized_end=7252
  _ACTIVITYFEEDACTIVITYREWARD._serialized_start=7254
  _ACTIVITYFEEDACTIVITYREWARD._serialized_end=7344
  _ACTIVITYVOTEACTIVITY._serialized_start=7346
  _ACTIVITYVOTEACTIVITY._serialized_end=7446
  _ACTIVITYRPGV2ACTIVITY._serialized_start=7449
  _ACTIVITYRPGV2ACTIVITY._serialized_end=7692
  _ACTIVITYSPOTACTIVITY._serialized_start=7695
  _ACTIVITYSPOTACTIVITY._serialized_end=7991
  _ACTIVITYACTIVITYITEM._serialized_start=7993
  _ACTIVITYACTIVITYITEM._serialized_end=8055
  _ACTIVITYUPGRADEACTIVITY._serialized_start=8058
  _ACTIVITYUPGRADEACTIVITY._serialized_end=8187
  _ACTIVITYUPGRADEACTIVITYREWARD._serialized_start=8189
  _ACTIVITYUPGRADEACTIVITYREWARD._serialized_end=8302
  _ACTIVITYFRIENDGIFTACTIVITY._serialized_start=8305
  _ACTIVITYFRIENDGIFTACTIVITY._serialized_end=8479
  _ACTIVITYUPGRADEACTIVITYDISPLAY._serialized_start=8481
  _ACTIVITYUPGRADEACTIVITYDISPLAY._serialized_end=8557
  _ACTIVITYACTIVITYDESKTOP._serialized_start=8559
  _ACTIVITYACTIVITYDESKTOP._serialized_end=8666
  _ACTIVITYGACHAACTIVITYINFO._serialized_start=8669
  _ACTIVITYGACHAACTIVITYINFO._serialized_end=8823
  _ACTIVITYGACHAPOOL._serialized_start=8825
  _ACTIVITYGACHAPOOL._serialized_end=8923
  _ACTIVITYGACHACONTROL._serialized_start=8925
  _ACTIVITYGACHACONTROL._serialized_end=8947
  _ACTIVITYTASKDISPLAY._serialized_start=8950
  _ACTIVITYTASKDISPLAY._serialized_end=9130
  _ACTIVITYSIMULATIONACTIVITYINFO._serialized_start=9132
  _ACTIVITYSIMULATIONACTIVITYINFO._serialized_end=9210
  _ACTIVITYREWARDMAIL._serialized_start=9212
  _ACTIVITYREWARDMAIL._serialized_end=9279
  _ACTIVITYCOMBININGACTIVITYINFO._serialized_start=9282
  _ACTIVITYCOMBININGACTIVITYINFO._serialized_end=9424
  _ACTIVITYCOMBININGCRAFTPOOL._serialized_start=9426
  _ACTIVITYCOMBININGCRAFTPOOL._serialized_end=9488
  _ACTIVITYCOMBININGMAP._serialized_start=9490
  _ACTIVITYCOMBININGMAP._serialized_end=9607
  _ACTIVITYCOMBININGORDER._serialized_start=9609
  _ACTIVITYCOMBININGORDER._serialized_end=9633
  _ACTIVITYCOMBININGCRAFT._serialized_start=9636
  _ACTIVITYCOMBININGCRAFT._serialized_end=9806
  _ACTIVITYCHESTREPLACEUP._serialized_start=9808
  _ACTIVITYCHESTREPLACEUP._serialized_end=9850
  _ACTIVITYVILLAGEACTIVITYINFO._serialized_start=9853
  _ACTIVITYVILLAGEACTIVITYINFO._serialized_end=10107
  _ACTIVITYVILLAGEBUILDING._serialized_start=10110
  _ACTIVITYVILLAGEBUILDING._serialized_end=10461
  _ACTIVITYVILLAGETASK._serialized_start=10464
  _ACTIVITYVILLAGETASK._serialized_end=10659
  _ACTIVITYLIVEREVENTINFO._serialized_start=10662
  _ACTIVITYLIVEREVENTINFO._serialized_end=11001
  _ACTIVITYLIVERTEXTINFO._serialized_start=11004
  _ACTIVITYLIVERTEXTINFO._serialized_end=11140
  _ANIMATIONANIMATION._serialized_start=11142
  _ANIMATIONANIMATION._serialized_end=11253
  _AUDIOAUDIO._serialized_start=11255
  _AUDIOAUDIO._serialized_end=11328
  _AUDIOBGM._serialized_start=11331
  _AUDIOBGM._serialized_end=11655
  _CHARACTEREMOJI._serialized_start=11658
  _CHARACTEREMOJI._serialized_end=12070
  _CHARACTERCUTIN._serialized_start=12073
  _CHARACTERCUTIN._serialized_end=12229
  _CHARACTERSKIN._serialized_start=12232
  _CHARACTERSKIN._serialized_end=12392
  _CHESTCHEST._serialized_start=12395
  _CHESTCHEST._serialized_end=12718
  _CHESTPOOL._serialized_start=12720
  _CHESTPOOL._serialized_end=12731
  _CHESTPOOLSEQ._serialized_start=12733
  _CHESTPOOLSEQ._serialized_end=12747
  _CHESTITEMPOOL._serialized_start=12749
  _CHESTITEMPOOL._serialized_end=12764
  _CHESTCHESTSHOP._serialized_start=12767
  _CHESTCHESTSHOP._serialized_end=13038
  _CHESTPREVIEW._serialized_start=13040
  _CHESTPREVIEW._serialized_end=13127
  _CHESTUP._serialized_start=13129
  _CHESTUP._serialized_end=13138
  _CHESTITEMPRICE._serialized_start=13140
  _CHESTITEMPRICE._serialized_end=13156
  _CHESTREPLACEUP._serialized_start=13158
  _CHESTREPLACEUP._serialized_end=13279
  _CHESTREPLACEPOOL._serialized_start=13281
  _CHESTREPLACEPOOL._serialized_end=13371
  _COMPOSECHARACOMPOSE._serialized_start=13373
  _COMPOSECHARACOMPOSE._serialized_end=13459
  _CONTESTCONTEST._serialized_start=13461
  _CONTESTCONTEST._serialized_end=13508
  _DESKTOPMATCHMODE._serialized_start=13511
  _DESKTOPMATCHMODE._serialized_end=14526
  _DESKTOPCHEST._serialized_start=14529
  _DESKTOPCHEST._serialized_end=14737
  _DESKTOPSETTINGS._serialized_start=14739
  _DESKTOPSETTINGS._serialized_end=14788
  _DESKTOPFIELDSPELL._serialized_start=14790
  _DESKTOPFIELDSPELL._serialized_end=14876
  _EVENTSSOSCOIN._serialized_start=14879
  _EVENTSSOSCOIN._serialized_end=15076
  _EVENTSDAILYEVENT._serialized_start=15079
  _EVENTSDAILYEVENT._serialized_end=15326
  _EVENTSBASETASK._serialized_start=15329
  _EVENTSBASETASK._serialized_end=15491
  _EXCHANGEEXCHANGE._serialized_start=15494
  _EXCHANGEEXCHANGE._serialized_end=15810
  _EXCHANGESEARCHEXCHANGE._serialized_start=15813
  _EXCHANGESEARCHEXCHANGE._serialized_end=16136
  _EXCHANGEFUSHIQUANEXCHANGE._serialized_start=16139
  _EXCHANGEFUSHIQUANEXCHANGE._serialized_end=16464
  _FANFAN._serialized_start=16467
  _FANFAN._serialized_end=16717
  _FANDESCFANDESC._serialized_start=16720
  _FANDESCFANDESC._serialized_end=17075
  _GAMELIVESELECTFILTERS._serialized_start=17078
  _GAMELIVESELECTFILTERS._serialized_end=17404
  _INFOERROR._serialized_start=17406
  _INFOERROR._serialized_end=17493
  _INFOFORBIDDEN._serialized_start=17496
  _INFOFORBIDDEN._serialized_end=17666
  _INFONEAR._serialized_start=17668
  _INFONEAR._serialized_end=17753
  _INFOTRANSLATE._serialized_start=17755
  _INFOTRANSLATE._serialized_end=17852
  _ITEMDEFINITIONCURRENCY._serialized_start=17855
  _ITEMDEFINITIONCURRENCY._serialized_end=18101
  _ITEMDEFINITIONITEM._serialized_start=18104
  _ITEMDEFINITIONITEM._serialized_end=18954
  _ITEMDEFINITIONTITLE._serialized_start=18957
  _ITEMDEFINITIONTITLE._serialized_end=19282
  _ITEMDEFINITIONCHARACTER._serialized_start=19285
  _ITEMDEFINITIONCHARACTER._serialized_end=20862
  _ITEMDEFINITIONVIEW._serialized_start=20864
  _ITEMDEFINITIONVIEW._serialized_end=20969
  _ITEMDEFINITIONSKIN._serialized_start=20972
  _ITEMDEFINITIONSKIN._serialized_end=22408
  _ITEMDEFINITIONITEMRECOVERY._serialized_start=22410
  _ITEMDEFINITIONITEMRECOVERY._serialized_end=22438
  _ITEMDEFINITIONITEMMANUALPOOL._serialized_start=22440
  _ITEMDEFINITIONITEMMANUALPOOL._serialized_end=22517
  _ITEMDEFINITIONSOURCELIMIT._serialized_start=22519
  _ITEMDEFINITIONSOURCELIMIT._serialized_end=22595
  _ITEMDEFINITIONITEMPACKAGE._serialized_start=22597
  _ITEMDEFINITIONITEMPACKAGE._serialized_end=22671
  _ITEMDEFINITIONFAKERANDOMPOOL._serialized_start=22673
  _ITEMDEFINITIONFAKERANDOMPOOL._serialized_end=22758
  _ITEMDEFINITIONLOADINGIMAGE._serialized_start=22760
  _ITEMDEFINITIONLOADINGIMAGE._serialized_end=22874
  _LEADERBOARDLEADERBOARD._serialized_start=22877
  _LEADERBOARDLEADERBOARD._serialized_end=23009
  _LEVELDEFINITIONLEVELDEFINITION._serialized_start=23012
  _LEVELDEFINITIONLEVELDEFINITION._serialized_end=23501
  _LEVELDEFINITIONCHARACTER._serialized_start=23504
  _LEVELDEFINITIONCHARACTER._serialized_end=23741
  _LEVELDEFINITIONTRAIL._serialized_start=23743
  _LEVELDEFINITIONTRAIL._serialized_end=23856
  _LEVELDEFINITIONTOPRANK._serialized_start=23858
  _LEVELDEFINITIONTOPRANK._serialized_end=23946
  _MALLGOODS._serialized_start=23949
  _MALLGOODS._serialized_end=24578
  _MALLPRODUCT._serialized_start=24581
  _MALLPRODUCT._serialized_end=24811
  _MALLGOODSSHELVES._serialized_start=24814
  _MALLGOODSSHELVES._serialized_end=24946
  _MALLZONEPARAMS._serialized_start=24948
  _MALLZONEPARAMS._serialized_end=25016
  _MALLMONTHTICKET._serialized_start=25019
  _MALLMONTHTICKET._serialized_end=25579
  _MALLCHANNELCONFIG._serialized_start=25582
  _MALLCHANNELCONFIG._serialized_end=25791
  _MALLMONTHTICKETINFO._serialized_start=25793
  _MALLMONTHTICKETINFO._serialized_end=25826
  _MATCHSHILIANSHILIAN._serialized_start=25829
  _MATCHSHILIANSHILIAN._serialized_end=26024
  _MATCHSHILIANSHILIANREWARD._serialized_start=26026
  _MATCHSHILIANSHILIANREWARD._serialized_end=26106
  _MATCHSHILIANSHILIANTIME._serialized_start=26108
  _MATCHSHILIANSHILIANTIME._serialized_end=26173
  _MISCFUNCTIONDAILYSIGNIN._serialized_start=26175
  _MISCFUNCTIONDAILYSIGNIN._serialized_end=26253
  _RANKINTRODUCERANK._serialized_start=26255
  _RANKINTRODUCERANK._serialized_end=26300
  _RANKINTRODUCERANK3._serialized_start=26302
  _RANKINTRODUCERANK3._serialized_end=26348
  _SEASONSEASON._serialized_start=26351
  _SEASONSEASON._serialized_end=26648
  _SEASONLEVELTICKET._serialized_start=26650
  _SEASONLEVELTICKET._serialized_end=26762
  _SEASONLEVELTICKETPOOL._serialized_start=26764
  _SEASONLEVELTICKETPOOL._serialized_end=26868
  _SEASONTICKETRETRY._serialized_start=26870
  _SEASONTICKETRETRY._serialized_end=26936
  _SEASONSEASONREWARD._serialized_start=26938
  _SEASONSEASONREWARD._serialized_end=27034
  _SHOPSZHPGOODS._serialized_start=27037
  _SHOPSZHPGOODS._serialized_end=27275
  _SHOPSZHPREFRESHGROUP._serialized_start=27277
  _SHOPSZHPREFRESHGROUP._serialized_end=27299
  _SHOPSZHPREFRESHPRICE._serialized_start=27301
  _SHOPSZHPREFRESHPRICE._serialized_end=27358
  _SHOPSGOODS._serialized_start=27361
  _SHOPSGOODS._serialized_end=27840
  _SHOPSGOODSPACKAGE._serialized_start=27842
  _SHOPSGOODSPACKAGE._serialized_end=27910
  _SHOPSINTERVALREFRESHGOODS._serialized_start=27912
  _SHOPSINTERVALREFRESHGOODS._serialized_end=28016
  _SHOPSITEMPACKAGE._serialized_start=28018
  _SHOPSITEMPACKAGE._serialized_end=28067
  _SPOTSPOT._serialized_start=28070
  _SPOTSPOT._serialized_end=28526
  _SPOTREWARDS._serialized_start=28529
  _SPOTREWARDS._serialized_end=28688
  _SPOTEVENT._serialized_start=28691
  _SPOTEVENT._serialized_end=29140
  _SPOTCHARACTERSPOT._serialized_start=29143
  _SPOTCHARACTERSPOT._serialized_end=30671
  _SPOTSKINSPOT._serialized_start=30674
  _SPOTSKINSPOT._serialized_end=32104
  _SPOTAUDIOSPOT._serialized_start=32106
  _SPOTAUDIOSPOT._serialized_end=32195
  _STRSTR._serialized_start=32197
  _STRSTR._serialized_end=32295
  _STREVENT._serialized_start=32297
  _STREVENT._serialized_end=32397
  _TOURNAMENTTOURNAMENTS._serialized_start=32399
  _TOURNAMENTTOURNAMENTS._serialized_end=32472
  _TUTORIALINIT._serialized_start=32475
  _TUTORIALINIT._serialized_end=32837
  _TUTORIALSTEP._serialized_start=32840
  _TUTORIALSTEP._serialized_end=33138
  _VIPVIP._serialized_start=33141
  _VIPVIP._serialized_end=33546
  _VOICESOUND._serialized_start=33549
  _VOICESOUND._serialized_end=33898
  _VOICEEVENT._serialized_start=33901
  _VOICEEVENT._serialized_end=34121
  _VOICESPOT._serialized_start=34123
  _VOICESPOT._serialized_end=34193
# @@protoc_insertion_point(module_scope)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的日本麻将教学网站启动器
无GUI版本，直接启动服务器并打开浏览器
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import socket
import sys
from pathlib import Path

# 导入嵌入的静态文件
try:
    from embedded_static import create_temp_static_dir, cleanup_temp_dir
    USE_EMBEDDED = True
except ImportError:
    USE_EMBEDDED = False

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        try:
            return socket.gethostbyname(socket.gethostname())
        except:
            return "127.0.0.1"

def main():
    port = 8000
    
    print("🀄 日本麻将教学网站")
    print("=" * 50)
    print("正在启动服务器...")
    
    try:
        # 获取静态文件目录
        if USE_EMBEDDED:
            print("✅ 使用嵌入的静态文件")
            static_dir = create_temp_static_dir()
        else:
            print("✅ 使用本地静态文件")
            if getattr(sys, 'frozen', False):
                # 打包后的路径
                base_path = Path(sys._MEIPASS)
                static_dir = base_path / "static"
            else:
                # 开发环境路径
                static_dir = Path(__file__).parent / "static"
        
        # 检查目录和文件
        if not static_dir.exists():
            print(f"❌ 找不到网站文件目录: {static_dir}")
            input("按回车键退出...")
            return 1
        
        index_file = static_dir / "index.html"
        if not index_file.exists():
            print(f"❌ 找不到index.html文件: {index_file}")
            input("按回车键退出...")
            return 1
        
        print(f"✅ 网站文件目录: {static_dir}")
        
        # 创建服务器
        handler_cls = lambda *args, **kwargs: http.server.SimpleHTTPRequestHandler(
            *args, directory=str(static_dir), **kwargs
        )
        
        socketserver.ThreadingTCPServer.allow_reuse_address = True
        server = socketserver.ThreadingTCPServer(("0.0.0.0", port), handler_cls)
        
        # 获取IP地址
        local_ip = get_local_ip()
        
        print("✅ 服务器启动成功！")
        print(f"📍 本机访问: http://127.0.0.1:{port}")
        print(f"🌐 局域网访问: http://{local_ip}:{port}")
        print()
        print("🚀 浏览器将在3秒后自动打开...")
        print("📚 开始学习日本麻将规则吧！")
        print()
        print("⚠️  请保持此窗口打开，关闭窗口将停止服务器")
        print("🛑 按 Ctrl+C 可以停止服务器")
        print("=" * 50)
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open(f"http://127.0.0.1:{port}")
                print("✅ 浏览器已打开")
            except Exception as e:
                print(f"❌ 无法自动打开浏览器: {e}")
                print(f"请手动访问: http://127.0.0.1:{port}")
        
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 启动服务器
        try:
            server.serve_forever()
        except KeyboardInterrupt:
            print("\n\n🛑 正在停止服务器...")
            server.shutdown()
            server.server_close()
            
            # 清理临时目录
            if USE_EMBEDDED:
                cleanup_temp_dir(static_dir)
            
            print("✅ 服务器已停止")
            print("👋 感谢使用日本麻将教学网站！")
            return 0
        
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 端口 {port} 已被占用")
            print("请关闭其他程序或重启电脑后重试")
        else:
            print(f"❌ 启动服务器失败: {e}")
        input("按回车键退出...")
        return 1
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())

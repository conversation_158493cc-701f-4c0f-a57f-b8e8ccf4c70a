{"time":"2025-08-14T12:00:33.6354174+08:00","level":"INFO","prefix":"echo","file":"server.go","line":"503","message":"============================================================================================"}
{"time":"2025-08-14T12:00:33.6354174+08:00","level":"INFO","prefix":"echo","file":"server.go","line":"504","message":"服务启动"}
{"time":"2025-08-14T12:00:51.3229661+08:00","level":"INFO","prefix":"echo","file":"server.go","line":"173","message":"{\"account_id\": ********, \"account\": {\"account_id\": ********, \"nickname\": \"Nifilm\", \"login_time\": **********, \"logout_time\": **********, \"gold\": 103300, \"avatar_id\": 400603, \"platform_diamond\": [{\"id\": 100001, \"count\": 0}, {\"id\": 101005, \"count\": 0}, {\"id\": 101002, \"count\": 0}, {\"id\": 101006, \"count\": 0}, {\"id\": 101001, \"count\": 0}, {\"id\": 101003, \"count\": 0}, {\"id\": 101004, \"count\": 0}, {\"id\": 101007, \"count\": 0}, {\"id\": 101008, \"count\": 0}, {\"id\": 101009, \"count\": 0}, {\"id\": 101010, \"count\": 0}, {\"id\": 101011, \"count\": 0}], \"level\": {\"id\": 10101, \"score\": 0}, \"level3\": {\"id\": 20102, \"score\": 1}, \"avatar_frame\": 305537, \"platform_skin_ticket\": [{\"id\": 100004, \"count\": 0}, {\"id\": 102005, \"count\": 0}, {\"id\": 102002, \"count\": 0}, {\"id\": 102001, \"count\": 0}, {\"id\": 102003, \"count\": 0}, {\"id\": 102006, \"count\": 0}, {\"id\": 102004, \"count\": 0}, {\"id\": 102007, \"count\": 0}, {\"id\": 102008, \"count\": 0}, {\"id\": 102009, \"count\": 0}, {\"id\": 102010, \"count\": 0}, {\"id\": 102011, \"count\": 0}], \"achievement_count\": [{\"rare\": 1, \"count\": 34}], \"loading_image\": [250701, 250601, 250501, 250401, 250302, 250301, 250201, 250103, 250102, 250101, 241202, 241201, 241101, 241001, 240901, 240805, 240804, 240803, 240802, 240801, 240701, 240602, 240601, 240501, 240401, 240302, 240301, 240201, 240102, 240101, 231202, 231201, 231101, 231001, 230901, 230803, 230802, 230801, 230701, 230602, 230601, 230501, 230401, 230302, 230301, 230103, 230102, 230101, 221202, 221201, 221001, 220901, 220803, 220802, 220801, 220701, 220601, 220501, 220301, 220201, 220102, 220101, 211202, 211201, 211101, 210901, 210803, 210802, 210801, 210701, 210601, 210201, 201201, 201202, 30740017, 30740016, 30740015, 30740014, 30740013, 30740012, 30740011, 30740010, 30740009, 30740008, 30740007, 30740006, 30740005, 30740004, 30740003, 30740002, 30740001], \"badges\": [{\"id\": 810001, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810002, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810003, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810004, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810005, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810006, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810011, \"achieved_time\": 1754051639, \"achieved_counter\": 1}, {\"id\": 810012, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810013, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810014, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810015, \"achieved_time\": 1754051639, \"achieved_counter\": 2}, {\"id\": 810016, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810021, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810022, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810023, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810024, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810025, \"achieved_time\": 0, \"achieved_counter\": 0}, {\"id\": 810026, \"achieved_time\": 0, \"achieved_counter\": 0}], \"room_id\": 0, \"title\": 0, \"signature\": \"\", \"email\": \"\", \"email_verify\": 0, \"diamond\": 0, \"vip\": 0, \"birthday\": 0, \"phone\": \"\", \"phone_verify\": 0, \"skin_ticket\": 0, \"verified\": 0, \"challenge_levels\": [], \"frozen_state\": 0, \"favorite_hu\": []}, \"access_token\": \"eb68925b-e5ef-42a0-b655-ca3e0148737e\", \"signup_time\": 1753994105, \"country\": \"CN\", \"has_unread_announcement\": false, \"is_id_card_authed\": false, \"logined_version\": [], \"rewarded_version\": []}"}

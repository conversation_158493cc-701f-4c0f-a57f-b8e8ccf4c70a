#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接诊断工具
帮助排查局域网访问问题
"""

import socket
import subprocess
import sys
import platform

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 连接到一个外部地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        try:
            # 备用方法
            hostname = socket.gethostname()
            return socket.gethostbyname(hostname)
        except:
            return "无法获取"

def check_port_open(port=8000):
    """检查端口是否被占用"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        result = s.connect_ex(('127.0.0.1', port))
        s.close()
        return result == 0
    except:
        return False

def check_firewall_windows():
    """检查Windows防火墙状态"""
    if platform.system() != "Windows":
        return "非Windows系统"
    
    try:
        # 检查防火墙状态
        result = subprocess.run(
            ["netsh", "advfirewall", "show", "allprofiles", "state"],
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            return "防火墙状态:\n" + result.stdout
        else:
            return "无法获取防火墙状态"
    except:
        return "检查防火墙失败"

def test_network_connectivity():
    """测试网络连通性"""
    print("=" * 60)
    print("🔍 网络连接诊断工具")
    print("=" * 60)
    
    # 1. 获取IP信息
    local_ip = get_local_ip()
    print(f"📍 本机IP地址: {local_ip}")
    
    # 2. 检查端口
    port = 8000
    port_open = check_port_open(port)
    print(f"🔌 端口{port}状态: {'✅ 开放' if port_open else '❌ 关闭或被占用'}")
    
    # 3. 显示访问地址
    print(f"\n🌐 访问地址:")
    print(f"   本机: http://127.0.0.1:{port}")
    print(f"   局域网: http://{local_ip}:{port}")
    
    # 4. 检查防火墙（仅Windows）
    if platform.system() == "Windows":
        print(f"\n🛡️ 防火墙检查:")
        fw_status = check_firewall_windows()
        if "State" in fw_status:
            lines = fw_status.split('\n')
            for line in lines:
                if "State" in line:
                    print(f"   {line.strip()}")
        else:
            print(f"   {fw_status}")
    
    # 5. 提供解决方案
    print(f"\n💡 如果局域网无法访问，请尝试:")
    print(f"   1. 关闭Windows防火墙（临时测试）")
    print(f"   2. 在防火墙中添加Python.exe例外")
    print(f"   3. 检查杀毒软件是否阻止网络连接")
    print(f"   4. 确认路由器未开启AP隔离")
    print(f"   5. 尝试其他端口: python server.py 8080")
    
    # 6. Windows防火墙规则建议
    if platform.system() == "Windows":
        python_exe = sys.executable
        print(f"\n🔧 Windows防火墙规则命令:")
        print(f"   以管理员身份运行命令提示符，执行:")
        print(f'   netsh advfirewall firewall add rule name="Python HTTP Server" dir=in action=allow program="{python_exe}" enable=yes')
    
    print("=" * 60)

if __name__ == "__main__":
    test_network_connectivity()

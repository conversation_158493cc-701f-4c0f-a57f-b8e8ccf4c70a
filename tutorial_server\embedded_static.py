# -*- coding: utf-8 -*-
"""
嵌入的静态文件内容
自动生成，请勿手动修改
"""

import base64
from pathlib import Path
import tempfile
import os

STATIC_FILES = {
    "app.js": """// Practice Questions by Category
const practiceData = {
  1: [ // 役的判断
    {
      id: 'p1_1',
      text: "手牌：[2][3][4]万 [5][6][7]筒 [1][2][3]索 [7][8][9]万 [4][4]筒，门清听[3][6]筒。有什么役？",
      options: ["立直", "平和", "断幺九", "以上都可能"],
      answer: 3,
      explain: "可立直获得1番；四面子皆顺子+雀头非役牌+两面和=平和1番；无1/9/字牌=断幺九1番。"
    },
    {
      id: 'p1_2',
      text: "副露后手牌全是2-8数牌，无字牌。有什么役？",
      options: ["断幺九", "平和", "无役", "立直"],
      answer: 0,
      explain: "断幺九不需门清，副露也成立。平和和立直都需要门清。"
    },
    {
      id: 'p1_3',
      text: "门清手牌有两组[2][3][4]万，这是什么役？",
      options: ["二盃口", "一盃口", "平和", "断幺九"],
      answer: 1,
      explain: "两组完全相同的顺子=一盃口1番（门清限定）。"
    }
  ],
  2: [ // 振听识别
    {
      id: 'p2_1',
      text: "你听[4][7]万，舍牌河有[4]万。上家打出[7]万，能荣和吗？",
      options: ["能荣和", "不能荣和", "看情况"],
      answer: 1,
      explain: "舍张振听：你的舍牌包含和张4万，所以对所有和张（4万和7万）都不能荣和。"
    },
    {
      id: 'p2_2',
      text: "立直听牌，本巡内你选择不和一张牌，同巡内其他人再打相同牌？",
      options: ["可以荣和", "不能荣和", "只能自摸"],
      answer: 1,
      explain: "同巡振听：本巡内放过和张后，本巡内不能再荣和任何牌，下一巡解除。"
    },
    {
      id: 'p2_3',
      text: "立直后摸牌改变了待牌，新的舍牌河包含新和张，怎么办？",
      options: ["可以荣和", "只能自摸", "可以取消立直"],
      answer: 1,
      explain: "立直振听：立直后出现的舍张振听持续到和牌或流局，不能荣和任何牌。"
    }
  ],
  3: [ // 平和判定
    {
      id: 'p3_1',
      text: "[1][2][3]万 [4][5][6]筒 [7][8][9]索 [2][3][4]万 [5][5]筒，听[1][4][7]筒，是平和吗？",
      options: ["是", "否"],
      answer: 1,
      explain: "听多面不是两面和牌，不符合平和条件。平和要求严格的两面和牌。"
    },
    {
      id: 'p3_2',
      text: "[2][3][4]万 [5][6][7]筒 [1][2][3]索 [7][8][9]万 [东][东]，听[东]，是平和吗？",
      options: ["是", "否"],
      answer: 1,
      explain: "单骑和牌不是两面，且雀头是字牌，不符合平和条件。"
    },
    {
      id: 'p3_3',
      text: "[2][3][4]万 [5][6][7]筒 [1][2][3]索 [6][7][8]万 [9][9]筒，门清听[5][8]筒，是平和吗？",
      options: ["是", "否"],
      answer: 0,
      explain: "四面子皆顺子，雀头9筒非役牌，两面和牌，门清，符合平和所有条件。"
    }
  ]
};

// Main Quiz Questions
const quizData = [
  {
    id: 1,
    text: "门清听两面（3万/6万），你的舍牌里有一张3万。此时他家打出3万。你能否荣和？",
    options: ["能荣和", "只能自摸", "无法和"],
    answer: 1,
    explain: "你处于舍张振听，不能对3万荣和，但仍可自摸3万或荣/自摸6万。"
  },
  {
    id: 2,
    text: "副露后的断幺九（不含1/9/字）是否依然是一番役？",
    options: ["是", "否", "只在门清时算"],
    answer: 0,
    explain: "断幺九无门清限制，副露也可成立。与立直不同。"
  },
  {
    id: 3,
    text: "立直后同巡你放过一张可荣和的牌，这一巡内是否还可以改判去荣和？",
    options: ["可以", "不可以，下一巡解除", "永久不能和"],
    answer: 1,
    explain: "这是同巡振听：当前巡内放过和张则本巡内不可再荣和，下一巡自动解除。"
  },
  {
    id: 4,
    text: "听单骑将牌，是否一定有平和？",
    options: ["是", "否"],
    answer: 1,
    explain: "平和要求四面子皆顺子、将为非役牌、且和牌为两面。单骑不满足两面和条件。"
  },
  {
    id: 5,
    text: "场风为东，你手里碰出东刻子。是否满足役牌？",
    options: ["是", "否", "要门清才算"],
    answer: 0,
    explain: "役牌（场风/门风/三元）无门清限制，碰出刻子即可成立一番。"
  },
  {
    id: 6,
    text: "手牌全是万子，包含字牌。这是什么役？",
    options: ["清一色", "混一色", "断幺九", "无役"],
    answer: 1,
    explain: "一种数牌+字牌=混一色。清一色要求仅一种数牌无字牌。"
  },
  {
    id: 7,
    text: "门清手牌：[1][1][1]万 [2][2][2]筒 [3][3][3]索 [4][4][4]万 [5][5]筒，有什么役？",
    options: ["对对和", "断幺九", "无役", "三暗刻"],
    answer: 2,
    explain: "虽然是对对和牌型，但对对和不是基础役。含1万不是断幺九。门清全刻子需要其他役。"
  },
  {
    id: 8,
    text: "立直宣言需要支付多少点棒？",
    options: ["500点", "1000点", "1500点", "不需要"],
    answer: 1,
    explain: "立直宣言需要支付1000点棒，放在桌面中央。"
  }
];

// Render Practice Questions
function renderPractice(practiceNum) {
  const root = document.getElementById(`practice${practiceNum}-root`);
  const questions = practiceData[practiceNum];

  root.innerHTML = '';
  questions.forEach((q, idx) => {
    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-item';
    questionDiv.style.marginBottom = '16px';
    questionDiv.style.padding = '12px';
    questionDiv.style.background = '#0f1419';
    questionDiv.style.borderRadius = '6px';

    const h = document.createElement('h4');
    h.textContent = `${idx + 1}. `;
    h.style.margin = '0 0 8px 0';

    const p = document.createElement('p');
    p.textContent = q.text;
    p.style.margin = '0 0 12px 0';

    const list = document.createElement('ul');
    list.style.listStyle = 'none';
    list.style.paddingLeft = '0';
    list.style.margin = '0';

    q.options.forEach((opt, i) => {
      const li = document.createElement('li');
      li.style.margin = '4px 0';
      const id = `${q.id}_opt${i}`;
      li.innerHTML = `<label style="cursor: pointer;"><input type="radio" name="${q.id}" value="${i}" id="${id}" style="margin-right: 8px;"> ${opt}</label>`;
      list.appendChild(li);
    });

    questionDiv.appendChild(h);
    questionDiv.appendChild(p);
    questionDiv.appendChild(list);
    root.appendChild(questionDiv);
  });
}

// Check Practice Answers
function checkPractice(practiceNum) {
  const questions = practiceData[practiceNum];
  let correct = 0;

  questions.forEach(q => {
    const sel = document.querySelector(`input[name="${q.id}"]:checked`);
    if (sel && Number(sel.value) === q.answer) correct++;
  });

  const result = document.getElementById(`practice${practiceNum}-result`);
  result.innerHTML = `<strong>结果：${correct}/${questions.length} 正确</strong>`;

  // Show explanations
  const root = document.getElementById(`practice${practiceNum}-root`);
  const questionItems = root.querySelectorAll('.question-item');

  questions.forEach((q, idx) => {
    let explainEl = questionItems[idx].querySelector('.explain');
    if (!explainEl) {
      explainEl = document.createElement('div');
      explainEl.className = 'tip explain';
      explainEl.style.marginTop = '12px';
      questionItems[idx].appendChild(explainEl);
    }

    const sel = document.querySelector(`input[name="${q.id}"]:checked`);
    const isCorrect = sel && Number(sel.value) === q.answer;
    const status = isCorrect ? '✅ 正确' : '❌ 错误';

    explainEl.innerHTML = `<strong>${status}</strong><br>解析：${q.explain}`;
  });
}

// Render Main Quiz
function renderQuiz() {
  const root = document.getElementById('quiz-root');
  root.innerHTML = '';

  quizData.forEach((q, idx) => {
    const questionDiv = document.createElement('div');
    questionDiv.className = 'question-item';
    questionDiv.style.marginBottom = '20px';
    questionDiv.style.padding = '16px';
    questionDiv.style.background = '#1a1f26';
    questionDiv.style.borderRadius = '8px';
    questionDiv.style.border = '1px solid #2a2f3a';

    const h = document.createElement('h3');
    h.textContent = `题目 ${idx + 1}`;
    h.style.margin = '0 0 12px 0';
    h.style.color = '#ffffff';

    const p = document.createElement('p');
    p.textContent = q.text;
    p.style.margin = '0 0 16px 0';
    p.style.lineHeight = '1.6';

    const list = document.createElement('ul');
    list.style.listStyle = 'none';
    list.style.paddingLeft = '0';
    list.style.margin = '0';

    q.options.forEach((opt, i) => {
      const li = document.createElement('li');
      li.style.margin = '8px 0';
      const id = `q${q.id}_opt${i}`;
      li.innerHTML = `<label style="cursor: pointer; display: block; padding: 8px; background: #0f1419; border-radius: 4px; transition: background 0.2s;"><input type="radio" name="q${q.id}" value="${i}" id="${id}" style="margin-right: 12px;"> ${opt}</label>`;

      // Add hover effect
      li.addEventListener('mouseenter', () => {
        li.querySelector('label').style.background = '#1a1f26';
      });
      li.addEventListener('mouseleave', () => {
        li.querySelector('label').style.background = '#0f1419';
      });

      list.appendChild(li);
    });

    questionDiv.appendChild(h);
    questionDiv.appendChild(p);
    questionDiv.appendChild(list);
    root.appendChild(questionDiv);
  });
}

// Check Main Quiz Answers
function checkAnswers() {
  let correct = 0;

  quizData.forEach(q => {
    const sel = document.querySelector(`input[name="q${q.id}"]:checked`);
    if (sel && Number(sel.value) === q.answer) correct++;
  });

  const result = document.getElementById('quiz-result');
  const percentage = Math.round((correct / quizData.length) * 100);
  let grade = '';

  if (percentage >= 90) grade = '🏆 优秀';
  else if (percentage >= 80) grade = '🎉 良好';
  else if (percentage >= 70) grade = '👍 及格';
  else grade = '📚 需要复习';

  result.innerHTML = `<strong>成绩：${correct}/${quizData.length} 正确 (${percentage}%) - ${grade}</strong>`;

  // Show explanations
  const root = document.getElementById('quiz-root');
  const questionItems = root.querySelectorAll('.question-item');

  quizData.forEach((q, idx) => {
    let explainEl = questionItems[idx].querySelector('.explain');
    if (!explainEl) {
      explainEl = document.createElement('div');
      explainEl.className = 'tip explain';
      explainEl.style.marginTop = '16px';
      questionItems[idx].appendChild(explainEl);
    }

    const sel = document.querySelector(`input[name="q${q.id}"]:checked`);
    const isCorrect = sel && Number(sel.value) === q.answer;
    const status = isCorrect ? '✅ 正确' : '❌ 错误';

    explainEl.innerHTML = `<strong>${status}</strong><br>解析：${q.explain}`;
  });
}

// Initialize everything
window.addEventListener('DOMContentLoaded', () => {
  // Render practice sections
  for (let i = 1; i <= 3; i++) {
    renderPractice(i);
  }

  // Render main quiz
  renderQuiz();

  // Add event listeners
  document.getElementById('check-btn').addEventListener('click', checkAnswers);

  document.querySelectorAll('.practice-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const practiceNum = parseInt(e.target.dataset.practice);
      checkPractice(practiceNum);
    });
  });
});

""",
    "index.html": """<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>日本麻将新手入门 · 无役不胡 & 振听</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <h1>日本麻将新手入门</h1>
    <p>一步一步学会打日麻，重点搞懂「无役不胡」与「振听」</p>
  </header>

  <main class="container">
    <nav class="toc">
      <h2>学习路径</h2>
      <ol>
        <li><a href="#step1">认识基本牌与和牌条件</a></li>
        <li><a href="#step2">什么是役（Yaku）与「无役不胡」</a></li>
        <li><a href="#step3">常见一番役详解</a></li>
        <li><a href="#step4">什么是振听（Furiten）</a></li>
        <li><a href="#step5">振听可视化示例</a></li>
        <li><a href="#step6">常见新手误区</a></li>
        <li><a href="#practice">分步练习题</a></li>
        <li><a href="#quiz">综合测验</a></li>
        <li><a href="#ref">参考和进阶</a></li>
      </ol>
    </nav>

    <section id="step1" class="card">
      <h2>一步：认识基本牌与和牌条件</h2>
      <p>
        日本麻将使用 <strong>万/筒/索</strong> 三种数牌与 <strong>风/三元</strong> 字牌，共 34 种牌。和牌通常需要：
      </p>
      <ul>
        <li>由 4 个面子（刻/顺）+ 1 对将 组成的 14 张（七对子/国士等特殊形除外）；</li>
        <li>至少 <strong>1 个役（Yaku）</strong>；</li>
        <li>未处于禁止和牌的状态（例如：<strong>振听</strong>）；</li>
        <li>不违反其他规则（例如：抢杠/立直的前置条件）。</li>
      </ul>
    </section>

    <section id="step2" class="card">
      <h2>二步：什么是役（Yaku）与「无役不胡」</h2>
      <p>
        「役」是和牌的前提条件，没有役就算牌型凑齐也不能和，这就是「<strong>无役不胡</strong>」。
      </p>
      <h3>役的分类</h3>
      <ul>
        <li><strong>门清役</strong>：需要门清（未副露）才能成立，如立直、平和、一盃口</li>
        <li><strong>通用役</strong>：门清副露都可成立，如断幺九、役牌、混一色</li>
        <li><strong>特殊役</strong>：七对子、国士无双等特殊牌型</li>
      </ul>
      <p class="tip">
        门清手可以靠 <strong>立直</strong> 来获得役；副露（吃碰杠）后立直失效，因此需要其它役（如断幺九/役牌/混一色等）。
      </p>
    </section>

    <section id="step3" class="card">
      <h2>三步：常见一番役详解</h2>

      <div class="yaku-section">
        <h3>🎯 立直（リーチ）</h3>
        <p><strong>条件</strong>：门清听牌时宣言立直，支付1000点棒</p>
        <p><strong>价值</strong>：1番 + 可能的里宝牌</p>
        <div class="example">
          <strong>示例</strong>：听两面 [2][3][4] [5][6][7] [8][8] [9][9][9] [东][东] 听1万4万<br>
          宣言立直后，无论自摸还是荣和都有1番
        </div>
      </div>

      <div class="yaku-section">
        <h3>🚫 断幺九（タンヤオ）</h3>
        <p><strong>条件</strong>：手牌不含1、9、字牌（只有2-8的数牌）</p>
        <p><strong>价值</strong>：1番（门清副露均可）</p>
        <div class="tile-example">
          <strong>✅ 正确示例</strong>：<br>
          <code>[2][3][4]万 [5][6][7]筒 [2][3][4]索 [6][7][8]万 [5][5]筒</code>
        </div>
        <div class="tile-example error">
          <strong>❌ 错误示例</strong>：<br>
          <code>[1][2][3]万 [5][6][7]筒 [2][3][4]索 [6][7][8]万 [5][5]筒</code><br>
          <small>含有1万，不符合断幺九</small>
        </div>
      </div>

      <div class="yaku-section">
        <h3>🀄 役牌（ヤクハイ）</h3>
        <p><strong>条件</strong>：场风、门风、白发中任一刻子</p>
        <p><strong>价值</strong>：每个役牌刻子1番</p>
        <div class="example">
          <strong>示例</strong>：东场南家，手中有[南][南][南]刻子 = 门风役牌1番<br>
          如果还有[白][白][白]刻子 = 再加1番，共2番
        </div>
      </div>

      <div class="yaku-section">
        <h3>🎵 平和（ピンフ）</h3>
        <p><strong>条件</strong>：门清 + 四面子皆顺子 + 雀头非役牌 + 两面和牌</p>
        <p><strong>价值</strong>：1番（仅门清）</p>
        <div class="tile-example">
          <strong>✅ 平和示例</strong>：<br>
          <code>[2][3][4]万 [5][6][7]筒 [1][2][3]索 [7][8][9]万 [4][4]筒</code><br>
          听[3][6]筒两面，雀头4筒非役牌
        </div>
        <div class="tile-example error">
          <strong>❌ 非平和</strong>：<br>
          <code>[2][3][4]万 [5][6][7]筒 [1][2][3]索 [7][8][9]万 [东][东]</code><br>
          听[东]单骑，不是两面和牌
        </div>
      </div>

      <div class="yaku-section">
        <h3>🔄 一盃口（イーペーコー）</h3>
        <p><strong>条件</strong>：门清 + 两组完全相同的顺子</p>
        <p><strong>价值</strong>：1番（仅门清）</p>
        <div class="tile-example">
          <strong>示例</strong>：<br>
          <code>[2][3][4]万 [2][3][4]万 [5][6][7]筒 [8][8][8]索 [9][9]筒</code><br>
          两组[2][3][4]万相同
        </div>
      </div>

      <div class="yaku-section">
        <h3>🌈 混一色（ホンイツ）</h3>
        <p><strong>条件</strong>：一种数牌 + 字牌</p>
        <p><strong>价值</strong>：门清3番，副露2番</p>
        <div class="tile-example">
          <strong>示例</strong>：<br>
          <code>[1][2][3]万 [4][5][6]万 [7][8][9]万 [东][东][东] [发][发]</code><br>
          只有万子和字牌
        </div>
      </div>

      <div class="yaku-section">
        <h3>💎 清一色（チンイツ）</h3>
        <p><strong>条件</strong>：仅一种数牌（无字牌）</p>
        <p><strong>价值</strong>：门清6番，副露5番</p>
        <div class="tile-example">
          <strong>示例</strong>：<br>
          <code>[1][1][1]万 [2][3][4]万 [5][6][7]万 [8][9][9]万 [9]万</code><br>
          全部万子
        </div>
      </div>
    </section>

    <section id="step4" class="card">
      <h2>四步：什么是振听（Furiten）</h2>
      <p>
        当你听牌后，如果<strong>你的手牌</strong>中曾经舍过与当前和牌同一张（或同一种）的牌，则你处于<strong>振听</strong>，
        这会导致你 <strong>不能荣和</strong>（吃铳），只能自摸。常见三类振听：
      </p>
      <ol>
        <li><strong>舍张振听</strong>：听牌后，你的舍牌里包含和张（同张或同种，按待牌判定）</li>
        <li><strong>同巡振听</strong>：同一巡内你放过可荣和的牌（选择过不和），到下一巡解除</li>
        <li><strong>立直振听</strong>：立直后出现舍张振听会持续到和牌或流局（无法通过换手解除）</li>
      </ol>
      <p class="tip">「同种」的判定以你的待牌为准：两面听的两张都算和张；双碰听两张刻子也都算。</p>
    </section>

    <section id="step5" class="card">
      <h2>五步：振听可视化示例</h2>

      <div class="furiten-example">
        <h3>📋 舍张振听示例</h3>
        <div class="visual-example">
          <div class="hand-display">
            <strong>你的手牌</strong>：<br>
            <code>[1][2][3]万 [4][5][6]筒 [7][8][9]索 [2][2][2]万 [5]筒</code><br>
            <span class="waiting">听牌：[4][7]筒</span>
          </div>
          <div class="discard-display">
            <strong>你的舍牌河</strong>：<br>
            <div class="discard-river">
              <span class="tile">9万</span>
              <span class="tile">1筒</span>
              <span class="tile danger">4筒</span>
              <span class="tile">东</span>
              <span class="tile">6索</span>
              <span class="tile">白</span>
            </div>
          </div>
          <div class="result error">
            <strong>结果</strong>：你曾舍过4筒，现在听4筒7筒，处于舍张振听<br>
            ❌ 不能荣和4筒　✅ 可以自摸4筒　✅ 可以荣和/自摸7筒
          </div>
        </div>
      </div>

      <div class="furiten-example">
        <h3>🔄 同巡振听示例</h3>
        <div class="visual-example">
          <div class="scenario">
            <strong>情况</strong>：你立直听[3][6]万，上家打出3万
          </div>
          <div class="choice">
            <strong>你的选择</strong>：选择不和（可能想等更大的牌）
          </div>
          <div class="result warning">
            <strong>后果</strong>：本巡内其他人打出3万或6万，你都不能荣和<br>
            下一巡开始，同巡振听自动解除
          </div>
        </div>
      </div>

      <div class="furiten-example">
        <h3>🎯 立直振听示例</h3>
        <div class="visual-example">
          <div class="scenario">
            <strong>情况</strong>：立直后摸到新牌，改变了待牌，但舍牌河中有新的和张
          </div>
          <div class="hand-display">
            <strong>立直时</strong>：听[4][7]万<br>
            <strong>摸牌后</strong>：听[1][4]万，但河里有1万
          </div>
          <div class="result error">
            <strong>结果</strong>：立直振听，直到和牌或流局都不能荣和任何牌<br>
            只能自摸和牌
          </div>
        </div>
      </div>
    </section>

    <section id="step6" class="card">
      <h2>六步：常见新手误区</h2>
      <ul>
        <li>以为凑齐 4 面子 1 将就能和：忽略了「必须有役」</li>
        <li>副露后仍想靠立直拿役：立直仅限门清</li>
        <li>不知道自己振听：不看舍牌就宣称可荣和</li>
        <li>听边张/坎张/单骑仍以为是平和：平和对和牌形有严格要求</li>
        <li>混淆门清役和通用役：一盃口副露后无效，断幺九副露仍有效</li>
        <li>忽视宝牌：有时宝牌能提供关键的番数</li>
      </ul>
    </section>

    <section id="practice" class="card">
      <h2>分步练习题</h2>
      <p>每个练习针对特定知识点，帮你逐步掌握日麻规则。</p>

      <div class="practice-section">
        <h3>🎯 练习1：役的判断</h3>
        <div class="practice-quiz" id="practice1-root"></div>
        <button class="btn practice-btn" data-practice="1">检查答案</button>
        <div class="practice-result" id="practice1-result"></div>
      </div>

      <div class="practice-section">
        <h3>🚫 练习2：振听识别</h3>
        <div class="practice-quiz" id="practice2-root"></div>
        <button class="btn practice-btn" data-practice="2">检查答案</button>
        <div class="practice-result" id="practice2-result"></div>
      </div>

      <div class="practice-section">
        <h3>🎵 练习3：平和判定</h3>
        <div class="practice-quiz" id="practice3-root"></div>
        <button class="btn practice-btn" data-practice="3">检查答案</button>
        <div class="practice-result" id="practice3-result"></div>
      </div>
    </section>

    <section id="quiz" class="card">
      <h2>综合测验</h2>
      <p>选择“能荣和/只能自摸/无法和”，检验你对无役与振听的理解。</p>
      <div class="quiz" id="quiz-root"></div>
      <button id="check-btn" class="btn">提交答案</button>
      <p id="quiz-result" class="result"></p>
    </section>

    <section id="ref" class="card">
      <h2>参考和进阶</h2>
      <ul>
        <li>进阶役种：三色同顺、混一色、对对和、七对子、三暗刻、混老头/清一色等</li>
        <li>规则细节：抢杠、包牌、食替、一发、双立直、海底/河底</li>
        <li>推荐实践：先以门清立直/断幺九/役牌为核心稳步得分</li>
      </ul>
    </section>
  </main>

  <footer class="site-footer">
    <p>Mahjong Tutorial © 2025 · 本站仅用于学习交流</p>
  </footer>

  <script src="app.js"></script>
</body>
</html>

""",
    "styles.css": """/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, "Segoe UI", Roboto, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Helvetica, Arial, sans-serif;
  background: #0e1013;
  color: #e6e6e6;
  line-height: 1.6;
}

/* Header */
.site-header {
  padding: 32px 16px;
  text-align: center;
  background: linear-gradient(135deg, #243949, #517fa4);
}

.site-header h1 {
  margin: 0 0 8px;
  font-size: clamp(24px, 5vw, 32px);
  font-weight: 700;
}

.site-header p {
  margin: 0;
  color: #dfe9f3;
  font-size: clamp(14px, 3vw, 16px);
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 24px auto;
  padding: 0 16px;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 20px;
}

/* Table of Contents */
.toc {
  background: #161a20;
  border: 1px solid #2a2f3a;
  border-radius: 12px;
  padding: 20px;
  position: sticky;
  top: 20px;
  height: fit-content;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.toc h2 {
  margin-top: 0;
  font-size: 18px;
  color: #ffffff;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 8px;
}

.toc ol {
  padding-left: 20px;
}

.toc li {
  margin: 8px 0;
}

.toc a {
  color: #b3c7d6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.toc a:hover {
  color: #3b82f6;
}

/* Cards */
.card {
  background: #161a20;
  border: 1px solid #2a2f3a;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.card h2 {
  margin-top: 0;
  color: #ffffff;
  font-size: 24px;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 8px;
}

.card h3 {
  color: #e6e6e6;
  margin-top: 24px;
  margin-bottom: 12px;
}

/* Yaku Sections */
.yaku-section {
  margin: 20px 0;
  padding: 16px;
  background: #1a1f26;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.yaku-section h3 {
  margin-top: 0;
  color: #ffffff;
  font-size: 18px;
}

/* Tile Examples */
.tile-example {
  background: #0f1419;
  border: 1px solid #2a2f3a;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  font-family: "Courier New", monospace;
}

.tile-example.error {
  border-left: 4px solid #ef4444;
  background: #1f1012;
}

.tile-example code {
  background: transparent;
  color: #fbbf24;
  font-weight: 600;
}

/* Furiten Visualization */
.furiten-example {
  margin: 20px 0;
  padding: 16px;
  background: #1a1f26;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.visual-example {
  margin: 12px 0;
}

.hand-display, .discard-display, .scenario, .choice {
  margin: 12px 0;
  padding: 12px;
  background: #0f1419;
  border-radius: 6px;
}

.discard-river {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.tile {
  background: #2a2f3a;
  color: #e6e6e6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #3a3f4a;
}

.tile.danger {
  background: #ef4444;
  color: white;
  font-weight: bold;
}

.waiting {
  color: #10b981;
  font-weight: 600;
}

.result {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  font-weight: 600;
}

.result.error {
  background: #1f1012;
  border-left: 4px solid #ef4444;
  color: #fca5a5;
}

.result.warning {
  background: #1f1a0f;
  border-left: 4px solid #f59e0b;
  color: #fcd34d;
}

/* Practice Sections */
.practice-section {
  margin: 24px 0;
  padding: 20px;
  background: #1a1f26;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.practice-quiz {
  margin: 16px 0;
}

.practice-result {
  margin-top: 12px;
  font-weight: 600;
}

/* Lists and Pills */
.pill-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-left: 0;
  list-style: none;
}

.pill-list li {
  background: #1f2630;
  border: 1px solid #2f3b4a;
  border-radius: 999px;
  padding: 8px 12px;
  font-size: 14px;
  transition: background 0.2s ease;
}

.pill-list li:hover {
  background: #2a3441;
}

/* Special Boxes */
.tip {
  background: #14221a;
  border-left: 4px solid #3cb371;
  color: #cde7d8;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px 0;
}

.example {
  background: #1a1822;
  border-left: 4px solid #8a7bd1;
  color: #dcd7ff;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px 0;
}

/* Buttons */
.btn {
  background: #3b82f6;
  border: none;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.practice-btn {
  background: #10b981;
}

.practice-btn:hover {
  background: #059669;
}

/* Footer */
.site-footer {
  padding: 32px 16px;
  text-align: center;
  color: #98a6b3;
  font-size: 14px;
  border-top: 1px solid #2a2f3a;
  margin-top: 40px;
}

/* Responsive Design */
@media (max-width: 900px) {
  .container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .toc {
    position: static;
    order: -1;
  }

  .card {
    padding: 20px;
  }

  .yaku-section {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .site-header {
    padding: 24px 12px;
  }

  .container {
    padding: 0 12px;
  }

  .card {
    padding: 16px;
  }

  .tile-example code {
    font-size: 12px;
    word-break: break-all;
  }

  .discard-river {
    gap: 2px;
  }

  .tile {
    padding: 2px 6px;
    font-size: 11px;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
    color: black;
    font-size: 12pt;
    line-height: 1.4;
  }

  .site-header {
    background: none;
    color: black;
    border-bottom: 2px solid black;
  }

  .container {
    display: block;
    max-width: none;
    margin: 0;
    padding: 0;
  }

  .toc {
    background: none;
    border: 1px solid black;
    page-break-after: always;
  }

  .card {
    background: none;
    border: 1px solid black;
    box-shadow: none;
    page-break-inside: avoid;
    margin-bottom: 20pt;
  }

  .card h2 {
    border-bottom: 2px solid black;
  }

  .yaku-section, .furiten-example, .practice-section {
    background: none;
    border: 1px solid black;
    page-break-inside: avoid;
  }

  .tile-example {
    background: #f5f5f5;
    border: 1px solid black;
  }

  .tip, .example {
    background: #f0f0f0;
    border-left: 4px solid black;
    color: black;
  }

  .btn {
    display: none;
  }

  .site-footer {
    border-top: 1px solid black;
    color: black;
  }

  /* Hide interactive elements in print */
  .practice-quiz, .quiz, #quiz-result, .practice-result {
    display: none;
  }
}

/* Dark Theme Enhancements */
@media (prefers-color-scheme: dark) {
  body {
    background: #0a0e13;
  }

  .card {
    background: #151a21;
    border-color: #252a35;
  }

  .toc {
    background: #151a21;
    border-color: #252a35;
  }
}

""",
    "test.html": """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f0f0f0; 
        }
        .test-box { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .success { color: #28a745; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <div class="test-box">
        <h1 class="success">✅ 网络连接测试成功！</h1>
        <p class="info">如果你能看到这个页面，说明局域网连接正常。</p>
        <p><strong>测试时间:</strong> <span id="time"></span></p>
        <p><strong>用户代理:</strong> <span id="ua"></span></p>
        <p><strong>屏幕分辨率:</strong> <span id="screen"></span></p>
        
        <h2>下一步测试:</h2>
        <ul>
            <li><a href="/">返回主页面</a></li>
            <li><a href="/styles.css">测试CSS文件</a></li>
            <li><a href="/app.js">测试JS文件</a></li>
        </ul>
    </div>

    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        document.getElementById('ua').textContent = navigator.userAgent;
        document.getElementById('screen').textContent = screen.width + 'x' + screen.height;
    </script>
</body>
</html>
""",
}

def create_temp_static_dir():
    """创建临时静态文件目录"""
    temp_dir = Path(tempfile.mkdtemp(prefix="mahjong_tutorial_"))
    
    for file_path, content in STATIC_FILES.items():
        full_path = temp_dir / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        if content.startswith("data:base64,"):
            # 二进制文件
            data = base64.b64decode(content[12:])
            with open(full_path, 'wb') as f:
                f.write(data)
        else:
            # 文本文件
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
    
    return temp_dir

def cleanup_temp_dir(temp_dir):
    """清理临时目录"""
    import shutil
    try:
        shutil.rmtree(temp_dir)
    except:
        pass
